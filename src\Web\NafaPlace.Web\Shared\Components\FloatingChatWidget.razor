@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IChatService ChatService
@inject ILogger<FloatingChatWidget> Logger

<!-- Chat Widget Flottant -->
<div class="floating-chat-widget @(isFirstLoad ? "first-load" : "")">
    <!-- Bouton Chat Flottant -->
    <div class="chat-toggle-btn @(isOpen ? "hidden" : "")" @onclick="ToggleChat">
        <i class="bi bi-chat-dots"></i>
        @if (unreadCount > 0)
        {
            <span class="notification-badge">@unreadCount</span>
        }
    </div>

    <!-- Fenêtre de Chat -->
    <div class="chat-window @(isOpen ? "open" : "")">
        <!-- Header du Chat -->
        <div class="chat-header">
            <div class="d-flex align-items-center">
                <div class="chat-avatar">
                    <i class="bi bi-headset"></i>
                </div>
                <div class="chat-info">
                    <h6 class="mb-0">Support NafaPlace</h6>
                    <small class="status-text">
                        <span class="status-dot online"></span>
                        En ligne
                    </small>
                </div>
            </div>
            <div class="chat-actions">
                @if (isAuthenticated)
                {
                    <button class="btn-history" @onclick="OpenChatHistory" title="Historique des conversations">
                        <i class="bi bi-clock-history"></i>
                    </button>
                }
                <button class="btn-minimize" @onclick="MinimizeChat">
                    <i class="bi bi-dash"></i>
                </button>
                <button class="btn-close-chat" @onclick="CloseChat">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        </div>

        <!-- Messages du Chat -->
        <div class="chat-messages" id="chatMessages">
            @if (messages.Any())
            {
                @foreach (var message in messages)
                {
                    <div class="message @(message.IsFromUser ? "user" : "support")">
                        @if (!message.IsFromUser)
                        {
                            <div class="message-avatar">
                                <i class="bi bi-person-circle"></i>
                            </div>
                        }
                        <div class="message-content">
                            <div class="message-bubble">
                                @message.Content
                            </div>
                            <div class="message-time">
                                @message.Timestamp.ToString("HH:mm")
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="bi bi-chat-heart"></i>
                    </div>
                    <h6>Bienvenue !</h6>
                    <p>Comment pouvons-nous vous aider ?</p>
                </div>
            }

            @if (isTyping)
            {
                <div class="message support">
                    <div class="message-avatar">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-bubble typing">
                            <div class="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Suggestions Rapides -->
        @if (!messages.Any() && !isTyping)
        {
            <div class="quick-suggestions">
                @if (!isAuthenticated)
                {
                    <div class="auth-prompt">
                        <p class="text-muted small mb-2">
                            <i class="bi bi-info-circle"></i>
                            Connectez-vous pour un support personnalisé et l'historique de vos conversations
                        </p>
                        <button class="btn btn-outline-primary btn-sm mb-2" @onclick="RedirectToLogin">
                            <i class="bi bi-person-plus"></i> Se connecter
                        </button>
                    </div>
                }

                @foreach (var suggestion in quickSuggestions)
                {
                    <button class="suggestion-btn" @onclick="() => SendQuickMessage(suggestion)">
                        @suggestion
                    </button>
                }

                @if (isAuthenticated && currentConversationId.HasValue)
                {
                    <button class="suggestion-btn agent-request-btn" @onclick="@(() => RequestHumanAgent("Demande d'assistance personnalisée"))">
                        <i class="bi bi-person-headset"></i> Parler à un agent
                    </button>
                }
            </div>
        }

        <!-- Zone de Saisie -->
        <div class="chat-input">
            <div class="input-group">
                <input type="text" class="form-control"
                       placeholder="Tapez votre message..."
                       @bind="currentMessage"
                       @onkeypress="HandleKeyPress"
                       @oninput="HandleInput"
                       @onfocus="HandleInputFocus"
                       @onblur="HandleInputBlur"
                       disabled="@isLoading">
                <button class="btn btn-primary" @onclick="SendMessage" 
                        disabled="@(isLoading || string.IsNullOrWhiteSpace(currentMessage))">
                    @if (isLoading)
                    {
                        <span class="spinner-border spinner-border-sm"></span>
                    }
                    else
                    {
                        <i class="bi bi-send"></i>
                    }
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .floating-chat-widget {
        position: fixed !important;
        bottom: 20px !important;
        right: 20px !important;
        z-index: 999999 !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        pointer-events: auto !important;
        display: block !important;
        visibility: visible !important;
    }

    .chat-toggle-btn {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #E73C30, #F96302);
        border-radius: 50%;
        display: flex !important;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3);
        transition: all 0.3s ease;
        position: relative;
        animation: pulse 2s infinite;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .chat-toggle-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 25px rgba(231, 60, 48, 0.4);
    }

    .chat-toggle-btn.hidden {
        opacity: 0;
        transform: scale(0);
        pointer-events: none;
    }

    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    @@keyframes pulse {
        0% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3); }
        50% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.6); }
        100% { box-shadow: 0 4px 20px rgba(231, 60, 48, 0.3); }
    }

    .chat-window {
        width: 320px;
        height: 450px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        display: flex;
        flex-direction: column;
        transform: translateY(100%) scale(0.8);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        pointer-events: none;
        overflow: hidden;
        position: absolute;
        bottom: 80px;
        right: 0;
    }

    .chat-window.open {
        transform: translateY(0) scale(1);
        opacity: 1;
        pointer-events: all;
    }

    .chat-header {
        background: linear-gradient(135deg, #E73C30, #F96302);
        color: white;
        padding: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .chat-avatar {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 18px;
    }

    .chat-info h6 {
        margin: 0;
        font-weight: 600;
    }

    .status-text {
        opacity: 0.9;
        font-size: 12px;
    }

    .status-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .status-dot.online {
        background: #28a745;
        animation: blink 1.5s infinite;
    }

    @@keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }

    .chat-actions {
        display: flex;
        gap: 5px;
    }

    .btn-minimize, .btn-close-chat, .btn-history {
        background: none;
        border: none;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background 0.2s;
    }

    .btn-minimize:hover, .btn-close-chat:hover, .btn-history:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    .chat-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        background: #f8f9fa;
    }

    .message {
        display: flex;
        margin-bottom: 15px;
        align-items: flex-end;
    }

    .message.user {
        justify-content: flex-end;
    }

    .message-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #6c757d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        font-size: 14px;
        flex-shrink: 0;
    }

    .message-content {
        max-width: 70%;
    }

    .message-bubble {
        padding: 10px 15px;
        border-radius: 18px;
        font-size: 14px;
        line-height: 1.4;
    }

    .message.support .message-bubble {
        background: white;
        color: #333;
        border-bottom-left-radius: 5px;
    }

    .message.user .message-bubble {
        background: linear-gradient(135deg, #E73C30, #F96302);
        color: white;
        border-bottom-right-radius: 5px;
    }

    .message-time {
        font-size: 11px;
        color: #6c757d;
        margin-top: 5px;
        text-align: right;
    }

    .message.support .message-time {
        text-align: left;
    }

    .welcome-message {
        text-align: center;
        padding: 30px 20px;
        color: #6c757d;
    }

    .welcome-icon {
        font-size: 48px;
        color: #E73C30;
        margin-bottom: 15px;
    }

    .typing-indicator {
        display: flex;
        gap: 3px;
        padding: 8px 0;
    }

    .typing-indicator span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #6c757d;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
    .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

    @@keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }

    .quick-suggestions {
        padding: 10px 15px;
        border-top: 1px solid #e9ecef;
        background: white;
    }

    .suggestion-btn {
        display: block;
        width: 100%;
        background: none;
        border: 1px solid #e9ecef;
        padding: 8px 12px;
        margin-bottom: 5px;
        border-radius: 20px;
        font-size: 13px;
        color: #6c757d;
        cursor: pointer;
        transition: all 0.2s;
        text-align: left;
    }

    .suggestion-btn:hover {
        background: #f8f9fa;
        border-color: #E73C30;
        color: #E73C30;
    }

    .auth-prompt {
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 10px;
        text-align: center;
    }

    .auth-prompt .btn {
        font-size: 12px;
        padding: 5px 12px;
    }

    .agent-request-btn {
        background: linear-gradient(135deg, #28a745, #20c997) !important;
        color: white !important;
        border-color: #28a745 !important;
        font-weight: 500;
    }

    .agent-request-btn:hover {
        background: linear-gradient(135deg, #218838, #1ea080) !important;
        border-color: #1e7e34 !important;
        color: white !important;
        transform: translateY(-1px);
    }

    .chat-input {
        padding: 15px;
        border-top: 1px solid #e9ecef;
        background: white;
    }

    .chat-input .form-control {
        border: 1px solid #e9ecef;
        border-radius: 25px;
        padding: 10px 15px;
        font-size: 14px;
    }

    .chat-input .btn {
        border-radius: 50%;
        width: 40px;
        height: 40px;
        padding: 0;
        margin-left: 8px;
    }

    /* Responsive */
    @@media (max-width: 768px) {
        .floating-chat-widget {
            bottom: 15px !important;
            right: 15px !important;
        }

        .chat-window {
            width: calc(100vw - 30px);
            height: calc(100vh - 100px);
            max-width: 320px;
            max-height: 450px;
            bottom: 80px;
            right: 0;
        }

        .chat-toggle-btn {
            width: 50px !important;
            height: 50px !important;
            font-size: 20px !important;
        }
    }

    /* Assurer que le chat reste au-dessus de tout */
    .floating-chat-widget * {
        box-sizing: border-box;
    }

    /* Éviter les conflits avec d'autres éléments */
    body:has(.floating-chat-widget) {
        position: relative;
    }

    /* Forcer le positionnement même en cas de conflits */
    .floating-chat-widget {
        will-change: transform;
    }

    /* Animation d'entrée pour attirer l'attention */
    @@keyframes chatEntrance {
        0% {
            transform: translateY(100px) scale(0.3);
            opacity: 0;
        }
        50% {
            transform: translateY(-10px) scale(1.05);
        }
        100% {
            transform: translateY(0) scale(1);
            opacity: 1;
        }
    }

    .floating-chat-widget.first-load .chat-toggle-btn {
        animation: chatEntrance 0.6s ease-out;
    }
</style>

@code {
    private bool isOpen = false;
    private bool isMinimized = false;
    private bool isLoading = false;
    private bool isTyping = false;
    private string currentMessage = "";
    private int unreadCount = 0;
    private List<ChatMessage> messages = new();
    private Timer? autoShowTimer;
    private bool isFirstLoad = true;
    private int? currentConversationId = null;
    private string sessionId = Guid.NewGuid().ToString();
    private bool isAuthenticated = false;
    private string? userId = null;
    private bool isSignalRConnected = false;
    private DotNetObjectReference<FloatingChatWidget>? dotNetRef;
    
    private List<string> quickSuggestions = new()
    {
        "Suivi de commande",
        "Retour produit", 
        "Problème de paiement",
        "Livraison",
        "Garantie"
    };

    protected override async Task OnInitializedAsync()
    {
        // Vérifier l'authentification
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;

        if (isAuthenticated)
        {
            userId = authState.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            // Restaurer l'état du chat si l'utilisateur vient de se connecter
            await RestoreChatStateAfterLogin();

            // Charger les conversations existantes
            await LoadUserConversations();
        }
        else
        {
            // Pour les utilisateurs anonymes, créer un ID de session
            userId = $"guest_{sessionId}";
        }

        // Démarrer le timer pour réapparition automatique
        StartAutoShowTimer();

        // Message de bienvenue après un délai
        await Task.Delay(2000);
        if (!isOpen)
        {
            unreadCount = 1;
            StateHasChanged();
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Créer la référence .NET pour les callbacks JavaScript
            dotNetRef = DotNetObjectReference.Create(this);

            // Initialiser SignalR si l'utilisateur est authentifié
            if (isAuthenticated)
            {
                await InitializeSignalRAsync();
            }

            // Retirer la classe first-load après l'animation
            await Task.Delay(1000);
            isFirstLoad = false;
            StateHasChanged();
        }
    }

    private void StartAutoShowTimer()
    {
        // Réapparaître après 3 minutes si fermé définitivement
        autoShowTimer = new Timer(async _ =>
        {
            if (!isOpen && !isMinimized)
            {
                unreadCount++;
                await InvokeAsync(StateHasChanged);
            }
        }, null, TimeSpan.FromMinutes(3), TimeSpan.FromMinutes(3));
    }

    private void ToggleChat()
    {
        isOpen = !isOpen;
        isMinimized = false;

        if (isOpen)
        {
            unreadCount = 0;

            // Ajouter message de bienvenue si premier ouverture
            if (!messages.Any())
            {
                _ = Task.Run(async () =>
                {
                    await Task.Delay(1000);

                    if (isAuthenticated)
                    {
                        await AddSupportMessage("Bonjour ! Comment puis-je vous aider aujourd'hui ? Je peux vous mettre en relation avec un agent si nécessaire.", "Assistant");
                    }
                    else
                    {
                        await AddSupportMessage("Bonjour ! Je suis votre assistant virtuel. Comment puis-je vous aider ? Pour un support personnalisé, veuillez vous connecter.", "Assistant");
                    }
                });
            }
        }

        StateHasChanged();
    }

    private void MinimizeChat()
    {
        isOpen = false;
        isMinimized = true;
        StateHasChanged();
    }

    private void CloseChat()
    {
        isOpen = false;
        isMinimized = false;
        StateHasChanged();
    }

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !string.IsNullOrWhiteSpace(currentMessage))
        {
            await SendMessage();
        }
    }

    private Timer? typingTimer;
    private bool isUserTyping = false;

    private async Task HandleInput(ChangeEventArgs e)
    {
        if (!isUserTyping)
        {
            isUserTyping = true;
            await SendTypingIndicatorAsync(true);
        }

        // Réinitialiser le timer
        typingTimer?.Dispose();
        typingTimer = new Timer(async _ =>
        {
            isUserTyping = false;
            await SendTypingIndicatorAsync(false);
        }, null, 2000, Timeout.Infinite);
    }

    private async Task HandleInputFocus(FocusEventArgs e)
    {
        // Rejoindre le groupe de conversation si pas encore fait
        await JoinConversationGroupAsync();
    }

    private async Task HandleInputBlur(FocusEventArgs e)
    {
        // Arrêter l'indicateur de frappe
        if (isUserTyping)
        {
            isUserTyping = false;
            await SendTypingIndicatorAsync(false);
        }
        typingTimer?.Dispose();
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(currentMessage) || isLoading) return;

        var message = currentMessage.Trim();
        currentMessage = "";
        isLoading = true;

        try
        {
            // Ajouter le message de l'utilisateur localement
            messages.Add(new ChatMessage
            {
                Content = message,
                IsFromUser = true,
                Timestamp = DateTime.Now,
                SenderName = isAuthenticated ? "Vous" : "Invité"
            });

            StateHasChanged();
            await ScrollToBottom();

            if (isAuthenticated && currentConversationId.HasValue)
            {
                // Envoyer via SignalR si connecté, sinon via l'API
                if (isSignalRConnected)
                {
                    await JSRuntime.InvokeVoidAsync("chatSignalR.sendMessage", currentConversationId.Value, message);
                }
                else
                {
                    await ChatService.SendMessageAsync(currentConversationId.Value, message);
                }
            }
            else
            {
                // Utiliser le chatbot pour les utilisateurs non authentifiés ou sans conversation
                isTyping = true;
                StateHasChanged();

                var response = await ChatService.SendChatbotMessageAsync(message, sessionId);

                isTyping = false;
                await AddSupportMessage(response.Message, "Assistant");

                // Si le chatbot recommande un agent humain
                if (response.RequiresHumanAgent && isAuthenticated)
                {
                    await RequestHumanAgent("Demande d'assistance personnalisée");
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'envoi du message");

            // Fallback vers réponse automatique
            isTyping = true;
            StateHasChanged();
            await Task.Delay(1000);
            isTyping = false;

            await AddSupportMessage("Désolé, je rencontre des difficultés techniques. Veuillez réessayer.", "Assistant");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SendQuickMessage(string message)
    {
        // Si l'utilisateur est authentifié et n'a pas de conversation active, en créer une
        if (isAuthenticated && !currentConversationId.HasValue)
        {
            await StartNewConversation(message);
        }

        currentMessage = message;
        await SendMessage();
    }

    private async Task AddSupportMessage(string content, string senderName)
    {
        messages.Add(new ChatMessage
        {
            Content = content,
            IsFromUser = false,
            Timestamp = DateTime.Now,
            SenderName = senderName
        });

        StateHasChanged();
        await ScrollToBottom();
    }

    private string GenerateResponse(string userMessage)
    {
        var lowerMessage = userMessage.ToLower();
        
        if (lowerMessage.Contains("commande") || lowerMessage.Contains("suivi"))
            return "Pour suivre votre commande, rendez-vous dans votre espace client > Mes Commandes.";
        
        if (lowerMessage.Contains("retour") || lowerMessage.Contains("remboursement"))
            return "Vous pouvez retourner un produit dans les 14 jours. Allez dans 'Mes Commandes' > 'Retourner'.";
        
        if (lowerMessage.Contains("paiement") || lowerMessage.Contains("carte"))
            return "Nous acceptons Visa/Mastercard, Orange Money, et paiement à la livraison.";
        
        if (lowerMessage.Contains("livraison") || lowerMessage.Contains("délai"))
            return "Délais de livraison : 2-5 jours ouvrables. Gratuit dès 50.000 GNF.";
        
        if (lowerMessage.Contains("bonjour") || lowerMessage.Contains("salut"))
            return "Bonjour ! Je suis ravi de vous aider. Quelle est votre question ?";
        
        return "Merci pour votre message. Un agent va vous répondre rapidement !";
    }

    private async Task ScrollToBottom()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("scrollToBottom", "chatMessages");
        }
        catch { }
    }

    private async Task LoadUserConversations()
    {
        try
        {
            var conversations = await ChatService.GetUserConversationsAsync();
            var activeConversation = conversations.FirstOrDefault(c => c.Status == "Open" || c.Status == "InProgress");

            if (activeConversation != null)
            {
                currentConversationId = activeConversation.Id;
                await LoadConversationMessages(activeConversation.Id);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des conversations");
        }
    }

    private async Task LoadConversationMessages(int conversationId)
    {
        try
        {
            var apiMessages = await ChatService.GetConversationMessagesAsync(conversationId);
            messages.Clear();

            foreach (var msg in apiMessages.OrderBy(m => m.Timestamp))
            {
                messages.Add(new ChatMessage
                {
                    Content = msg.Content,
                    IsFromUser = msg.SenderType == "Customer",
                    Timestamp = msg.Timestamp,
                    SenderName = msg.SenderName
                });
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des messages");
        }
    }

    private async Task StartNewConversation(string initialMessage)
    {
        try
        {
            if (!isAuthenticated) return;

            var conversationId = await ChatService.StartConversationAsync("Support Chat", initialMessage);

            if (conversationId > 0)
            {
                currentConversationId = conversationId;

                // Rejoindre le groupe SignalR pour cette conversation
                await JoinConversationGroupAsync();

                Logger.LogInformation("Nouvelle conversation créée: {ConversationId}", conversationId);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la création de conversation");
        }
    }

    private async Task RequestHumanAgent(string reason)
    {
        try
        {
            if (!currentConversationId.HasValue) return;

            var success = await ChatService.RequestHumanAgentAsync(currentConversationId.Value, reason);

            if (success)
            {
                await AddSupportMessage("Votre demande a été transmise à un agent. Vous serez contacté sous peu.", "Système");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la demande d'agent");
        }
    }

    private async Task InitializeSignalRAsync()
    {
        try
        {
            var token = await ChatService.GetAuthTokenAsync();
            var chatApiUrl = "http://localhost:5007"; // TODO: Get from configuration

            // Enregistrer la référence Blazor pour les callbacks
            await JSRuntime.InvokeVoidAsync("chatSignalR.registerBlazorWidget", dotNetRef);

            // Initialiser la connexion SignalR
            await JSRuntime.InvokeVoidAsync("chatSignalR.init", chatApiUrl, token);

            isSignalRConnected = true;
            Logger.LogInformation("SignalR initialisé avec succès");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'initialisation de SignalR");
        }
    }

    [JSInvokable]
    public async Task OnMessageReceived(object messageData)
    {
        try
        {
            // Traiter le message reçu via SignalR
            var message = System.Text.Json.JsonSerializer.Deserialize<dynamic>(messageData.ToString() ?? "{}");

            await AddSupportMessage(message.GetProperty("content").GetString() ?? "",
                                  message.GetProperty("senderName").GetString() ?? "Agent");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du traitement du message reçu");
        }
    }

    [JSInvokable]
    public async Task OnTypingIndicator(object typingData)
    {
        try
        {
            var data = System.Text.Json.JsonSerializer.Deserialize<dynamic>(typingData.ToString() ?? "{}");
            var isTypingValue = data.GetProperty("isTyping").GetBoolean();

            if (isTypingValue != isTyping)
            {
                isTyping = isTypingValue;
                await InvokeAsync(StateHasChanged);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du traitement de l'indicateur de frappe");
        }
    }

    [JSInvokable]
    public async Task OnAgentAssigned(object agentData)
    {
        try
        {
            var data = System.Text.Json.JsonSerializer.Deserialize<dynamic>(agentData.ToString() ?? "{}");
            var agentName = data.GetProperty("agentName").GetString() ?? "Agent";

            await AddSupportMessage($"🎯 {agentName} a rejoint la conversation et va vous aider.", "Système");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du traitement de l'assignation d'agent");
        }
    }

    private async Task JoinConversationGroupAsync()
    {
        if (isSignalRConnected && currentConversationId.HasValue)
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("chatSignalR.joinGroup", currentConversationId.Value);
                Logger.LogInformation("Rejoint le groupe de conversation {ConversationId}", currentConversationId.Value);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Erreur lors de la connexion au groupe de conversation");
            }
        }
    }

    private async Task SendTypingIndicatorAsync(bool typing)
    {
        if (isSignalRConnected && currentConversationId.HasValue)
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("chatSignalR.sendTypingIndicator", currentConversationId.Value, typing);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Erreur lors de l'envoi de l'indicateur de frappe");
            }
        }
    }

    private async Task RedirectToLogin()
    {
        try
        {
            // Sauvegarder l'état actuel du chat pour les utilisateurs anonymes
            if (!isAuthenticated && messages.Any())
            {
                var chatState = new
                {
                    Messages = messages.Select(m => new { m.Content, m.IsFromUser, m.Timestamp, m.SenderName }).ToList(),
                    SessionId = sessionId
                };

                await JSRuntime.InvokeVoidAsync("localStorage.setItem", "tempChatState",
                    System.Text.Json.JsonSerializer.Serialize(chatState));
            }

            // Rediriger vers la page de connexion
            var currentUrl = await JSRuntime.InvokeAsync<string>("eval", "window.location.href");
            var loginUrl = $"/account/login?returnUrl={Uri.EscapeDataString(currentUrl)}";

            await JSRuntime.InvokeVoidAsync("window.location.assign", loginUrl);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la redirection vers la connexion");
        }
    }

    private async Task RestoreChatStateAfterLogin()
    {
        try
        {
            var chatStateJson = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "tempChatState");

            if (!string.IsNullOrEmpty(chatStateJson))
            {
                var chatState = System.Text.Json.JsonSerializer.Deserialize<dynamic>(chatStateJson);

                // Restaurer les messages
                // Note: Ici on pourrait créer une nouvelle conversation avec l'historique

                // Nettoyer le stockage temporaire
                await JSRuntime.InvokeVoidAsync("localStorage.removeItem", "tempChatState");

                Logger.LogInformation("État du chat restauré après connexion");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de la restauration de l'état du chat");
        }
    }

    private async Task OpenChatHistory()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("window.open", "/account/chat-history", "_blank");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'ouverture de l'historique");
        }
    }

    public void Dispose()
    {
        autoShowTimer?.Dispose();
        typingTimer?.Dispose();
        dotNetRef?.Dispose();

        // Déconnecter SignalR
        if (isSignalRConnected)
        {
            try
            {
                JSRuntime.InvokeVoidAsync("chatSignalR.disconnect");
            }
            catch { }
        }
    }

    public class ChatMessage
    {
        public string Content { get; set; } = "";
        public bool IsFromUser { get; set; }
        public DateTime Timestamp { get; set; }
        public string SenderName { get; set; } = "";
    }
}
