using NafaPlace.Delivery.Application.DTOs;

namespace NafaPlace.Delivery.Application.Services;

public interface IDeliveryRepository
{
    // Zone Management
    Task<List<DeliveryZoneDto>> GetDeliveryZonesAsync();
    Task<DeliveryZoneDto?> GetDeliveryZoneByIdAsync(int zoneId);
    Task<DeliveryZoneDto?> GetDeliveryZoneByCodeAsync(string code);
    Task<DeliveryZoneDto> CreateDeliveryZoneAsync(CreateDeliveryZoneRequest request);
    Task<DeliveryZoneDto> UpdateDeliveryZoneAsync(int id, UpdateDeliveryZoneRequest request);
    Task<bool> DeleteDeliveryZoneAsync(int id);

    // Delivery Order Management
    Task<DeliveryOrderDto> CreateDeliveryOrderAsync(CreateDeliveryOrderRequest request);
    Task<DeliveryOrderDto?> GetDeliveryOrderByIdAsync(int id);

    // Tracking Management
    Task<List<DeliveryTrackingDto>> GetDeliveryTrackingByTrackingNumberAsync(string trackingNumber);
    Task<bool> UpdateDeliveryStatusAsync(int deliveryOrderId, UpdateDeliveryStatusRequest request);

    // Carrier Management
    Task<List<CarrierDto>> GetCarriersAsync(bool activeOnly = true);
    Task<CarrierDto?> GetCarrierByIdAsync(int carrierId);
    Task<CarrierDto?> GetCarrierByCodeAsync(string code);
    Task<CarrierDto> CreateCarrierAsync(CreateCarrierRequest request);
    Task<CarrierDto> UpdateCarrierAsync(int id, UpdateCarrierRequest request);
    Task<bool> DeleteCarrierAsync(int id);

    // Carrier Zone Management
    Task<List<CarrierZoneDto>> GetCarrierZonesAsync(int? carrierId = null, int? zoneId = null);
    Task<CarrierZoneDto> CreateCarrierZoneAsync(CreateCarrierZoneRequest request);
    Task<CarrierZoneDto> UpdateCarrierZoneAsync(int id, UpdateCarrierZoneRequest request);
    Task<bool> DeleteCarrierZoneAsync(int id);
}
