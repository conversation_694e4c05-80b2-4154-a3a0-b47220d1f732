# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers csproj et restaurer les dépendances
COPY ["src/Services/Inventory/NafaPlace.Inventory.API/NafaPlace.Inventory.API.csproj", "Services/Inventory/NafaPlace.Inventory.API/"]
COPY ["src/Services/Inventory/NafaPlace.Inventory.Application/NafaPlace.Inventory.Application.csproj", "Services/Inventory/NafaPlace.Inventory.Application/"]
COPY ["src/Services/Inventory/NafaPlace.Inventory.Domain/NafaPlace.Inventory.Domain.csproj", "Services/Inventory/NafaPlace.Inventory.Domain/"]
COPY ["src/Services/Inventory/NafaPlace.Inventory.Infrastructure/NafaPlace.Inventory.Infrastructure.csproj", "Services/Inventory/NafaPlace.Inventory.Infrastructure/"]
RUN dotnet restore "Services/Inventory/NafaPlace.Inventory.API/NafaPlace.Inventory.API.csproj"

# Copier le reste des fichiers et construire
COPY ["src/Services/Inventory/", "Services/Inventory/"]
WORKDIR "/src/Services/Inventory/NafaPlace.Inventory.API"
RUN dotnet build "NafaPlace.Inventory.API.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "NafaPlace.Inventory.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Créer un utilisateur non-root pour la sécurité
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

EXPOSE 80
ENTRYPOINT ["dotnet", "NafaPlace.Inventory.API.dll"]
