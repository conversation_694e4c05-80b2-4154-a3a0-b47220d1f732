using NafaPlace.Chat.Domain.Enums;

namespace NafaPlace.Chat.Application.DTOs;

public class SupportTicketDto
{
    public int Id { get; set; }
    public string TicketNumber { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TicketStatus Status { get; set; }
    public TicketPriority Priority { get; set; }
    public TicketCategory Category { get; set; }
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public string? AssignedAgentId { get; set; }
    public string? AssignedAgentName { get; set; }
    public string? DepartmentId { get; set; }
    public string? DepartmentName { get; set; }
    public List<string> Tags { get; set; } = new();
    public List<TicketAttachmentDto> Attachments { get; set; } = new();
    public Dictionary<string, object> CustomFields { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? ResolvedAt { get; set; }
    public DateTime? ClosedAt { get; set; }
    public DateTime? DueDate { get; set; }
    public TimeSpan? ResponseTime { get; set; }
    public TimeSpan? ResolutionTime { get; set; }
    public int? SatisfactionRating { get; set; }
    public string? SatisfactionComment { get; set; }
    public string? Resolution { get; set; }
    public int MessageCount { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public bool HasUnreadMessages { get; set; }
    public List<TicketHistoryDto> History { get; set; } = new();
}

public class TicketAttachmentDto
{
    public int Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileUrl { get; set; } = string.Empty;
    public string? ThumbnailUrl { get; set; }
    public DateTime UploadedAt { get; set; }
    public string UploadedBy { get; set; } = string.Empty;
}

public class TicketHistoryDto
{
    public int Id { get; set; }
    public int TicketId { get; set; }
    public TicketHistoryAction Action { get; set; }
    public string Description { get; set; } = string.Empty;
    public string? OldValue { get; set; }
    public string? NewValue { get; set; }
    public string PerformedBy { get; set; } = string.Empty;
    public string PerformedByName { get; set; } = string.Empty;
    public DateTime PerformedAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class CreateTicketDto
{
    public string Subject { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TicketPriority Priority { get; set; } = TicketPriority.Normal;
    public TicketCategory Category { get; set; }
    public string CustomerId { get; set; } = string.Empty;
    public string? DepartmentId { get; set; }
    public string? AssignedAgentId { get; set; }
    public List<string> Tags { get; set; } = new();
    public List<TicketAttachmentDto> Attachments { get; set; } = new();
    public Dictionary<string, object> CustomFields { get; set; } = new();
    public DateTime? DueDate { get; set; }
    public string? Source { get; set; } = "Web";
}

public class UpdateTicketDto
{
    public int TicketId { get; set; }
    public string? Subject { get; set; }
    public string? Description { get; set; }
    public TicketStatus? Status { get; set; }
    public TicketPriority? Priority { get; set; }
    public TicketCategory? Category { get; set; }
    public string? AssignedAgentId { get; set; }
    public string? DepartmentId { get; set; }
    public List<string>? Tags { get; set; }
    public Dictionary<string, object>? CustomFields { get; set; }
    public DateTime? DueDate { get; set; }
    public string? Resolution { get; set; }
    public string UpdatedBy { get; set; } = string.Empty;
    public string? UpdateReason { get; set; }
}

public class TicketMessageDto
{
    public int Id { get; set; }
    public int TicketId { get; set; }
    public string SenderId { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public MessageType Type { get; set; }
    public bool IsInternal { get; set; }
    public List<TicketAttachmentDto> Attachments { get; set; } = new();
    public DateTime SentAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public bool IsRead { get; set; }
    public bool IsFromAgent { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class TicketFilterDto
{
    public TicketStatus? Status { get; set; }
    public TicketPriority? Priority { get; set; }
    public TicketCategory? Category { get; set; }
    public string? AssignedAgentId { get; set; }
    public string? DepartmentId { get; set; }
    public string? CustomerId { get; set; }
    public List<string>? Tags { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public DateTime? CreatedBefore { get; set; }
    public DateTime? DueBefore { get; set; }
    public bool? IsOverdue { get; set; }
    public bool? HasUnreadMessages { get; set; }
    public string? SearchQuery { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "CreatedAt";
    public bool SortDescending { get; set; } = true;
}

public class TicketStatsDto
{
    public int TotalTickets { get; set; }
    public int OpenTickets { get; set; }
    public int InProgressTickets { get; set; }
    public int PendingTickets { get; set; }
    public int ResolvedTickets { get; set; }
    public int ClosedTickets { get; set; }
    public int OverdueTickets { get; set; }
    public double AverageResponseTime { get; set; }
    public double AverageResolutionTime { get; set; }
    public double FirstResponseRate { get; set; }
    public double ResolutionRate { get; set; }
    public double CustomerSatisfactionScore { get; set; }
    public Dictionary<TicketPriority, int> TicketsByPriority { get; set; } = new();
    public Dictionary<TicketCategory, int> TicketsByCategory { get; set; } = new();
    public Dictionary<string, int> TicketsByAgent { get; set; } = new();
    public Dictionary<string, int> TicketsByDepartment { get; set; } = new();
    public Dictionary<string, int> TicketsByDay { get; set; } = new();
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class AgentTicketPerformanceDto
{
    public string AgentId { get; set; } = string.Empty;
    public string AgentName { get; set; } = string.Empty;
    public int TotalTickets { get; set; }
    public int OpenTickets { get; set; }
    public int ResolvedTickets { get; set; }
    public int OverdueTickets { get; set; }
    public double AverageResponseTime { get; set; }
    public double AverageResolutionTime { get; set; }
    public double FirstResponseRate { get; set; }
    public double ResolutionRate { get; set; }
    public double CustomerSatisfactionScore { get; set; }
    public int SatisfactionRatings { get; set; }
    public Dictionary<TicketPriority, int> TicketsByPriority { get; set; } = new();
    public Dictionary<TicketCategory, int> TicketsByCategory { get; set; } = new();
    public List<string> TopTags { get; set; } = new();
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class TicketEscalationRuleDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<EscalationConditionDto> Conditions { get; set; } = new();
    public List<EscalationActionDto> Actions { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public int Priority { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class EscalationConditionDto
{
    public string Field { get; set; } = string.Empty; // Priority, Category, ResponseTime, etc.
    public string Operator { get; set; } = string.Empty; // equals, greater_than, less_than, etc.
    public string Value { get; set; } = string.Empty;
    public TimeSpan? TimeThreshold { get; set; }
}

public class EscalationActionDto
{
    public EscalationActionType Type { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class TicketTemplateDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TicketCategory Category { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public TicketPriority DefaultPriority { get; set; } = TicketPriority.Normal;
    public string? DefaultDepartmentId { get; set; }
    public List<string> DefaultTags { get; set; } = new();
    public Dictionary<string, object> DefaultCustomFields { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public int UsageCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class TicketSLADto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<SLAConditionDto> Conditions { get; set; } = new();
    public TimeSpan FirstResponseTime { get; set; }
    public TimeSpan ResolutionTime { get; set; }
    public bool IsActive { get; set; } = true;
    public int Priority { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class SLAConditionDto
{
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}

public class TicketWorkflowDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<WorkflowStepDto> Steps { get; set; } = new();
    public List<WorkflowTriggerDto> Triggers { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class WorkflowStepDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public WorkflowActionType ActionType { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public List<WorkflowConditionDto> Conditions { get; set; } = new();
    public int Order { get; set; }
}

public class WorkflowTriggerDto
{
    public WorkflowTriggerType Type { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class WorkflowConditionDto
{
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}

// Enums
public enum TicketStatus
{
    New,
    Open,
    InProgress,
    Pending,
    Resolved,
    Closed,
    Cancelled,
    Reopened
}

public enum TicketPriority
{
    Low,
    Normal,
    High,
    Urgent,
    Critical
}

public enum TicketCategory
{
    General,
    Technical,
    Billing,
    Account,
    Product,
    Order,
    Shipping,
    Refund,
    Bug,
    FeatureRequest,
    Complaint,
    Compliment
}

public enum TicketHistoryAction
{
    Created,
    Updated,
    StatusChanged,
    PriorityChanged,
    Assigned,
    Unassigned,
    Transferred,
    MessageAdded,
    AttachmentAdded,
    TagAdded,
    TagRemoved,
    Escalated,
    Resolved,
    Closed,
    Reopened,
    SatisfactionRated
}

public enum EscalationActionType
{
    ChangePriority,
    AssignToAgent,
    TransferToDepartment,
    SendNotification,
    SendEmail,
    CreateTask,
    AddTag,
    UpdateCustomField
}

public enum WorkflowActionType
{
    UpdateField,
    SendNotification,
    SendEmail,
    AssignToAgent,
    CreateTask,
    AddTag,
    RunScript,
    CallWebhook
}

public enum WorkflowTriggerType
{
    TicketCreated,
    TicketUpdated,
    StatusChanged,
    PriorityChanged,
    MessageAdded,
    TimeElapsed,
    SLABreach
}
