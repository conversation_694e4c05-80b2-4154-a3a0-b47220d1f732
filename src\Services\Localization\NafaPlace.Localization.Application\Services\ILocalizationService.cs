using NafaPlace.Localization.Application.DTOs;

namespace NafaPlace.Localization.Application.Services;

public interface ILocalizationService
{
    // Gestion des langues
    Task<List<LanguageDto>> GetSupportedLanguagesAsync();
    Task<LanguageDto?> GetLanguageAsync(string languageCode);
    Task<bool> AddLanguageAsync(LanguageDto language);
    Task<bool> UpdateLanguageAsync(LanguageDto language);
    Task<bool> RemoveLanguageAsync(string languageCode);
    Task<bool> SetDefaultLanguageAsync(string languageCode);
    Task<string> GetDefaultLanguageAsync();
    Task<bool> IsLanguageSupportedAsync(string languageCode);
    
    // Gestion des traductions
    Task<string> GetTranslationAsync(string key, string languageCode, string? defaultValue = null, string? @namespace = null);
    Task<Dictionary<string, string>> GetTranslationsAsync(List<string> keys, string languageCode, string? @namespace = null);
    Task<Dictionary<string, string>> GetAllTranslationsAsync(string languageCode, string? @namespace = null);
    Task<bool> SetTranslationAsync(string key, string languageCode, string value, string? @namespace = null, TranslationSource source = TranslationSource.Manual);
    Task<bool> SetTranslationsAsync(Dictionary<string, string> translations, string languageCode, string? @namespace = null, TranslationSource source = TranslationSource.Manual);
    Task<bool> DeleteTranslationAsync(string key, string languageCode, string? @namespace = null);
    Task<bool> DeleteTranslationsAsync(List<string> keys, string languageCode, string? @namespace = null);
    
    // Traduction automatique
    Task<string> AutoTranslateAsync(string text, string sourceLanguage, string targetLanguage, AutoTranslationProvider? provider = null);
    Task<Dictionary<string, string>> AutoTranslateBatchAsync(Dictionary<string, string> texts, string sourceLanguage, string targetLanguage, AutoTranslationProvider? provider = null);
    Task<bool> AutoTranslateKeyAsync(string key, string sourceLanguage, List<string> targetLanguages, string? @namespace = null);
    Task<int> AutoTranslateMissingAsync(string sourceLanguage, string targetLanguage, string? @namespace = null);
    Task<List<AutoTranslationDto>> GetAutoTranslationsAsync(string? languageCode = null, bool? isReviewed = null);
    Task<bool> ApproveAutoTranslationAsync(int autoTranslationId, string approvedBy);
    Task<bool> RejectAutoTranslationAsync(int autoTranslationId, string rejectedBy, string reason);
    
    // Mémoire de traduction
    Task<List<TranslationMemoryDto>> SearchTranslationMemoryAsync(string sourceText, string sourceLanguage, string targetLanguage, double minSimilarity = 0.7);
    Task<bool> AddToTranslationMemoryAsync(string sourceText, string targetText, string sourceLanguage, string targetLanguage, string? context = null);
    Task<bool> UpdateTranslationMemoryAsync(int memoryId, string targetText);
    Task<bool> DeleteFromTranslationMemoryAsync(int memoryId);
    Task<int> CleanupTranslationMemoryAsync(int minUsageCount = 1, int daysOld = 365);
    Task<Dictionary<string, object>> GetTranslationMemoryStatsAsync();
    
    // Gestion des lots de traduction
    Task<int> CreateTranslationBatchAsync(TranslationBatchDto batch);
    Task<List<TranslationBatchDto>> GetTranslationBatchesAsync(BatchStatus? status = null);
    Task<TranslationBatchDto?> GetTranslationBatchAsync(int batchId);
    Task<bool> UpdateTranslationBatchAsync(TranslationBatchDto batch);
    Task<bool> ProcessTranslationBatchAsync(int batchId);
    Task<bool> CancelTranslationBatchAsync(int batchId);
    Task<bool> DeleteTranslationBatchAsync(int batchId);
    
    // Projets de traduction
    Task<int> CreateTranslationProjectAsync(TranslationProjectDto project);
    Task<List<TranslationProjectDto>> GetTranslationProjectsAsync(ProjectStatus? status = null);
    Task<TranslationProjectDto?> GetTranslationProjectAsync(int projectId);
    Task<bool> UpdateTranslationProjectAsync(TranslationProjectDto project);
    Task<bool> DeleteTranslationProjectAsync(int projectId);
    Task<Dictionary<string, double>> GetProjectProgressAsync(int projectId);
    Task<bool> AssignTranslatorToProjectAsync(int projectId, string translatorId, List<string> languages);
    Task<bool> RemoveTranslatorFromProjectAsync(int projectId, string translatorId);
    
    // Workflows de traduction
    Task<int> CreateTranslationWorkflowAsync(LocalizationWorkflowDto workflow);
    Task<List<LocalizationWorkflowDto>> GetTranslationWorkflowsAsync(WorkflowStatus? status = null);
    Task<LocalizationWorkflowDto?> GetTranslationWorkflowAsync(int workflowId);
    Task<bool> UpdateTranslationWorkflowAsync(LocalizationWorkflowDto workflow);
    Task<bool> DeleteTranslationWorkflowAsync(int workflowId);
    Task<bool> ExecuteWorkflowAsync(int workflowId, List<string> keys, string sourceLanguage, List<string> targetLanguages);
    Task<bool> SetDefaultWorkflowAsync(int workflowId);
    
    // Qualité des traductions
    Task<TranslationQualityDto> AssessTranslationQualityAsync(int translationId);
    Task<List<TranslationQualityDto>> GetQualityAssessmentsAsync(string? languageCode = null, QualityScore? minScore = null);
    Task<bool> UpdateQualityAssessmentAsync(TranslationQualityDto qualityAssessment);
    Task<List<TranslationDto>> GetLowQualityTranslationsAsync(QualityScore maxScore = QualityScore.Fair);
    Task<Dictionary<string, object>> GetQualityMetricsAsync(string? languageCode = null, DateTime? startDate = null, DateTime? endDate = null);
    Task<bool> ValidateTranslationAsync(string key, string languageCode, string? @namespace = null);
    Task<List<string>> ValidateAllTranslationsAsync(string languageCode, string? @namespace = null);
    
    // Recherche et filtrage
    Task<List<TranslationDto>> SearchTranslationsAsync(string query, string? languageCode = null, string? @namespace = null, TranslationStatus? status = null);
    Task<List<TranslationDto>> GetTranslationsByStatusAsync(TranslationStatus status, string? languageCode = null);
    Task<List<TranslationDto>> GetOutdatedTranslationsAsync(string languageCode, DateTime since);
    Task<List<TranslationDto>> GetMissingTranslationsAsync(string sourceLanguage, string targetLanguage, string? @namespace = null);
    Task<List<string>> GetUnusedTranslationKeysAsync(string? @namespace = null);
    Task<Dictionary<string, int>> GetTranslationUsageStatsAsync(string? @namespace = null);
    
    // Espaces de noms et organisation
    Task<List<string>> GetNamespacesAsync();
    Task<bool> CreateNamespaceAsync(string @namespace, string description);
    Task<bool> DeleteNamespaceAsync(string @namespace);
    Task<bool> RenameNamespaceAsync(string oldNamespace, string newNamespace);
    Task<Dictionary<string, int>> GetTranslationCountByNamespaceAsync(string languageCode);
    Task<bool> MoveTranslationsToNamespaceAsync(List<string> keys, string sourceNamespace, string targetNamespace);
    
    // Import/Export
    Task<byte[]> ExportTranslationsAsync(TranslationExportDto exportRequest);
    Task<bool> ImportTranslationsAsync(Stream fileStream, TranslationImportDto importRequest);
    Task<List<string>> ValidateImportFileAsync(Stream fileStream, string format);
    Task<Dictionary<string, object>> GetImportPreviewAsync(Stream fileStream, TranslationImportDto importRequest);
    Task<bool> ExportTranslationMemoryAsync(string sourceLanguage, string targetLanguage, string format = "tmx");
    Task<bool> ImportTranslationMemoryAsync(Stream fileStream, string format = "tmx");
    
    // Localisation de contenu
    Task<string> LocalizeContentAsync(string content, string languageCode, Dictionary<string, object>? parameters = null);
    Task<Dictionary<string, string>> LocalizeContentBatchAsync(Dictionary<string, string> content, string languageCode, Dictionary<string, object>? parameters = null);
    Task<string> FormatNumberAsync(double number, string languageCode, string? format = null);
    Task<string> FormatCurrencyAsync(decimal amount, string languageCode, string? currencyCode = null);
    Task<string> FormatDateAsync(DateTime date, string languageCode, string? format = null);
    Task<string> FormatDateTimeAsync(DateTime dateTime, string languageCode, string? format = null);
    
    // Gestion des devises
    Task<List<CurrencyDto>> GetSupportedCurrenciesAsync();
    Task<CurrencyDto?> GetCurrencyAsync(string currencyCode);
    Task<bool> UpdateCurrencyAsync(CurrencyDto currency);
    Task<decimal> ConvertCurrencyAsync(decimal amount, string fromCurrency, string toCurrency);
    Task<Dictionary<string, double>> GetExchangeRatesAsync(string baseCurrency);
    Task<bool> UpdateExchangeRatesAsync();
    
    // Analytics et rapports
    Task<LocalizationAnalyticsDto> GetLocalizationAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, double>> GetTranslationCompletionRatesAsync();
    Task<Dictionary<string, int>> GetTranslationActivityAsync(DateTime startDate, DateTime endDate);
    Task<List<PopularTranslationDto>> GetPopularTranslationsAsync(int limit = 50);
    Task<Dictionary<string, object>> GetTranslatorPerformanceAsync(string translatorId, DateTime? startDate = null, DateTime? endDate = null);
    Task<byte[]> GenerateLocalizationReportAsync(string reportType, Dictionary<string, object> parameters, string format = "pdf");
    
    // Configuration
    Task<LocalizationConfigDto> GetLocalizationConfigAsync();
    Task<bool> UpdateLocalizationConfigAsync(LocalizationConfigDto config);
    Task<Dictionary<string, object>> GetProviderSettingsAsync(AutoTranslationProvider provider);
    Task<bool> UpdateProviderSettingsAsync(AutoTranslationProvider provider, Dictionary<string, object> settings);
    Task<bool> TestProviderConnectionAsync(AutoTranslationProvider provider);
    
    // Cache et performance
    Task<bool> RefreshTranslationCacheAsync(string? languageCode = null, string? @namespace = null);
    Task<bool> WarmupCacheAsync(List<string> languageCodes);
    Task<Dictionary<string, object>> GetCacheStatsAsync();
    Task<bool> ClearCacheAsync(string? pattern = null);
    Task<bool> PreloadTranslationsAsync(string languageCode, string? @namespace = null);
    
    // Versioning et historique
    Task<List<TranslationDto>> GetTranslationHistoryAsync(string key, string languageCode, string? @namespace = null);
    Task<bool> RevertTranslationAsync(string key, string languageCode, int version, string? @namespace = null);
    Task<bool> CreateTranslationSnapshotAsync(string name, string? description = null);
    Task<List<Dictionary<string, object>>> GetTranslationSnapshotsAsync();
    Task<bool> RestoreFromSnapshotAsync(int snapshotId);
    Task<bool> DeleteSnapshotAsync(int snapshotId);
    
    // Collaboration et workflow
    Task<bool> AssignTranslationAsync(string key, string languageCode, string translatorId, string? @namespace = null);
    Task<bool> SubmitTranslationForReviewAsync(string key, string languageCode, string? @namespace = null);
    Task<bool> ApproveTranslationAsync(string key, string languageCode, string approvedBy, string? @namespace = null);
    Task<bool> RejectTranslationAsync(string key, string languageCode, string rejectedBy, string reason, string? @namespace = null);
    Task<List<TranslationDto>> GetPendingTranslationsAsync(string? translatorId = null, string? languageCode = null);
    Task<List<TranslationDto>> GetTranslationsForReviewAsync(string? reviewerId = null, string? languageCode = null);
    
    // Notifications et alertes
    Task<bool> NotifyTranslatorsAsync(string message, List<string>? translatorIds = null, List<string>? languageCodes = null);
    Task<bool> SetupTranslationAlertsAsync(string userId, Dictionary<string, object> alertSettings);
    Task<List<Dictionary<string, object>>> GetTranslationAlertsAsync(string userId);
    Task<bool> SendCompletionNotificationAsync(int projectId);
    Task<bool> SendQualityAlertAsync(string languageCode, QualityScore threshold);
    
    // API et intégrations
    Task<Dictionary<string, object>> GetAPIUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<bool> ValidateAPIKeyAsync(string apiKey);
    Task<List<string>> GetAPIEndpointsAsync();
    Task<bool> SyncWithExternalSystemAsync(string systemId, Dictionary<string, object> config);
    Task<bool> ProcessWebhookAsync(string eventType, Dictionary<string, object> data);
    
    // Maintenance et optimisation
    Task<bool> OptimizeTranslationDatabaseAsync();
    Task<int> CleanupOldTranslationsAsync(int daysToKeep = 365);
    Task<bool> RebuildSearchIndexAsync();
    Task<Dictionary<string, bool>> GetServiceHealthAsync();
    Task<bool> TestLocalizationServiceAsync();
    Task<List<string>> GetSystemLanguagesAsync();
    
    // Détection automatique de langue
    Task<string> DetectLanguageAsync(string text);
    Task<Dictionary<string, double>> DetectLanguageWithConfidenceAsync(string text);
    Task<bool> IsTextInLanguageAsync(string text, string languageCode, double threshold = 0.8);
    Task<List<string>> GetSuggestedLanguagesAsync(string text, int maxSuggestions = 3);
    
    // Pluralisation et contexte
    Task<string> GetPluralTranslationAsync(string key, string languageCode, int count, string? @namespace = null);
    Task<bool> SetPluralTranslationAsync(string key, string languageCode, Dictionary<string, string> pluralForms, string? @namespace = null);
    Task<string> GetContextualTranslationAsync(string key, string languageCode, string context, string? @namespace = null);
    Task<List<string>> GetTranslationContextsAsync(string key, string languageCode, string? @namespace = null);
    
    // Suggestions et recommandations
    Task<List<string>> GetTranslationSuggestionsAsync(string key, string languageCode, string? @namespace = null);
    Task<List<string>> GetSimilarTranslationsAsync(string text, string languageCode, int maxResults = 10);
    Task<Dictionary<string, string>> GetTranslationRecommendationsAsync(string sourceText, string sourceLanguage, string targetLanguage);
    Task<bool> LearnFromUserFeedbackAsync(string key, string languageCode, string feedback, bool isPositive);
    
    // Sécurité et audit
    Task<List<Dictionary<string, object>>> GetTranslationAuditLogAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<bool> LogTranslationActionAsync(string action, string userId, Dictionary<string, object> details);
    Task<bool> ValidateTranslationPermissionsAsync(string userId, string action, string? languageCode = null);
    Task<List<string>> GetUserPermissionsAsync(string userId);
    Task<bool> EncryptSensitiveTranslationsAsync(List<string> keys, string? @namespace = null);
}
