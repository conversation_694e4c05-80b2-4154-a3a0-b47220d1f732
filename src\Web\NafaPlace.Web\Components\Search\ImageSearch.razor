@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@inject IAdvancedSearchService AdvancedSearchService
@inject IJSRuntime JSRuntime

<div class="image-search-component">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-camera me-2"></i>Recherche par image
            </h5>
            <small class="text-muted">Trouvez des produits similaires en téléchargeant une image</small>
        </div>
        
        <div class="card-body">
            @if (!isSearching && searchResults.Count == 0)
            {
                <!-- Zone de téléchargement -->
                <div class="upload-zone @(isDragOver ? "drag-over" : "")" 
                     @ondrop="OnDrop" 
                     @ondragover="OnDragOver" 
                     @ondragenter="OnDragEnter" 
                     @ondragleave="OnDragLeave"
                     @onclick="TriggerFileInput">
                    
                    <input type="file" 
                           @ref="fileInput" 
                           accept="image/*" 
                           style="display: none;" 
                           @onchange="OnFileSelected" />
                    
                    @if (selectedImageUrl == null)
                    {
                        <div class="upload-content text-center">
                            <i class="bi bi-cloud-upload display-1 text-muted mb-3"></i>
                            <h6>Glissez-déposez une image ici</h6>
                            <p class="text-muted mb-3">ou cliquez pour sélectionner un fichier</p>
                            <button type="button" class="btn btn-outline-primary">
                                <i class="bi bi-folder2-open me-2"></i>Choisir une image
                            </button>
                            <div class="mt-3">
                                <small class="text-muted">
                                    Formats supportés: JPG, PNG, GIF (max 5MB)
                                </small>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="selected-image text-center">
                            <img src="@selectedImageUrl" alt="Image sélectionnée" class="preview-image mb-3" />
                            <div class="image-actions">
                                <button type="button" class="btn btn-primary me-2" @onclick="SearchByImage">
                                    <i class="bi bi-search me-2"></i>Rechercher des produits similaires
                                </button>
                                <button type="button" class="btn btn-outline-secondary" @onclick="ClearImage">
                                    <i class="bi bi-x-circle me-2"></i>Changer d'image
                                </button>
                            </div>
                        </div>
                    }
                </div>
                
                <!-- URL d'image alternative -->
                <div class="mt-3">
                    <div class="row align-items-end">
                        <div class="col">
                            <label class="form-label">Ou entrez l'URL d'une image</label>
                            <input type="url" 
                                   class="form-control" 
                                   placeholder="https://exemple.com/image.jpg"
                                   @bind="imageUrl" 
                                   @bind:event="oninput" />
                        </div>
                        <div class="col-auto">
                            <button type="button" 
                                    class="btn btn-outline-primary" 
                                    @onclick="LoadImageFromUrl"
                                    disabled="@(string.IsNullOrEmpty(imageUrl))">
                                <i class="bi bi-link me-2"></i>Charger
                            </button>
                        </div>
                    </div>
                </div>
            }
            
            @if (isSearching)
            {
                <!-- État de chargement -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Recherche en cours...</span>
                    </div>
                    <h6>Analyse de l'image en cours...</h6>
                    <p class="text-muted">Nous recherchons des produits similaires</p>
                </div>
            }
            
            @if (searchResults.Any())
            {
                <!-- Résultats de recherche -->
                <div class="search-results">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Produits similaires trouvés (@searchResults.Count)</h6>
                        <button type="button" class="btn btn-sm btn-outline-secondary" @onclick="StartNewSearch">
                            <i class="bi bi-arrow-left me-1"></i>Nouvelle recherche
                        </button>
                    </div>
                    
                    @if (selectedImageUrl != null)
                    {
                        <div class="original-image mb-3">
                            <small class="text-muted">Image de référence:</small>
                            <img src="@selectedImageUrl" alt="Image de référence" class="reference-image" />
                        </div>
                    }
                    
                    <div class="row g-3">
                        @foreach (var product in searchResults)
                        {
                            <div class="col-md-6 col-lg-4">
                                <div class="card product-result h-100">
                                    <div class="position-relative">
                                        @if (product.Images.Any())
                                        {
                                            <img src="@product.Images.First().Url" 
                                                 alt="@product.Name" 
                                                 class="card-img-top" 
                                                 style="height: 150px; object-fit: cover;" />
                                        }
                                        <div class="similarity-badge position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-success">
                                                @(new Random().Next(75, 95))% similaire
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <a href="/catalog/product/@product.Id" class="text-decoration-none">
                                                @product.Name
                                            </a>
                                        </h6>
                                        <p class="card-text text-muted small">
                                            @(product.Description.Length > 60 ? 
                                              product.Description.Substring(0, 60) + "..." : 
                                              product.Description)
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="h6 text-primary mb-0">
                                                @product.Price.ToString("N0") GNF
                                            </span>
                                            <small class="text-muted">@product.CategoryName</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
            
            @if (hasSearched && !searchResults.Any() && !isSearching)
            {
                <!-- Aucun résultat -->
                <div class="no-results text-center py-4">
                    <i class="bi bi-search display-4 text-muted mb-3"></i>
                    <h6>Aucun produit similaire trouvé</h6>
                    <p class="text-muted">
                        Essayez avec une autre image ou utilisez la recherche textuelle.
                    </p>
                    <button type="button" class="btn btn-primary" @onclick="StartNewSearch">
                        <i class="bi bi-arrow-clockwise me-2"></i>Essayer une autre image
                    </button>
                </div>
            }
        </div>
    </div>
</div>

<style>
    .upload-zone {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .upload-zone:hover,
    .upload-zone.drag-over {
        border-color: #0d6efd;
        background: #e7f3ff;
    }

    .preview-image {
        max-width: 200px;
        max-height: 200px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .reference-image {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 4px;
        border: 2px solid #dee2e6;
    }

    .product-result {
        transition: transform 0.2s ease;
    }

    .product-result:hover {
        transform: translateY(-2px);
    }

    .similarity-badge {
        font-size: 0.75rem;
    }

    .upload-content {
        padding: 1rem;
    }

    .image-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        flex-wrap: wrap;
    }
</style>

@code {
    [Parameter] public EventCallback<List<ProductDto>> OnSearchCompleted { get; set; }

    private ElementReference fileInput;
    private string? selectedImageUrl;
    private string imageUrl = "";
    private bool isDragOver = false;
    private bool isSearching = false;
    private bool hasSearched = false;
    private List<ProductDto> searchResults = new();

    private async Task TriggerFileInput()
    {
        await JSRuntime.InvokeVoidAsync("document.getElementById", fileInput).AsTask();
        await JSRuntime.InvokeVoidAsync("eval", $"document.querySelector('input[type=file]').click()");
    }

    private async Task OnFileSelected(ChangeEventArgs e)
    {
        var files = e.Value?.ToString();
        if (!string.IsNullOrEmpty(files))
        {
            // Simuler le chargement de l'image
            // Dans une vraie implémentation, on uploadrait le fichier
            selectedImageUrl = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=";
            StateHasChanged();
        }
    }

    private async Task LoadImageFromUrl()
    {
        if (!string.IsNullOrEmpty(imageUrl))
        {
            selectedImageUrl = imageUrl;
            imageUrl = "";
            StateHasChanged();
        }
    }

    private void OnDragOver(DragEventArgs e)
    {
        isDragOver = true;
    }

    private void OnDragEnter(DragEventArgs e)
    {
        isDragOver = true;
    }

    private void OnDragLeave(DragEventArgs e)
    {
        isDragOver = false;
    }

    private async Task OnDrop(DragEventArgs e)
    {
        isDragOver = false;
        
        // Simuler le traitement du fichier déposé
        selectedImageUrl = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=";
        StateHasChanged();
    }

    private async Task SearchByImage()
    {
        if (string.IsNullOrEmpty(selectedImageUrl))
            return;

        isSearching = true;
        hasSearched = false;
        searchResults.Clear();
        StateHasChanged();

        try
        {
            // Simuler un délai de recherche
            await Task.Delay(2000);
            
            searchResults = await AdvancedSearchService.SearchByImageAsync(selectedImageUrl, 12);
            hasSearched = true;
            
            if (OnSearchCompleted.HasDelegate)
            {
                await OnSearchCompleted.InvokeAsync(searchResults);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la recherche par image: {ex.Message}");
            hasSearched = true;
        }
        finally
        {
            isSearching = false;
            StateHasChanged();
        }
    }

    private void ClearImage()
    {
        selectedImageUrl = null;
        imageUrl = "";
        StateHasChanged();
    }

    private void StartNewSearch()
    {
        selectedImageUrl = null;
        imageUrl = "";
        searchResults.Clear();
        hasSearched = false;
        isSearching = false;
        StateHasChanged();
    }
}
