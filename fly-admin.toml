# Configuration pour NafaPlace Admin Portal (Blazor WebAssembly)
app = "nafaplace-admin-portal"
primary_region = "cdg"

[build]
  dockerfile = "Dockerfile.admin"

[env]
  ASPNETCORE_ENVIRONMENT = "Staging"
  TZ = "Africa/Conakry"
  LANG = "fr_GN.UTF-8"

[http_service]
  internal_port = 80
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 256
