using System.Collections.Generic;

namespace NafaPlace.Catalog.Application.DTOs.Product
{
    public class ProductSearchResultDto
    {
        public PagedResultDto<ProductDto> Products { get; set; } = new();
        public SearchMetadataDto Metadata { get; set; } = new();
        public List<SearchSuggestionDto> Suggestions { get; set; } = new();
        public List<SearchFacetDto> Facets { get; set; } = new();
        public List<ProductDto> SimilarProducts { get; set; } = new();
    }

    public class SearchMetadataDto
    {
        public string? OriginalQuery { get; set; }
        public string? CorrectedQuery { get; set; }
        public int TotalResults { get; set; }
        public double SearchTimeMs { get; set; }
        public bool HasSpellingSuggestions { get; set; }
        public List<string> AppliedFilters { get; set; } = new();
        public Dictionary<string, object> SearchStats { get; set; } = new();
    }

    public class SearchSuggestionDto
    {
        public string Text { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // product, category, brand, etc.
        public int ResultCount { get; set; }
        public double Relevance { get; set; }
    }

    public class SearchFacetDto
    {
        public string Name { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // range, list, boolean
        public List<SearchFacetValueDto> Values { get; set; } = new();
    }

    public class SearchFacetValueDto
    {
        public string Value { get; set; } = string.Empty;
        public string DisplayValue { get; set; } = string.Empty;
        public int Count { get; set; }
        public bool IsSelected { get; set; }
    }
}
