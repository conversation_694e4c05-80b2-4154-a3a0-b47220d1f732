using Microsoft.EntityFrameworkCore;
using NafaPlace.Analytics.Domain.Entities;

namespace NafaPlace.Analytics.Infrastructure.Data;

public class AnalyticsDbContext : DbContext
{
    public AnalyticsDbContext(DbContextOptions<AnalyticsDbContext> options) : base(options)
    {
    }

    public DbSet<SalesMetric> SalesMetrics { get; set; }
    public DbSet<ProductMetric> ProductMetrics { get; set; }
    public DbSet<UserMetric> UserMetrics { get; set; }
    public DbSet<AnalyticsEvent> AnalyticsEvents { get; set; }
    public DbSet<Report> Reports { get; set; }
    public DbSet<KPI> KPIs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configuration SalesMetrics
        modelBuilder.Entity<SalesMetric>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Date).IsRequired();
            entity.Property(e => e.Period).HasMaxLength(20).IsRequired();
            entity.Property(e => e.TotalSales).HasColumnType("decimal(18,2)");
            entity.Property(e => e.AverageOrderValue).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Revenue).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Profit).HasColumnType("decimal(18,2)");
            entity.Property(e => e.SellerId).HasMaxLength(100);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.Date, e.Period });
            entity.HasIndex(e => e.SellerId);
        });

        // Configuration ProductMetrics
        modelBuilder.Entity<ProductMetric>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProductId).IsRequired();
            entity.Property(e => e.Date).IsRequired();
            entity.Property(e => e.Revenue).HasColumnType("decimal(18,2)").HasDefaultValue(0);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.ProductId, e.Date });
        });

        // Configuration UserMetrics
        modelBuilder.Entity<UserMetric>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Date).IsRequired();
            entity.Property(e => e.TotalSpent).HasColumnType("decimal(18,2)").HasDefaultValue(0);
            entity.Property(e => e.AverageOrderValue).HasColumnType("decimal(18,2)").HasDefaultValue(0);
            entity.Property(e => e.DeviceType).HasMaxLength(50);
            entity.Property(e => e.Browser).HasMaxLength(100);
            entity.Property(e => e.Location).HasMaxLength(100);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.UserId, e.Date });
        });

        // Configuration AnalyticsEvents
        modelBuilder.Entity<AnalyticsEvent>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.EventType).HasMaxLength(100).IsRequired();
            entity.Property(e => e.UserId).HasMaxLength(100);
            entity.Property(e => e.SessionId).HasMaxLength(100);
            entity.Property(e => e.EntityType).HasMaxLength(50);
            entity.Property(e => e.EntityId).HasMaxLength(100);
            entity.Property(e => e.Properties).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.IpAddress).HasMaxLength(45);
            entity.Property(e => e.UserAgent).HasMaxLength(500);
            entity.Property(e => e.Referrer).HasMaxLength(500);
            entity.Property(e => e.Page).HasMaxLength(500);
            entity.Property(e => e.Timestamp).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.EventType, e.Timestamp });
            entity.HasIndex(e => new { e.UserId, e.Timestamp });
            entity.HasIndex(e => e.SessionId);
        });

        // Configuration Reports
        modelBuilder.Entity<Report>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Type).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Parameters).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.Schedule).HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.CreatedBy).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Recipients).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.Type, e.IsActive });
        });

        // Configuration KPIs
        modelBuilder.Entity<KPI>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Category).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Value).HasColumnType("decimal(18,4)");
            entity.Property(e => e.PreviousValue).HasColumnType("decimal(18,4)");
            entity.Property(e => e.Target).HasColumnType("decimal(18,4)");
            entity.Property(e => e.Unit).HasMaxLength(20);
            entity.Property(e => e.Period).HasMaxLength(20).IsRequired();
            entity.Property(e => e.Date).IsRequired();
            entity.Property(e => e.Trend).HasMaxLength(20);
            entity.Property(e => e.SellerId).HasMaxLength(100);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.Name, e.Date });
            entity.HasIndex(e => e.SellerId);
        });
    }
}
