@page "/"
@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Models.Cart
@using NafaPlace.Web.Models.Wishlist
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using NafaPlace.Web.Components.Reviews
@inject NavigationManager NavigationManager
@inject IProductService ProductService
@inject ICategoryService CategoryService
@inject ICartService CartService
@inject IWishlistService WishlistService
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<!-- Hero Section -->
<section class="hero mb-4 mb-md-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-12 col-md-6 animate-fade-in text-center text-md-start">
                <h1 class="display-4 fw-bold mb-3 mb-md-4">Bienvenue sur NafaPlace</h1>
                <p class="lead mb-3 mb-md-4">La plus grande marketplace africaine avec des milliers de produits et vendeurs de confiance.</p>
                <div class="d-grid d-md-block gap-2">
                    <button class="btn btn-primary btn-lg me-md-2 mb-2 mb-md-0" @onclick="NavigateToProducts">Découvrir</button>
                    <button class="btn btn-outline-light btn-lg" @onclick="NavigateToAbout">En savoir plus</button>
                </div>
            </div>
            <div class="col-md-6 d-none d-md-block">
                <!-- Placeholder pour une image ou slider -->
            </div>
        </div>
    </div>
</section>

<!-- Catégories populaires -->
<section class="categories-section mb-4 mb-md-5">
    <div class="container">
        <h2 class="text-center mb-3 mb-md-4">Catégories populaires</h2>
        <div class="row g-3 g-md-4">
            @if (categories == null)
            {
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else
            {
                <!-- Bouton Toutes les Catégories -->
                <div class="col-6 col-md-4 col-lg-2">
                    <div class="category-card text-center h-100" @onclick="NavigateToAllCategories">
                        <div class="d-flex align-items-center justify-content-center" style="height: 60px; background-color: #f8f9fa; border-radius: 8px;">
                            <i class="fas fa-th-large fa-lg text-primary"></i>
                        </div>
                        <div class="p-2 p-md-3">
                            <h6 class="mb-0 small">Toutes les Catégories</h6>
                        </div>
                    </div>
                </div>

                @foreach (var category in categories.Take(5))
                {
                    <div class="col-6 col-md-4 col-lg-2">
                        <div class="category-card text-center h-100" @onclick="() => NavigateToCategory(category.Id)">
                            <img src="@CategoryService.GetImageUrl(category)" alt="@category.Name" class="img-fluid" style="height: 60px; width: 100%; object-fit: cover; border-radius: 8px;">
                            <div class="p-2 p-md-3">
                                <h6 class="mb-0 small">@category.Name</h6>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</section>

<!-- Offres du jour -->
<section class="products-section mb-4 mb-md-5">
    <div class="container">
        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3 mb-md-4">
            <div class="mb-2 mb-md-0">
                <h2 class="mb-1">Offres du jour</h2>
                <p class="section-subtitle mb-0">Découvrez nos meilleures offres sélectionnées pour vous</p>
            </div>
            <a href="/catalog" class="btn-view-all align-self-end align-self-md-center">Voir tout</a>
        </div>
        <div class="row g-3 g-md-4">
            @if (featuredProducts == null)
            {
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else
            {
                @foreach (var product in featuredProducts.Take(8))
                {
                    <div class="col-6 col-md-4 col-lg-3">
                        <div class="card product-card h-100">
                            @if (product.DiscountPercentage > 0)
                            {
                                <span class="badge rounded-pill badge-sale px-3 py-2">-@product.DiscountPercentage%</span>
                            }
                            <a href="/catalog/products/@product.Id">
                                <img src="@(product.Images.Any() ? ProductService.GetImageUrl(product.Images.First()) : "/images/placeholder.png")" 
                                     class="card-img-top" alt="@product.Name">
                            </a>
                            <div class="card-body">
                                <h5 class="card-title"><a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a></h5>
                                <div class="d-flex mb-2">
                                    <StarRating Rating="product.Rating" ShowRatingText="false" CssClass="me-2" />
                                    <small>(@product.ReviewCount)</small>
                                </div>
                                <p class="card-text">@(product.ShortDescription ?? product.Description?.Substring(0, Math.Min(product.Description.Length, 100)) ?? "")</p>
                                <div class="mb-3">
                                    <span class="product-price">@product.Price.ToString("N0") GNF</span>
                                    @if (product.OldPrice.HasValue && product.OldPrice > product.Price)
                                    {
                                        <span class="product-old-price ms-2">@product.OldPrice.Value.ToString("N0") GNF</span>
                                    }
                                </div>
                                <div class="d-flex">
                                    <button class="btn btn-primary me-2" @onclick="() => AddToCart(product.Id)">
                                        <i class="fas fa-shopping-cart me-1"></i> Ajouter
                                    </button>
                                    <NafaPlace.Web.Shared.Components.WishlistButton Product="product" />
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</section>

<!-- Bannière promotionnelle -->
<section class="promo-banner">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8 text-center text-md-start">
                <h3>Livraison gratuite pour votre première commande !</h3>
                <p>Utilisez le code promo <strong>BIENVENUE</strong> pour bénéficier de la livraison gratuite sur votre première commande.</p>
                <button class="btn btn-outline-light" @onclick="NavigateToProducts">
                    <i class="bi bi-truck me-2"></i>En profiter maintenant
                </button>
            </div>
            <div class="col-md-4 d-none d-md-block text-end">
                <i class="bi bi-truck" style="font-size: 4rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Nouveaux arrivages -->
<section class="products-section">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2>Nouveaux arrivages</h2>
                <p class="section-subtitle">Les derniers produits ajoutés par nos vendeurs</p>
            </div>
            <a href="/catalog" class="btn-view-all">Voir tout</a>
        </div>
        <div class="row g-4">
            @if (newProducts == null)
            {
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else
            {
                @foreach (var product in newProducts.Take(8))
                {
                    <div class="col-6 col-md-3">
                        <div class="card product-card h-100">
                            <span class="badge-new">NOUVEAU</span>
                            <a href="/catalog/products/@product.Id">
                                <img src="@(product.Images.Any() ? ProductService.GetImageUrl(product.Images.First()) : "/images/placeholder.png")" 
                                     class="card-img-top" alt="@product.Name">
                            </a>
                            <div class="card-body">
                                <h5 class="card-title"><a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a></h5>
                                <div class="d-flex mb-2">
                                    <StarRating Rating="product.Rating" ShowRatingText="false" CssClass="me-2" />
                                    <small>(@product.ReviewCount)</small>
                                </div>
                                <p class="card-text">@(product.ShortDescription ?? product.Description?.Substring(0, Math.Min(product.Description.Length, 100)) ?? "")</p>
                                <div class="mb-3">
                                    <span class="product-price">@product.Price.ToString("N0") GNF</span>
                                </div>
                                <div class="d-flex">
                                    <button class="btn btn-primary me-2" @onclick="() => AddToCart(product.Id)">
                                        <i class="fas fa-shopping-cart me-1"></i> Ajouter
                                    </button>
                                    <NafaPlace.Web.Shared.Components.WishlistButton Product="product" />
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</section>

<!-- Recommandations IA -->
<section class="py-5">
    <div class="container">
        <div class="row align-items-center mb-4">
            <div class="col-md-8">
                <h2 class="mb-2">Recommandé pour vous</h2>
                <p class="text-muted mb-0">Découvrez des produits sélectionnés spécialement pour vous</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="/recommendations" class="btn btn-outline-primary">
                    <i class="bi bi-robot me-1"></i>
                    Voir toutes les recommandations
                </a>
            </div>
        </div>

        <div class="row g-4">
            @if (recommendedProducts != null && recommendedProducts.Any())
            {
                @foreach (var product in recommendedProducts.Take(4))
                {
                    <div class="col-lg-3 col-md-6">
                        <div class="card h-100 border-0 shadow-sm product-card position-relative">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-primary">
                                    <i class="bi bi-robot me-1"></i>IA
                                </span>
                            </div>
                            <a href="/catalog/products/@product.Id">
                                <img src="@product.MainImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                            </a>
                            <div class="card-body">
                                <h6 class="card-title">
                                    <a href="/catalog/products/@product.Id" class="text-decoration-none text-dark">@product.Name</a>
                                </h6>
                                <p class="text-primary fw-bold mb-2">@product.Price.ToString("N0") GNF</p>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="bi bi-lightbulb me-1 text-muted"></i>
                                    <small class="text-muted me-2">Recommandé pour vous</small>
                                    <div class="text-warning">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="bi @(i <= product.Rating ? "bi-star-fill" : "bi-star")"></i>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent border-0">
                                <div class="d-grid">
                                    <button class="btn btn-primary btn-sm" @onclick="() => AddToCart(product.Id)">
                                        <i class="bi bi-cart-plus me-1"></i>Ajouter au panier
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                @for (int i = 0; i < 4; i++)
                {
                    <div class="col-lg-3 col-md-6">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="spinner-border text-primary mb-3" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="text-muted">Chargement des recommandations...</p>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</section>

<!-- Pourquoi choisir NafaPlace -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Pourquoi choisir NafaPlace?</h2>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm text-center p-4">
                    <div class="mx-auto mb-4">
                        <i class="fas fa-shipping-fast text-primary" style="font-size: 3rem;"></i>
                    </div>
                    <h4>Livraison rapide</h4>
                    <p class="text-muted">Livraison dans toute l'Afrique avec suivi en temps réel de vos commandes.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm text-center p-4">
                    <div class="mx-auto mb-4">
                        <i class="fas fa-shield-alt text-primary" style="font-size: 3rem;"></i>
                    </div>
                    <h4>Paiements sécurisés</h4>
                    <p class="text-muted">Transactions sécurisées et multiples options de paiement disponibles.</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm text-center p-4">
                    <div class="mx-auto mb-4">
                        <i class="fas fa-headset text-primary" style="font-size: 3rem;"></i>
                    </div>
                    <h4>Support 24/7</h4>
                    <p class="text-muted">Notre équipe de support est disponible pour vous aider à tout moment.</p>
                </div>
            </div>
        </div>
    </div>
</section>

@code {
    private IEnumerable<CategoryDto> categories = Array.Empty<CategoryDto>();
    private IEnumerable<ProductDto> featuredProducts = Array.Empty<ProductDto>();
    private IEnumerable<ProductDto> newProducts = Array.Empty<ProductDto>();
    private IEnumerable<ProductDto> recommendedProducts = Array.Empty<ProductDto>();
    private string? _userId;

    protected override async Task OnInitializedAsync()
    {
        // Récupérer l'UserId de l'utilisateur connecté
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
            Console.WriteLine($"🔍 DEBUG Index: UserId récupéré = '{_userId}'");
        }
        else
        {
            Console.WriteLine("❌ DEBUG Index: Utilisateur non authentifié");
        }

        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            categories = await CategoryService.GetAllCategoriesAsync();
            featuredProducts = await ProductService.GetFeaturedProductsAsync(16);
            newProducts = await ProductService.GetNewProductsAsync(16);

            // Charger les recommandations (pour l'instant, utiliser les produits populaires)
            recommendedProducts = await ProductService.GetAllProductsAsync();
            recommendedProducts = recommendedProducts.Take(8).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading data: {ex.Message}");
            // Gérer l'erreur
        }
    }

    private void NavigateToProducts()
    {
        NavigationManager.NavigateTo("/catalog/products");
    }

    private void NavigateToAbout()
    {
        NavigationManager.NavigateTo("/about");
    }

    private void NavigateToCategory(int categoryId)
    {
        NavigationManager.NavigateTo($"/catalog?categoryId={categoryId}");
    }

    private void NavigateToAllCategories()
    {
        NavigationManager.NavigateTo("/catalog");
    }

    private async Task AddToCart(int productId)
    {
        Console.WriteLine($"🔍 DEBUG Index: Tentative d'ajout au panier - ProductId: {productId}");

        string userId = _userId ?? string.Empty;

        if (string.IsNullOrEmpty(userId))
        {
            // Utiliser un ID de session temporaire pour les utilisateurs non connectés
            userId = await GetOrCreateGuestUserId();
            Console.WriteLine($"🔍 DEBUG Index: Utilisateur invité - GuestId: {userId}");
        }

        try
        {
            Console.WriteLine($"🛒 DEBUG Index: Création de l'item panier - ProductId: {productId}, Quantity: 1");
            var cartItem = new CartItemCreateDto { ProductId = productId, Quantity = 1 };

            Console.WriteLine($"📡 DEBUG Index: Appel API AddItemToCartAsync...");
            var result = await CartService.AddItemToCartAsync(userId, cartItem);

            Console.WriteLine($"✅ DEBUG Index: Produit ajouté avec succès - ItemCount: {result?.ItemCount ?? 0}");

            // Notification de succès
            await JSRuntime.InvokeVoidAsync("showToast", $"✅ Produit ajouté au panier !", "success");
            Console.WriteLine($"Produit {productId} ajouté au panier.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG Index: Erreur lors de l'ajout au panier: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", $"❌ Erreur: {ex.Message}", "error");
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        try
        {
            var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");
            if (string.IsNullOrEmpty(guestId))
            {
                guestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";
                await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
                Console.WriteLine($"🔍 DEBUG Index: Nouvel ID invité créé: {guestId}");
            }
            else
            {
                Console.WriteLine($"🔍 DEBUG Index: ID invité existant récupéré: {guestId}");
            }
            return guestId;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG Index: Erreur lors de la gestion de l'ID invité: {ex.Message}");
            return $"guest_{Random.Shared.Next(1, int.MaxValue)}";
        }
    }


}
