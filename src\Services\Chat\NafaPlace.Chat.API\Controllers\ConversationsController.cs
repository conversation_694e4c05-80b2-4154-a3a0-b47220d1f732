using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Chat.Application.Services;
using NafaPlace.Chat.Application.DTOs;
using System.Security.Claims;

namespace NafaPlace.Chat.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ConversationsController : ControllerBase
{
    private readonly IChatService _chatService;
    private readonly ILogger<ConversationsController> _logger;

    public ConversationsController(IChatService chatService, ILogger<ConversationsController> logger)
    {
        _chatService = chatService;
        _logger = logger;
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> CreateConversation([FromBody] CreateConversationRequest request)
    {
        try
        {
            var userId = GetUserId();
            var userName = GetUserName();
            var userEmail = GetUserEmail();

            var conversationDto = new CreateConversationDto
            {
                Subject = request.Subject,
                Priority = request.Priority ?? "Normal",
                Category = request.Category ?? "General",
                UserId = userId,
                UserName = userName,
                UserEmail = userEmail,
                DepartmentId = request.DepartmentId,
                InitialMessage = request.InitialMessage,
                Tags = request.Tags ?? new List<string>(),
                Metadata = request.Metadata ?? new Dictionary<string, object>()
            };

            var conversationId = await _chatService.CreateConversationAsync(conversationDto);

            if (conversationId > 0)
            {
                return Ok(new { conversationId });
            }

            return BadRequest("Impossible de créer la conversation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de conversation");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet]
    [Authorize]
    public async Task<IActionResult> GetUserConversations([FromQuery] string? status = null)
    {
        try
        {
            var userId = GetUserId();
            var conversations = await _chatService.GetUserConversationsAsync(userId, 
                status != null ? Enum.Parse<ConversationStatus>(status) : null);

            return Ok(conversations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("{id}")]
    [Authorize]
    public async Task<IActionResult> GetConversation(int id)
    {
        try
        {
            var conversation = await _chatService.GetConversationAsync(id);
            
            if (conversation == null)
            {
                return NotFound("Conversation non trouvée");
            }

            // Vérifier que l'utilisateur a accès à cette conversation
            var userId = GetUserId();
            if (conversation.UserId != userId && !IsAgent())
            {
                return Forbid("Accès non autorisé à cette conversation");
            }

            return Ok(conversation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPut("{id}/close")]
    [Authorize]
    public async Task<IActionResult> CloseConversation(int id, [FromBody] CloseConversationRequest? request = null)
    {
        try
        {
            var success = await _chatService.CloseConversationAsync(id, request?.Reason ?? "Fermée par l'utilisateur");
            
            if (success)
            {
                return Ok(new { message = "Conversation fermée avec succès" });
            }

            return BadRequest("Impossible de fermer la conversation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la fermeture de la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPut("{id}/reopen")]
    [Authorize]
    public async Task<IActionResult> ReopenConversation(int id)
    {
        try
        {
            var success = await _chatService.ReopenConversationAsync(id);
            
            if (success)
            {
                return Ok(new { message = "Conversation rouverte avec succès" });
            }

            return BadRequest("Impossible de rouvrir la conversation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la réouverture de la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("{id}/messages")]
    [Authorize]
    public async Task<IActionResult> GetConversationMessages(int id, [FromQuery] int page = 1, [FromQuery] int pageSize = 50)
    {
        try
        {
            var messages = await _chatService.GetConversationMessagesAsync(id, page, pageSize);
            return Ok(messages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des messages de la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPut("{id}/read")]
    [Authorize]
    public async Task<IActionResult> MarkConversationAsRead(int id)
    {
        try
        {
            var success = await _chatService.MarkConversationAsReadAsync(id);
            
            if (success)
            {
                return Ok(new { message = "Conversation marquée comme lue" });
            }

            return BadRequest("Impossible de marquer la conversation comme lue");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage de la conversation comme lue {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPost("{id}/request-agent")]
    [Authorize]
    public async Task<IActionResult> RequestHumanAgent(int id, [FromBody] RequestAgentRequest request)
    {
        try
        {
            var success = await _chatService.RequestAgentAssignmentAsync(id, request.Reason);
            
            if (success)
            {
                return Ok(new { message = "Demande d'agent envoyée avec succès" });
            }

            return BadRequest("Impossible d'assigner un agent");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la demande d'agent pour la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPost("{id}/satisfaction")]
    [Authorize]
    public async Task<IActionResult> SubmitSatisfactionRating(int id, [FromBody] SatisfactionRequest request)
    {
        try
        {
            var success = await _chatService.SubmitSatisfactionRatingAsync(id, request.Rating, request.Comment);
            
            if (success)
            {
                return Ok(new { message = "Évaluation soumise avec succès" });
            }

            return BadRequest("Impossible de soumettre l'évaluation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la soumission de l'évaluation pour la conversation {ConversationId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    private string GetUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? 
               User.FindFirst("sub")?.Value ?? 
               "anonymous";
    }

    private string GetUserName()
    {
        return User.FindFirst(ClaimTypes.Name)?.Value ?? 
               User.FindFirst("name")?.Value ?? 
               "Utilisateur";
    }

    private string GetUserEmail()
    {
        return User.FindFirst(ClaimTypes.Email)?.Value ?? 
               User.FindFirst("email")?.Value ?? 
               "";
    }

    private bool IsAgent()
    {
        return User.IsInRole("Agent") || User.IsInRole("Admin");
    }
}

// DTOs pour les requêtes
public class CreateConversationRequest
{
    public string Subject { get; set; } = string.Empty;
    public string? Priority { get; set; }
    public string? Category { get; set; }
    public int? DepartmentId { get; set; }
    public string InitialMessage { get; set; } = string.Empty;
    public List<string>? Tags { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class CloseConversationRequest
{
    public string? Reason { get; set; }
}

public class RequestAgentRequest
{
    public string Reason { get; set; } = string.Empty;
}

public class SatisfactionRequest
{
    public int Rating { get; set; }
    public string? Comment { get; set; }
}
