using System.ComponentModel.DataAnnotations;
using NafaPlace.Common.Models;

namespace NafaPlace.Wishlist.Domain.Models;

public class WishlistItem : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [Required]
    public int ProductId { get; set; }

    [Required]
    [MaxLength(100)]
    public required string ProductName { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal ProductPrice { get; set; }

    [Required]
    [MaxLength(3)]
    public required string Currency { get; set; } = "GNF";

    [MaxLength(500)]
    public string? ProductImageUrl { get; set; }

    [MaxLength(50)]
    public string? ProductBrand { get; set; }

    public int? CategoryId { get; set; }

    [MaxLength(100)]
    public string? CategoryName { get; set; }

    public bool IsAvailable { get; set; } = true;

    public DateTime AddedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual Wishlist? Wishlist { get; set; }
}
