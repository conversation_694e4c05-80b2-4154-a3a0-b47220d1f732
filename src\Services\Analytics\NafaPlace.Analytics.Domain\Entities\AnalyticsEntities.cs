namespace NafaPlace.Analytics.Domain.Entities;

public class SalesMetric
{
    public int Id { get; set; }
    public DateTime Date { get; set; }
    public string Period { get; set; } = string.Empty; // daily, weekly, monthly
    public decimal TotalSales { get; set; }
    public int TotalOrders { get; set; }
    public int TotalCustomers { get; set; }
    public decimal AverageOrderValue { get; set; }
    public double ConversionRate { get; set; }
    public decimal Revenue { get; set; }
    public decimal Profit { get; set; }
    public string? SellerId { get; set; }
    public int? CategoryId { get; set; }
    public int? ProductId { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ProductMetric
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public DateTime Date { get; set; }
    public int Views { get; set; }
    public int Clicks { get; set; }
    public int AddToCart { get; set; }
    public int Purchases { get; set; }
    public decimal Revenue { get; set; }
    public double ConversionRate { get; set; }
    public double ClickThroughRate { get; set; }
    public double AverageRating { get; set; }
    public int ReviewCount { get; set; }
    public int WishlistCount { get; set; }
    public int ShareCount { get; set; }
    public double ReturnRate { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class UserMetric
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public int SessionCount { get; set; }
    public int PageViews { get; set; }
    public TimeSpan SessionDuration { get; set; }
    public double BounceRate { get; set; }
    public int OrderCount { get; set; }
    public decimal TotalSpent { get; set; }
    public decimal AverageOrderValue { get; set; }
    public DateTime LastActivity { get; set; }
    public string? DeviceType { get; set; }
    public string? Browser { get; set; }
    public string? Location { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class AnalyticsEvent
{
    public long Id { get; set; }
    public string EventType { get; set; } = string.Empty;
    public string? UserId { get; set; }
    public string? SessionId { get; set; }
    public string? EntityType { get; set; }
    public string? EntityId { get; set; }
    public string Properties { get; set; } = "{}";
    public DateTime Timestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public string? Referrer { get; set; }
    public string? Page { get; set; }
}

public class Report
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Parameters { get; set; } = "{}";
    public string? Schedule { get; set; }
    public bool IsActive { get; set; } = true;
    public string CreatedBy { get; set; } = string.Empty;
    public string Recipients { get; set; } = "[]";
    public DateTime? LastGenerated { get; set; }
    public DateTime? NextGeneration { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class KPI
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public decimal? PreviousValue { get; set; }
    public decimal? Target { get; set; }
    public string? Unit { get; set; }
    public string Period { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public string? Trend { get; set; }
    public double? ChangePercentage { get; set; }
    public string? SellerId { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
