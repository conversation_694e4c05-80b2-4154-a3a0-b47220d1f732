using NafaPlace.Web.Models.Catalog;

namespace NafaPlace.Web.Services;

public interface IAdvancedSearchService
{
    // Recherche avancée principale
    Task<ProductSearchResult> SearchProductsAdvancedAsync(ProductSearchRequest request);
    
    // Suggestions et autocomplétion
    Task<List<SearchSuggestion>> GetSearchSuggestionsAsync(string query, int maxSuggestions = 10);
    Task<List<string>> GetAutocompleteSuggestionsAsync(string query, int maxSuggestions = 5);
    
    // Recherche par similarité
    Task<List<ProductDto>> FindSimilarProductsAsync(int productId, int maxResults = 10);
    Task<List<ProductDto>> SearchByImageAsync(string imageUrl, int maxResults = 10);
    
    // Facettes et filtres
    Task<List<SearchFacet>> GetSearchFacetsAsync(ProductSearchRequest request);
    Task<Dictionary<string, List<string>>> GetAvailableFiltersAsync();
    
    // Historique et tendances
    Task<List<string>> GetPopularSearchesAsync(int count = 10);
    Task<List<string>> GetUserSearchHistoryAsync(int count = 10);
    Task<List<string>> GetSearchTrendsAsync(int days = 30);
}
