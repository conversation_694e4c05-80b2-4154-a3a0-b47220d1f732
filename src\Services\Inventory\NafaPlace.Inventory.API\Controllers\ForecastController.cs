using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Inventory.Application.DTOs;
using NafaPlace.Inventory.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Inventory.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class ForecastController : ControllerBase
{
    private readonly IStockForecastService _forecastService;
    private readonly ILogger<ForecastController> _logger;

    public ForecastController(IStockForecastService forecastService, ILogger<ForecastController> logger)
    {
        _forecastService = forecastService;
        _logger = logger;
    }

    [HttpGet("product/{productId}")]
    public async Task<ActionResult<StockForecastDto>> GetProductForecast(
        int productId,
        [FromQuery] int forecastDays = 30,
        [FromQuery] int historyDays = 90,
        [FromQuery] bool includeSeasonality = true,
        [FromQuery] bool includeTrends = true,
        [FromQuery] double safetyStockPercentage = 0.2)
    {
        try
        {
            var parameters = new ForecastParametersDto
            {
                ProductId = productId,
                ForecastDays = forecastDays,
                HistoryDays = historyDays,
                IncludeSeasonality = includeSeasonality,
                IncludeTrends = includeTrends,
                SafetyStockPercentage = safetyStockPercentage
            };

            var forecast = await _forecastService.GetProductForecastAsync(productId, parameters);
            return Ok(forecast);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la prévision pour le produit {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("bulk")]
    public async Task<ActionResult<ForecastSummaryDto>> GetBulkForecast([FromBody] BulkForecastRequestDto request)
    {
        try
        {
            var summary = await _forecastService.GetBulkForecastAsync(request);
            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la génération des prévisions en lot");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("seller/{sellerId}")]
    [Authorize(Roles = "Admin,Seller")]
    public async Task<ActionResult<List<StockForecastDto>>> GetSellerForecast(
        int sellerId,
        [FromQuery] int forecastDays = 30)
    {
        try
        {
            // Vérifier que le vendeur peut accéder à ses propres données
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && userSellerId != sellerId.ToString())
            {
                return Forbid("Accès non autorisé aux données de ce vendeur");
            }

            var parameters = new ForecastParametersDto { ForecastDays = forecastDays };
            var forecasts = await _forecastService.GetSellerForecastAsync(sellerId, parameters);
            return Ok(forecasts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des prévisions pour le vendeur {SellerId}", sellerId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("category/{categoryId}")]
    public async Task<ActionResult<List<StockForecastDto>>> GetCategoryForecast(
        int categoryId,
        [FromQuery] int forecastDays = 30)
    {
        try
        {
            var parameters = new ForecastParametersDto { ForecastDays = forecastDays };
            var forecasts = await _forecastService.GetCategoryForecastAsync(categoryId, parameters);
            return Ok(forecasts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des prévisions pour la catégorie {CategoryId}", categoryId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("recommendations")]
    public async Task<ActionResult<List<StockRecommendationDto>>> GetReorderRecommendations(
        [FromQuery] int? sellerId = null)
    {
        try
        {
            // Filtrer par vendeur si l'utilisateur n'est pas admin
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var recommendations = await _forecastService.GetReorderRecommendationsAsync(sellerId);
            return Ok(recommendations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des recommandations de commande");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("critical")]
    public async Task<ActionResult<List<StockForecastDto>>> GetCriticalStockForecast(
        [FromQuery] int? sellerId = null)
    {
        try
        {
            // Filtrer par vendeur si l'utilisateur n'est pas admin
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var criticalForecasts = await _forecastService.GetCriticalStockForecastAsync(sellerId);
            return Ok(criticalForecasts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des prévisions critiques");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("low-stock")]
    public async Task<ActionResult<List<StockForecastDto>>> GetLowStockForecast(
        [FromQuery] int? sellerId = null,
        [FromQuery] int daysThreshold = 7)
    {
        try
        {
            // Filtrer par vendeur si l'utilisateur n'est pas admin
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var lowStockForecasts = await _forecastService.GetLowStockForecastAsync(sellerId, daysThreshold);
            return Ok(lowStockForecasts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des prévisions de stock faible");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("trends/{productId}")]
    public async Task<ActionResult<Dictionary<string, object>>> GetDemandTrends(
        int productId,
        [FromQuery] int days = 30)
    {
        try
        {
            var trends = await _forecastService.GetDemandTrendsAsync(productId, days);
            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des tendances pour le produit {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("seasonality/{productId}")]
    public async Task<ActionResult<Dictionary<string, object>>> GetSeasonalityAnalysis(int productId)
    {
        try
        {
            var seasonality = await _forecastService.GetSeasonalityAnalysisAsync(productId);
            return Ok(seasonality);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'analyse de saisonnalité pour le produit {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("predictions/{productId}")]
    public async Task<ActionResult<List<DemandPredictionDto>>> GetDemandPredictions(
        int productId,
        [FromQuery] int days = 30)
    {
        try
        {
            var predictions = await _forecastService.GetDemandPredictionsAsync(productId, days);
            return Ok(predictions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des prédictions pour le produit {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("optimal-levels/{productId}")]
    public async Task<ActionResult<Dictionary<string, object>>> GetOptimalStockLevels(int productId)
    {
        try
        {
            var optimalLevels = await _forecastService.GetOptimalStockLevelsAsync(productId);
            return Ok(optimalLevels);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul des niveaux optimaux pour le produit {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("optimal-quantity/{productId}")]
    public async Task<ActionResult<decimal>> GetOptimalReorderQuantity(int productId)
    {
        try
        {
            var quantity = await _forecastService.CalculateOptimalReorderQuantityAsync(productId);
            return Ok(new { productId, optimalQuantity = quantity });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul de la quantité optimale pour le produit {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("optimal-date/{productId}")]
    public async Task<ActionResult<DateTime>> GetOptimalReorderDate(int productId)
    {
        try
        {
            var date = await _forecastService.GetOptimalReorderDateAsync(productId);
            return Ok(new { productId, optimalReorderDate = date });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul de la date optimale pour le produit {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("accuracy/{productId}")]
    public async Task<ActionResult<double>> GetForecastAccuracy(
        int productId,
        [FromQuery] int days = 30)
    {
        try
        {
            var accuracy = await _forecastService.GetForecastAccuracyAsync(productId, days);
            return Ok(new { productId, accuracy, days });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du calcul de la précision pour le produit {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("metrics")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<Dictionary<string, object>>> GetForecastMetrics(
        [FromQuery] int? sellerId = null)
    {
        try
        {
            var metrics = await _forecastService.GetForecastMetricsAsync(sellerId);
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des métriques de prévision");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("recalibrate/{productId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> RecalibrateForecastModel(int productId)
    {
        try
        {
            await _forecastService.RecalibrateForecastModelAsync(productId);
            return Ok(new { message = $"Modèle de prévision recalibré pour le produit {productId}" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du recalibrage du modèle pour le produit {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpPost("update")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> UpdateForecasts()
    {
        try
        {
            await _forecastService.UpdateForecastsAsync();
            return Ok(new { message = "Prévisions mises à jour avec succès" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour des prévisions");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpDelete("cleanup")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> CleanupOldForecasts([FromQuery] int daysToKeep = 90)
    {
        try
        {
            var deletedCount = await _forecastService.CleanupOldForecastsAsync(daysToKeep);
            return Ok(new { message = $"{deletedCount} anciennes prévisions supprimées" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du nettoyage des anciennes prévisions");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }
}
