using System.Net.Http.Json;
using System.Text.Json;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using Blazored.LocalStorage;
using NafaPlace.Web.Models;

namespace NafaPlace.Web.Services;

public class ChatService : IChatService
{
    private readonly HttpClient _httpClient;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ILocalStorageService _localStorage;
    private readonly IJSRuntime _jsRuntime;
    private readonly ILogger<ChatService> _logger;

    public ChatService(
        HttpClient httpClient,
        AuthenticationStateProvider authStateProvider,
        ILocalStorageService localStorage,
        IJSRuntime jsRuntime,
        ILogger<ChatService> logger)
    {
        _httpClient = httpClient;
        _authStateProvider = authStateProvider;
        _localStorage = localStorage;
        _jsRuntime = jsRuntime;
        _logger = logger;
    }

    public async Task<int> StartConversationAsync(string subject, string initialMessage)
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var request = new
            {
                Subject = subject,
                InitialMessage = initialMessage,
                Priority = "Normal",
                Department = "Support"
            };

            var response = await _httpClient.PostAsJsonAsync("/api/conversations", request);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<JsonElement>();
                return result.GetProperty("conversationId").GetInt32();
            }

            _logger.LogWarning("Échec de création de conversation: {StatusCode}", response.StatusCode);
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de conversation");
            return 0;
        }
    }

    public async Task<List<ChatConversationDto>> GetUserConversationsAsync()
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var response = await _httpClient.GetAsync("/api/conversations");
            
            if (response.IsSuccessStatusCode)
            {
                var conversations = await response.Content.ReadFromJsonAsync<List<ChatConversationDto>>();
                return conversations ?? new List<ChatConversationDto>();
            }

            return new List<ChatConversationDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des conversations");
            return new List<ChatConversationDto>();
        }
    }

    public async Task<ChatConversationDto?> GetConversationAsync(int conversationId)
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var response = await _httpClient.GetAsync($"/api/conversations/{conversationId}");
            
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<ChatConversationDto>();
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la conversation {ConversationId}", conversationId);
            return null;
        }
    }

    public async Task<bool> CloseConversationAsync(int conversationId)
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var response = await _httpClient.PutAsync($"/api/conversations/{conversationId}/close", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la fermeture de la conversation {ConversationId}", conversationId);
            return false;
        }
    }

    public async Task<int> SendMessageAsync(int conversationId, string content, string? attachmentUrl = null)
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var request = new
            {
                ConversationId = conversationId,
                Content = content,
                AttachmentUrl = attachmentUrl
            };

            var response = await _httpClient.PostAsJsonAsync("/api/messages", request);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<JsonElement>();
                return result.GetProperty("messageId").GetInt32();
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du message");
            return 0;
        }
    }

    public async Task<List<ChatMessageDto>> GetConversationMessagesAsync(int conversationId, int page = 1, int pageSize = 50)
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var response = await _httpClient.GetAsync($"/api/conversations/{conversationId}/messages?page={page}&pageSize={pageSize}");
            
            if (response.IsSuccessStatusCode)
            {
                var messages = await response.Content.ReadFromJsonAsync<List<ChatMessageDto>>();
                return messages ?? new List<ChatMessageDto>();
            }

            return new List<ChatMessageDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des messages");
            return new List<ChatMessageDto>();
        }
    }

    public async Task<bool> MarkMessageAsReadAsync(int messageId)
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var response = await _httpClient.PutAsync($"/api/messages/{messageId}/read", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage du message comme lu");
            return false;
        }
    }

    public async Task<bool> MarkConversationAsReadAsync(int conversationId)
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var response = await _httpClient.PutAsync($"/api/conversations/{conversationId}/read", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du marquage de la conversation comme lue");
            return false;
        }
    }

    public async Task<List<FAQItemDto>> GetFAQAsync(string? category = null)
    {
        try
        {
            var url = "/api/faq";
            if (!string.IsNullOrEmpty(category))
            {
                url += $"?category={Uri.EscapeDataString(category)}";
            }

            var response = await _httpClient.GetAsync(url);
            
            if (response.IsSuccessStatusCode)
            {
                var faqItems = await response.Content.ReadFromJsonAsync<List<FAQItemDto>>();
                return faqItems ?? new List<FAQItemDto>();
            }

            return new List<FAQItemDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la FAQ");
            return new List<FAQItemDto>();
        }
    }

    public async Task<List<string>> GetFAQCategoriesAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("/api/faq/categories");
            
            if (response.IsSuccessStatusCode)
            {
                var categories = await response.Content.ReadFromJsonAsync<List<string>>();
                return categories ?? new List<string>();
            }

            return new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des catégories FAQ");
            return new List<string>();
        }
    }

    public async Task<ChatbotResponseDto> SendChatbotMessageAsync(string message, string sessionId)
    {
        try
        {
            var request = new
            {
                Message = message,
                SessionId = sessionId
            };

            var response = await _httpClient.PostAsJsonAsync("/api/chatbot", request);
            
            if (response.IsSuccessStatusCode)
            {
                var botResponse = await response.Content.ReadFromJsonAsync<ChatbotResponseDto>();
                return botResponse ?? new ChatbotResponseDto { Message = "Désolé, je n'ai pas pu traiter votre demande." };
            }

            return new ChatbotResponseDto { Message = "Service temporairement indisponible." };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi du message au chatbot");
            return new ChatbotResponseDto { Message = "Erreur de communication avec le chatbot." };
        }
    }

    public async Task<bool> UpdateUserPresenceAsync(bool isOnline)
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var request = new { IsOnline = isOnline };
            var response = await _httpClient.PutAsJsonAsync("/api/presence", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la présence");
            return false;
        }
    }

    public async Task<List<ChatAgentDto>> GetAvailableAgentsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("/api/agents/available");
            
            if (response.IsSuccessStatusCode)
            {
                var agents = await response.Content.ReadFromJsonAsync<List<ChatAgentDto>>();
                return agents ?? new List<ChatAgentDto>();
            }

            return new List<ChatAgentDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des agents disponibles");
            return new List<ChatAgentDto>();
        }
    }

    public async Task<bool> RequestHumanAgentAsync(int conversationId, string reason)
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var request = new { Reason = reason };
            var response = await _httpClient.PostAsJsonAsync($"/api/conversations/{conversationId}/request-agent", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la demande d'agent humain");
            return false;
        }
    }

    public async Task<bool> JoinChatGroupAsync(int conversationId)
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("chatSignalR.joinGroup", conversationId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la connexion au groupe de chat");
            return false;
        }
    }

    public async Task<bool> LeaveChatGroupAsync(int conversationId)
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("chatSignalR.leaveGroup", conversationId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la déconnexion du groupe de chat");
            return false;
        }
    }

    public async Task SendTypingIndicatorAsync(int conversationId, bool isTyping)
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("chatSignalR.sendTypingIndicator", conversationId, isTyping);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de l'indicateur de frappe");
        }
    }

    public async Task<bool> SubmitSatisfactionRatingAsync(int conversationId, int rating, string? comment = null)
    {
        try
        {
            var token = await GetAuthTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }

            var request = new { Rating = rating, Comment = comment };
            var response = await _httpClient.PostAsJsonAsync($"/api/conversations/{conversationId}/satisfaction", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'envoi de l'évaluation de satisfaction");
            return false;
        }
    }

    public async Task<Dictionary<string, object>> GetChatConfigAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("/api/config");
            
            if (response.IsSuccessStatusCode)
            {
                var config = await response.Content.ReadFromJsonAsync<Dictionary<string, object>>();
                return config ?? new Dictionary<string, object>();
            }

            return new Dictionary<string, object>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la configuration du chat");
            return new Dictionary<string, object>();
        }
    }

    public async Task<string?> GetAuthTokenAsync()
    {
        try
        {
            return await _localStorage.GetItemAsync<string>("authToken");
        }
        catch
        {
            return null;
        }
    }

    private async Task SetAuthHeaderAsync()
    {
        var token = await GetAuthTokenAsync();
        if (!string.IsNullOrEmpty(token))
        {
            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        }
    }
}
