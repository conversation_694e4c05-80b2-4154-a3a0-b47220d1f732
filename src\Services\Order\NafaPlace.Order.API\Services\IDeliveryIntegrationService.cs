using NafaPlace.Order.API.Models;

namespace NafaPlace.Order.API.Services;

public interface IDeliveryIntegrationService
{
    Task<decimal> CalculateDeliveryFeeAsync(string address, decimal orderAmount, decimal? weight = null);
    Task<List<DeliveryQuoteDto>> GetDeliveryQuotesAsync(string address, decimal orderAmount, decimal? weight = null, decimal? volume = null);
    Task<DeliveryOrderDto?> CreateDeliveryOrderAsync(Domain.Order order);
    Task<bool> UpdateDeliveryStatusAsync(string trackingNumber, string status, string description);
    Task<List<DeliveryTrackingDto>> GetDeliveryTrackingAsync(string trackingNumber);
}

public class DeliveryIntegrationService : IDeliveryIntegrationService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<DeliveryIntegrationService> _logger;

    public DeliveryIntegrationService(HttpClient httpClient, ILogger<DeliveryIntegrationService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<decimal> CalculateDeliveryFeeAsync(string address, decimal orderAmount, decimal? weight = null)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/delivery/calculate-fee?address={Uri.EscapeDataString(address)}&orderAmount={orderAmount}");
            
            if (response.IsSuccessStatusCode)
            {
                var feeString = await response.Content.ReadAsStringAsync();
                if (decimal.TryParse(feeString, out var fee))
                {
                    return fee;
                }
            }
            
            _logger.LogWarning("Failed to calculate delivery fee, using default");
            return 25000; // Default fee
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating delivery fee");
            return 25000; // Default fee
        }
    }

    public async Task<List<DeliveryQuoteDto>> GetDeliveryQuotesAsync(string address, decimal orderAmount, decimal? weight = null, decimal? volume = null)
    {
        try
        {
            var request = new
            {
                DeliveryAddress = address,
                OrderValue = orderAmount,
                Weight = weight,
                Volume = volume
            };

            var response = await _httpClient.PostAsJsonAsync("api/delivery/quotes", request);
            
            if (response.IsSuccessStatusCode)
            {
                var quotes = await response.Content.ReadFromJsonAsync<List<DeliveryQuoteDto>>();
                return quotes ?? new List<DeliveryQuoteDto>();
            }
            
            _logger.LogWarning("Failed to get delivery quotes");
            return new List<DeliveryQuoteDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery quotes");
            return new List<DeliveryQuoteDto>();
        }
    }

    public async Task<DeliveryOrderDto?> CreateDeliveryOrderAsync(Domain.Order order)
    {
        try
        {
            if (order.ShippingAddress == null)
            {
                _logger.LogWarning("Cannot create delivery order without shipping address for order {OrderId}", order.Id);
                return null;
            }

            var deliveryRequest = new CreateDeliveryOrderRequest
            {
                OrderId = order.Id.ToString(),
                CustomerId = order.UserId,
                CustomerName = order.ShippingAddress.FullName,
                CustomerEmail = "", // We might need to get this from user service
                CustomerPhone = order.ShippingAddress.PhoneNumber ?? "",
                DeliveryAddress = order.ShippingAddress.Address,
                DeliveryCity = order.ShippingAddress.City,
                DeliveryRegion = order.ShippingAddress.Country,
                OrderValue = order.TotalAmount,
                PackageCount = order.OrderItems.Sum(i => i.Quantity),
                PackageDescription = $"Commande #{order.Id} - {order.OrderItems.Count} article(s)",
                Type = DeliveryType.Standard,
                ScheduledDeliveryDate = DateTime.UtcNow.AddDays(1)
            };

            var response = await _httpClient.PostAsJsonAsync("api/delivery/orders", deliveryRequest);
            
            if (response.IsSuccessStatusCode)
            {
                var deliveryOrder = await response.Content.ReadFromJsonAsync<DeliveryOrderDto>();
                _logger.LogInformation("Created delivery order {DeliveryOrderId} for order {OrderId}", 
                    deliveryOrder?.Id, order.Id);
                return deliveryOrder;
            }
            
            _logger.LogWarning("Failed to create delivery order for order {OrderId}", order.Id);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating delivery order for order {OrderId}", order.Id);
            return null;
        }
    }

    public async Task<bool> UpdateDeliveryStatusAsync(string trackingNumber, string status, string description)
    {
        try
        {
            // First, find the delivery order by tracking number
            var trackingResponse = await _httpClient.GetAsync($"api/delivery/orders/tracking/{trackingNumber}");
            if (!trackingResponse.IsSuccessStatusCode)
            {
                _logger.LogWarning("Delivery order not found for tracking number {TrackingNumber}", trackingNumber);
                return false;
            }

            var trackingEvents = await trackingResponse.Content.ReadFromJsonAsync<List<DeliveryTrackingDto>>();
            if (trackingEvents == null || !trackingEvents.Any())
            {
                _logger.LogWarning("No tracking events found for tracking number {TrackingNumber}", trackingNumber);
                return false;
            }

            var deliveryOrderId = trackingEvents.First().DeliveryOrderId;

            var updateRequest = new
            {
                Status = status,
                Description = description,
                EventDate = DateTime.UtcNow
            };

            var response = await _httpClient.PutAsJsonAsync($"api/delivery/orders/{deliveryOrderId}/status", updateRequest);
            
            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Updated delivery status for tracking {TrackingNumber} to {Status}", 
                    trackingNumber, status);
                return true;
            }
            
            _logger.LogWarning("Failed to update delivery status for tracking {TrackingNumber}", trackingNumber);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating delivery status for tracking {TrackingNumber}", trackingNumber);
            return false;
        }
    }

    public async Task<List<DeliveryTrackingDto>> GetDeliveryTrackingAsync(string trackingNumber)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/delivery/orders/tracking/{trackingNumber}");
            
            if (response.IsSuccessStatusCode)
            {
                var tracking = await response.Content.ReadFromJsonAsync<List<DeliveryTrackingDto>>();
                return tracking ?? new List<DeliveryTrackingDto>();
            }
            
            _logger.LogWarning("Failed to get delivery tracking for {TrackingNumber}", trackingNumber);
            return new List<DeliveryTrackingDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery tracking for {TrackingNumber}", trackingNumber);
            return new List<DeliveryTrackingDto>();
        }
    }
}

// DTOs for delivery integration
public class DeliveryQuoteDto
{
    public int CarrierId { get; set; }
    public string CarrierName { get; set; } = string.Empty;
    public int ZoneId { get; set; }
    public string ZoneName { get; set; } = string.Empty;
    public decimal DeliveryFee { get; set; }
    public decimal TotalFee { get; set; }
    public string Currency { get; set; } = "GNF";
    public int EstimatedDeliveryDays { get; set; }
    public DateTime EstimatedDeliveryDate { get; set; }
    public bool IsAvailable { get; set; }
    public bool SameDayAvailable { get; set; }
    public decimal? SameDayFee { get; set; }
    public bool ExpressAvailable { get; set; }
    public decimal? ExpressFee { get; set; }
}

public class DeliveryOrderDto
{
    public int Id { get; set; }
    public string OrderId { get; set; } = string.Empty;
    public string TrackingNumber { get; set; } = string.Empty;
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public decimal DeliveryFee { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? EstimatedDeliveryDate { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class DeliveryTrackingDto
{
    public int Id { get; set; }
    public int DeliveryOrderId { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? Location { get; set; }
    public DateTime EventDate { get; set; }
    public string? EventBy { get; set; }
    public string? Notes { get; set; }
    public bool IsCustomerVisible { get; set; }
}

public class CreateDeliveryOrderRequest
{
    public string OrderId { get; set; } = string.Empty;
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerEmail { get; set; } = string.Empty;
    public string CustomerPhone { get; set; } = string.Empty;
    public string DeliveryAddress { get; set; } = string.Empty;
    public string? DeliveryCity { get; set; }
    public string? DeliveryRegion { get; set; }
    public decimal OrderValue { get; set; }
    public int PackageCount { get; set; } = 1;
    public string? PackageDescription { get; set; }
    public DeliveryType Type { get; set; } = DeliveryType.Standard;
    public DateTime? ScheduledDeliveryDate { get; set; }
}

public enum DeliveryType
{
    Standard = 1,
    Express = 2,
    SameDay = 3,
    Scheduled = 4
}
