@using NafaPlace.Reviews.DTOs
@using NafaPlace.Web.Components.Reviews
@namespace NafaPlace.Web.Components.Reviews

<div class="review-summary">
    @if (Summary != null && Summary.TotalReviews > 0)
    {
        <div class="row">
            <div class="col-md-4">
                <div class="overall-rating text-center">
                    <div class="rating-score">
                        <span class="score">@Summary.AverageRating.ToString("0.0")</span>
                        <span class="max-score">/5</span>
                    </div>
                    <div class="rating-stars mb-2">
                        <StarRating Rating="Summary.AverageRating" ShowRatingText="false" CssClass="large" />
                    </div>
                    <div class="rating-count">
                        <small class="text-muted">Basé sur @Summary.TotalReviews avis</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="rating-breakdown">
                    @for (int rating = 5; rating >= 1; rating--)
                    {
                        var currentRating = rating;
                        var count = Summary.RatingDistribution.ContainsKey(currentRating) ? Summary.RatingDistribution[currentRating] : 0;
                        var percentage = Summary.TotalReviews > 0 ? (count * 100.0 / Summary.TotalReviews) : 0;
                        
                        <div class="rating-bar-item d-flex align-items-center mb-2">
                            <div class="rating-label me-2">
                                <span>@currentRating</span>
                                <i class="fas fa-star text-warning"></i>
                            </div>
                            <div class="rating-bar flex-grow-1 me-2">
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-warning" 
                                         role="progressbar" 
                                         style="width: @(percentage)%"
                                         aria-valuenow="@percentage" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                    </div>
                                </div>
                            </div>
                            <div class="rating-count">
                                <small class="text-muted">@count</small>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
        
        @if (ShowWriteReviewButton)
        {
            <div class="text-center mt-3">
                <button class="btn btn-primary" @onclick="HandleWriteReviewClick">
                    <i class="fas fa-star me-2"></i>@(IsEditMode ? "Modifier mon avis" : "Écrire un avis")
                </button>
            </div>
        }
    }
    else
    {
        <div class="no-reviews text-center py-4">
            <i class="fas fa-star fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun avis pour ce produit</h5>
            <p class="text-muted">Soyez le premier à partager votre expérience !</p>
            
            @if (ShowWriteReviewButton)
            {
                <button class="btn btn-primary" @onclick="HandleWriteReviewClick">
                    <i class="fas fa-star me-2"></i>@(IsEditMode ? "Modifier mon avis" : "Écrire le premier avis")
                </button>
            }
        </div>
    }
</div>

<style>
    .review-summary {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .overall-rating {
        padding: 1rem;
    }

    .rating-score {
        margin-bottom: 1rem;
    }

    .rating-score .score {
        font-size: 3rem;
        font-weight: 700;
        color: #ffc107;
        line-height: 1;
    }

    .rating-score .max-score {
        font-size: 1.5rem;
        color: #6c757d;
        font-weight: 500;
    }

    .rating-breakdown {
        padding: 1rem 0;
    }

    .rating-bar-item {
        min-height: 24px;
    }

    .rating-label {
        min-width: 50px;
        font-size: 0.9rem;
        color: #495057;
    }

    .rating-bar {
        min-width: 200px;
    }

    .rating-count {
        min-width: 30px;
        text-align: right;
    }

    .progress {
        background-color: #e9ecef;
    }

    .progress-bar {
        background-color: #ffc107 !important;
    }

    .no-reviews {
        padding: 3rem 1rem;
    }

    @@media (max-width: 768px) {
        .review-summary {
            padding: 1rem;
        }

        .rating-score .score {
            font-size: 2rem;
        }

        .rating-bar {
            min-width: 150px;
        }
    }
</style>

@code {
    [Parameter] public ReviewSummaryDto? Summary { get; set; }
    [Parameter] public bool ShowWriteReviewButton { get; set; } = true;
    [Parameter] public bool IsEditMode { get; set; } = false;
    [Parameter] public EventCallback OnWriteReviewClick { get; set; }

    private async Task HandleWriteReviewClick()
    {
        if (OnWriteReviewClick.HasDelegate)
        {
            await OnWriteReviewClick.InvokeAsync();
        }
    }
}
