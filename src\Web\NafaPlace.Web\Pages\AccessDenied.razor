@page "/access-denied"
@inject NavigationManager NavigationManager

<PageTitle>Accès <PERSON>é - NafaPlace</PageTitle>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card border-0 shadow-lg">
                <div class="card-body text-center p-5">
                    <!-- Icon -->
                    <div class="mb-4">
                        <i class="bi bi-shield-exclamation text-warning" style="font-size: 5rem;"></i>
                    </div>
                    
                    <!-- Title -->
                    <h2 class="mb-3 text-dark">Accès Restreint</h2>
                    
                    <!-- Description -->
                    <p class="text-muted mb-4 lead">
                        Cette fonctionnalité est réservée aux administrateurs et vendeurs. 
                        Veuillez utiliser les portails appropriés pour accéder aux outils de gestion.
                    </p>
                    
                    <!-- Portal Links -->
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <i class="bi bi-gear text-primary fs-1 mb-3"></i>
                                    <h5 class="card-title">Portail Administrateur</h5>
                                    <p class="card-text text-muted small">
                                        Gestion complète de la plateforme, utilisateurs, produits et commandes
                                    </p>
                                    <a href="http://localhost:8081" class="btn btn-primary" target="_blank">
                                        <i class="bi bi-box-arrow-up-right me-2"></i>Accéder
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <i class="bi bi-shop text-success fs-1 mb-3"></i>
                                    <h5 class="card-title">Portail Vendeur</h5>
                                    <p class="card-text text-muted small">
                                        Gestion de vos produits, commandes et statistiques de vente
                                    </p>
                                    <a href="http://localhost:8082" class="btn btn-success" target="_blank">
                                        <i class="bi bi-box-arrow-up-right me-2"></i>Accéder
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Navigation Options -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <button class="btn btn-outline-secondary me-md-2" @onclick="GoBack">
                            <i class="bi bi-arrow-left me-2"></i>Retour
                        </button>
                        <a href="/" class="btn btn-outline-primary">
                            <i class="bi bi-house me-2"></i>Accueil
                        </a>
                    </div>
                    
                    <!-- Info -->
                    <hr class="my-4">
                    <div class="alert alert-info border-0" role="alert">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Information :</strong> Les tableaux de bord et outils de gestion sont disponibles 
                        sur des portails séparés pour une meilleure sécurité et organisation.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private void GoBack()
    {
        NavigationManager.NavigateTo("/");
    }
}

<style>
    .card {
        background-color: white !important;
        color: #212529 !important;
    }
    
    .card-title {
        color: #212529 !important;
    }
    
    .card-text {
        color: #6c757d !important;
    }
    
    .text-muted {
        color: #6c757d !important;
    }
    
    .text-dark {
        color: #212529 !important;
    }
    
    .lead {
        color: #495057 !important;
    }
    
    .card:hover {
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
    
    .border-primary {
        border-color: #E73C30 !important;
    }
    
    .border-success {
        border-color: #28a745 !important;
    }
    
    .btn-primary {
        background-color: #E73C30;
        border-color: #E73C30;
    }
    
    .btn-primary:hover {
        background-color: #c8342a;
        border-color: #c8342a;
    }
    
    .btn-outline-primary {
        color: #E73C30;
        border-color: #E73C30;
    }
    
    .btn-outline-primary:hover {
        background-color: #E73C30;
        border-color: #E73C30;
    }
    
    .text-primary {
        color: #E73C30 !important;
    }
</style>
