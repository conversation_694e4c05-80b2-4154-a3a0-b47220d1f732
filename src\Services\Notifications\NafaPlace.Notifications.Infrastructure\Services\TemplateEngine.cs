using NafaPlace.Notifications.Application.Services;
using System.Text.RegularExpressions;

namespace NafaPlace.Notifications.Infrastructure.Services;

public class TemplateEngine : ITemplateEngine
{
    public string ProcessTemplate(string template, Dictionary<string, object> variables)
    {
        if (string.IsNullOrEmpty(template) || variables == null)
            return template;

        var result = template;

        // Remplacer les variables simples {{VariableName}}
        foreach (var variable in variables)
        {
            var placeholder = $"{{{{{variable.Key}}}}}";
            var value = variable.Value?.ToString() ?? string.Empty;
            result = result.Replace(placeholder, value);
        }

        // Traiter les boucles simples {{#Items}}...{{/Items}}
        result = ProcessLoops(result, variables);

        return result;
    }

    public bool ValidateTemplate(string template)
    {
        if (string.IsNullOrEmpty(template))
            return false;

        try
        {
            // Vérifier que toutes les balises sont bien fermées
            var openTags = Regex.Matches(template, @"\{\{#(\w+)\}\}").Count;
            var closeTags = Regex.Matches(template, @"\{\{/(\w+)\}\}").Count;

            return openTags == closeTags;
        }
        catch
        {
            return false;
        }
    }

    public List<string> ExtractVariables(string template)
    {
        var variables = new List<string>();

        if (string.IsNullOrEmpty(template))
            return variables;

        // Extraire les variables simples {{VariableName}}
        var matches = Regex.Matches(template, @"\{\{([^#/][^}]*)\}\}");
        
        foreach (Match match in matches)
        {
            var variableName = match.Groups[1].Value.Trim();
            if (!variables.Contains(variableName))
            {
                variables.Add(variableName);
            }
        }

        return variables;
    }

    private string ProcessLoops(string template, Dictionary<string, object> variables)
    {
        // Pattern pour détecter les boucles {{#Items}}...{{/Items}}
        var loopPattern = @"\{\{#(\w+)\}\}(.*?)\{\{/\1\}\}";
        var matches = Regex.Matches(template, loopPattern, RegexOptions.Singleline);

        foreach (Match match in matches)
        {
            var loopVariable = match.Groups[1].Value;
            var loopTemplate = match.Groups[2].Value;
            var fullMatch = match.Groups[0].Value;

            if (variables.TryGetValue(loopVariable, out var value))
            {
                var result = string.Empty;

                if (value is IEnumerable<object> items)
                {
                    foreach (var item in items)
                    {
                        var itemTemplate = loopTemplate;
                        
                        // Si l'item est un dictionnaire, remplacer les propriétés
                        if (item is Dictionary<string, object> itemDict)
                        {
                            foreach (var prop in itemDict)
                            {
                                var propPlaceholder = $"{{{{{prop.Key}}}}}";
                                var propValue = prop.Value?.ToString() ?? string.Empty;
                                itemTemplate = itemTemplate.Replace(propPlaceholder, propValue);
                            }
                        }
                        else
                        {
                            // Pour les objets simples, utiliser ToString()
                            itemTemplate = itemTemplate.Replace("{{.}}", item?.ToString() ?? string.Empty);
                        }

                        result += itemTemplate;
                    }
                }

                template = template.Replace(fullMatch, result);
            }
        }

        return template;
    }
}
