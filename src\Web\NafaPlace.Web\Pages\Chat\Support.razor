@page "/chat/support"
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Chat Support - NafaPlace</PageTitle>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-white bg-opacity-25 rounded-circle p-2">
                                <i class="bi bi-chat-dots fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="mb-0">💬 Chat Support NafaPlace</h5>
                            <small class="opacity-75">Support client en temps réel</small>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge bg-success">
                                <i class="bi bi-circle-fill me-1" style="font-size: 0.5rem;"></i>
                                En ligne
                            </span>
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <!-- Zone de chat -->
                    <div class="chat-container" style="height: 500px; overflow-y: auto;" id="chatContainer">
                        <div class="p-3">
                            @if (messages.Any())
                            {
                                @foreach (var message in messages)
                                {
                                    <div class="message-wrapper mb-3 @(message.IsFromUser ? "text-end" : "text-start")">
                                        <div class="d-inline-block">
                                            <div class="message @(message.IsFromUser ? "user-message" : "support-message")">
                                                <div class="message-content">
                                                    @message.Content
                                                </div>
                                                <div class="message-time">
                                                    @message.Timestamp.ToString("HH:mm")
                                                </div>
                                            </div>
                                            @if (!message.IsFromUser)
                                            {
                                                <div class="message-sender">
                                                    <small class="text-muted">
                                                        <i class="bi bi-person-circle me-1"></i>
                                                        @message.SenderName
                                                    </small>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="text-center py-5">
                                    <div class="mb-3">
                                        <i class="bi bi-chat-heart text-primary" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="text-muted">Bienvenue sur le support NafaPlace !</h5>
                                    <p class="text-muted">Comment pouvons-nous vous aider aujourd'hui ?</p>
                                </div>
                            }

                            @if (isTyping)
                            {
                                <div class="message-wrapper mb-3 text-start">
                                    <div class="d-inline-block">
                                        <div class="message support-message">
                                            <div class="typing-indicator">
                                                <span></span>
                                                <span></span>
                                                <span></span>
                                            </div>
                                        </div>
                                        <div class="message-sender">
                                            <small class="text-muted">
                                                <i class="bi bi-person-circle me-1"></i>
                                                Agent en train d'écrire...
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Zone de saisie -->
                    <div class="border-top p-3">
                        <div class="row g-2">
                            <div class="col">
                                <div class="input-group">
                                    <input type="text" class="form-control border-0 bg-light" 
                                           placeholder="Tapez votre message..." 
                                           @bind="currentMessage" 
                                           @onkeypress="HandleKeyPress"
                                           disabled="@isLoading">
                                    <button class="btn btn-outline-secondary" type="button" @onclick="AttachFile">
                                        <i class="bi bi-paperclip"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-auto">
                                <button class="btn btn-primary" @onclick="SendMessage" disabled="@(isLoading || string.IsNullOrWhiteSpace(currentMessage))">
                                    @if (isLoading)
                                    {
                                        <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                                    }
                                    else
                                    {
                                        <i class="bi bi-send"></i>
                                    }
                                    Envoyer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Suggestions rapides -->
                <div class="card-footer bg-light">
                    <div class="d-flex flex-wrap gap-2">
                        <small class="text-muted me-2">Suggestions rapides:</small>
                        @foreach (var suggestion in quickSuggestions)
                        {
                            <button class="btn btn-sm btn-outline-primary" @onclick="() => SendQuickMessage(suggestion)">
                                @suggestion
                            </button>
                        }
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-question-circle me-2"></i>
                        Questions Fréquentes
                    </h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        @for (int i = 0; i < faqItems.Count; i++)
                        {
                            var index = i;
                            var item = faqItems[index];
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading@(index)">
                                    <button class="accordion-button collapsed" type="button" 
                                            data-bs-toggle="collapse" data-bs-target="#collapse@(index)">
                                        @item.Question
                                    </button>
                                </h2>
                                <div id="collapse@(index)" class="accordion-collapse collapse" 
                                     data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        @item.Answer
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .user-message {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border-radius: 18px 18px 4px 18px;
        padding: 12px 16px;
        max-width: 300px;
        margin-left: auto;
    }

    .support-message {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 18px 18px 18px 4px;
        padding: 12px 16px;
        max-width: 300px;
    }

    .message-time {
        font-size: 0.75rem;
        opacity: 0.7;
        margin-top: 4px;
    }

    .message-sender {
        margin-top: 4px;
    }

    .typing-indicator {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .typing-indicator span {
        height: 8px;
        width: 8px;
        background: #6c757d;
        border-radius: 50%;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
    .typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

    @@keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }

    .chat-container {
        background: linear-gradient(to bottom, #ffffff, #f8f9fa);
    }
</style>

@code {
    private string currentMessage = "";
    private bool isLoading = false;
    private bool isTyping = false;
    private List<ChatMessage> messages = new();
    private List<string> quickSuggestions = new()
    {
        "Suivi de commande",
        "Retour produit", 
        "Problème de paiement",
        "Livraison",
        "Garantie"
    };

    private List<FaqItem> faqItems = new()
    {
        new FaqItem("Comment suivre ma commande ?", "Vous pouvez suivre votre commande en vous connectant à votre compte et en consultant la section 'Mes Commandes'."),
        new FaqItem("Quels sont les délais de livraison ?", "Les délais de livraison varient entre 2-5 jours ouvrables selon votre localisation."),
        new FaqItem("Comment retourner un produit ?", "Vous avez 14 jours pour retourner un produit. Contactez notre service client pour initier le retour."),
        new FaqItem("Quels moyens de paiement acceptez-vous ?", "Nous acceptons les cartes bancaires, Orange Money, et le paiement à la livraison.")
    };

    protected override async Task OnInitializedAsync()
    {
        // Message de bienvenue automatique
        await Task.Delay(1000);
        await AddSupportMessage("Bonjour ! Je suis votre assistant virtuel. Comment puis-je vous aider aujourd'hui ?", "Assistant IA");
    }

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !string.IsNullOrWhiteSpace(currentMessage))
        {
            await SendMessage();
        }
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(currentMessage) || isLoading) return;

        var message = currentMessage.Trim();
        currentMessage = "";
        isLoading = true;

        // Ajouter le message de l'utilisateur
        messages.Add(new ChatMessage
        {
            Content = message,
            IsFromUser = true,
            Timestamp = DateTime.Now,
            SenderName = "Vous"
        });

        StateHasChanged();
        await ScrollToBottom();

        // Simuler la frappe de l'agent
        isTyping = true;
        StateHasChanged();

        await Task.Delay(2000); // Simuler le temps de réponse

        isTyping = false;
        
        // Générer une réponse automatique
        var response = GenerateResponse(message);
        await AddSupportMessage(response, "Agent Support");

        isLoading = false;
        StateHasChanged();
    }

    private async Task SendQuickMessage(string message)
    {
        currentMessage = message;
        await SendMessage();
    }

    private async Task AddSupportMessage(string content, string senderName)
    {
        messages.Add(new ChatMessage
        {
            Content = content,
            IsFromUser = false,
            Timestamp = DateTime.Now,
            SenderName = senderName
        });

        StateHasChanged();
        await ScrollToBottom();
    }

    private string GenerateResponse(string userMessage)
    {
        var lowerMessage = userMessage.ToLower();
        
        if (lowerMessage.Contains("commande") || lowerMessage.Contains("suivi"))
            return "Pour suivre votre commande, rendez-vous dans votre espace client > Mes Commandes. Vous y trouverez le statut en temps réel et le numéro de suivi.";
        
        if (lowerMessage.Contains("retour") || lowerMessage.Contains("remboursement"))
            return "Vous pouvez retourner un produit dans les 14 jours suivant la réception. Allez dans 'Mes Commandes' et cliquez sur 'Retourner cet article'.";
        
        if (lowerMessage.Contains("paiement") || lowerMessage.Contains("carte"))
            return "Nous acceptons les cartes Visa/Mastercard, Orange Money, et le paiement à la livraison. Tous les paiements sont sécurisés.";
        
        if (lowerMessage.Contains("livraison") || lowerMessage.Contains("délai"))
            return "Nos délais de livraison sont de 2-5 jours ouvrables. La livraison est gratuite pour les commandes supérieures à 50.000 GNF.";
        
        if (lowerMessage.Contains("bonjour") || lowerMessage.Contains("salut"))
            return "Bonjour ! Je suis ravi de vous aider. Quelle est votre question aujourd'hui ?";
        
        return "Merci pour votre message. Un agent va vous répondre dans les plus brefs délais. En attendant, consultez notre FAQ ci-dessous pour des réponses rapides.";
    }

    private async Task ScrollToBottom()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("scrollToBottom", "chatContainer");
        }
        catch { }
    }

    private async Task AttachFile()
    {
        // TODO: Implémenter l'upload de fichiers
        await AddSupportMessage("La fonctionnalité d'envoi de fichiers sera bientôt disponible !", "Système");
    }

    public class ChatMessage
    {
        public string Content { get; set; } = "";
        public bool IsFromUser { get; set; }
        public DateTime Timestamp { get; set; }
        public string SenderName { get; set; } = "";
    }

    public class FaqItem
    {
        public string Question { get; set; }
        public string Answer { get; set; }

        public FaqItem(string question, string answer)
        {
            Question = question;
            Answer = answer;
        }
    }
}
