using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Analytics.Application.DTOs;
using NafaPlace.Analytics.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Analytics.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class AnalyticsController : ControllerBase
{
    private readonly IAnalyticsService _analyticsService;
    private readonly ILogger<AnalyticsController> _logger;

    public AnalyticsController(IAnalyticsService analyticsService, ILogger<AnalyticsController> logger)
    {
        _analyticsService = analyticsService;
        _logger = logger;
    }

    [HttpGet("dashboard")]
    public async Task<ActionResult<DashboardAnalyticsDto>> GetDashboardAnalytics(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int? sellerId = null,
        [FromQuery] int? categoryId = null,
        [FromQuery] string? region = null)
    {
        try
        {
            // Filtrer par vendeur si l'utilisateur n'est pas admin
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var filters = new AnalyticsFilterDto
            {
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow,
                SellerId = sellerId,
                CategoryId = categoryId,
                Region = region
            };

            var analytics = await _analyticsService.GetDashboardAnalyticsAsync(filters);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des analytics du dashboard");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("dashboard/seller/{sellerId}")]
    [Authorize(Roles = "Admin,Seller")]
    public async Task<ActionResult<DashboardAnalyticsDto>> GetSellerDashboardAnalytics(
        int sellerId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && userSellerId != sellerId.ToString())
            {
                return Forbid("Accès non autorisé aux analytics de ce vendeur");
            }

            var filters = new AnalyticsFilterDto
            {
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow,
                SellerId = sellerId
            };

            var analytics = await _analyticsService.GetSellerDashboardAnalyticsAsync(sellerId, filters);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des analytics vendeur {SellerId}", sellerId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("kpis")]
    public async Task<ActionResult<List<KPIDto>>> GetKPIs(
        [FromQuery] int? sellerId = null,
        [FromQuery] string[]? kpiNames = null)
    {
        try
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var kpis = await _analyticsService.GetKPIsAsync(sellerId, kpiNames?.ToList());
            return Ok(kpis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des KPIs");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("sales")]
    public async Task<ActionResult<SalesAnalyticsDto>> GetSalesAnalytics(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int? sellerId = null,
        [FromQuery] int? categoryId = null)
    {
        try
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var filters = new AnalyticsFilterDto
            {
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow,
                SellerId = sellerId,
                CategoryId = categoryId
            };

            var analytics = await _analyticsService.GetSalesAnalyticsAsync(filters);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des analytics de vente");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("products")]
    public async Task<ActionResult<ProductAnalyticsDto>> GetProductAnalytics(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int? sellerId = null,
        [FromQuery] int? categoryId = null)
    {
        try
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var filters = new AnalyticsFilterDto
            {
                StartDate = startDate,
                EndDate = endDate,
                SellerId = sellerId,
                CategoryId = categoryId
            };

            var analytics = await _analyticsService.GetProductAnalyticsAsync(filters);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des analytics produits");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("customers")]
    public async Task<ActionResult<CustomerAnalyticsDto>> GetCustomerAnalytics(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int? sellerId = null,
        [FromQuery] string? region = null)
    {
        try
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var filters = new AnalyticsFilterDto
            {
                StartDate = startDate,
                EndDate = endDate,
                SellerId = sellerId,
                Region = region
            };

            var analytics = await _analyticsService.GetCustomerAnalyticsAsync(filters);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des analytics clients");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("inventory")]
    public async Task<ActionResult<InventoryAnalyticsDto>> GetInventoryAnalytics(
        [FromQuery] int? sellerId = null,
        [FromQuery] int? categoryId = null)
    {
        try
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var filters = new AnalyticsFilterDto
            {
                SellerId = sellerId,
                CategoryId = categoryId
            };

            var analytics = await _analyticsService.GetInventoryAnalyticsAsync(filters);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des analytics inventaire");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("traffic")]
    public async Task<ActionResult<TrafficAnalyticsDto>> GetTrafficAnalytics(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var filters = new AnalyticsFilterDto
            {
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var analytics = await _analyticsService.GetTrafficAnalyticsAsync(filters);
            return Ok(analytics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des analytics de trafic");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("performance")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<PerformanceMetricsDto>> GetPerformanceMetrics()
    {
        try
        {
            var metrics = await _analyticsService.GetPerformanceMetricsAsync();
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des métriques de performance");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("top-sellers")]
    public async Task<ActionResult<List<TopSellerDto>>> GetTopSellers(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int limit = 10)
    {
        try
        {
            var filters = new AnalyticsFilterDto
            {
                StartDate = startDate,
                EndDate = endDate
            };

            var topSellers = await _analyticsService.GetTopSellersAsync(filters, limit);
            return Ok(topSellers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des top vendeurs");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("top-products")]
    public async Task<ActionResult<List<TopProductDto>>> GetTopProducts(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int? sellerId = null,
        [FromQuery] int limit = 10)
    {
        try
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var filters = new AnalyticsFilterDto
            {
                StartDate = startDate,
                EndDate = endDate,
                SellerId = sellerId
            };

            var topProducts = await _analyticsService.GetTopProductsAsync(filters, limit);
            return Ok(topProducts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des top produits");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("revenue-timeseries")]
    public async Task<ActionResult<List<TimeSeriesDataDto>>> GetRevenueTimeSeries(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int? sellerId = null,
        [FromQuery] string timeGranularity = "day")
    {
        try
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var filters = new AnalyticsFilterDto
            {
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow,
                SellerId = sellerId,
                TimeGranularity = timeGranularity
            };

            var timeSeries = await _analyticsService.GetRevenueTimeSeriesAsync(filters);
            return Ok(timeSeries);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des séries temporelles de revenus");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("recent-activities")]
    public async Task<ActionResult<List<RecentActivityDto>>> GetRecentActivities(
        [FromQuery] int limit = 20,
        [FromQuery] int? sellerId = null)
    {
        try
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            var userSellerId = User.FindFirst("SellerId")?.Value;

            if (userRole != "Admin" && !string.IsNullOrEmpty(userSellerId))
            {
                sellerId = int.Parse(userSellerId);
            }

            var activities = await _analyticsService.GetRecentActivitiesAsync(limit, sellerId);
            return Ok(activities);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des activités récentes");
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }

    [HttpGet("product/{productId}/insights")]
    public async Task<ActionResult<Dictionary<string, object>>> GetProductInsights(
        int productId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var filters = new AnalyticsFilterDto
            {
                StartDate = startDate,
                EndDate = endDate
            };

            var insights = await _analyticsService.GetProductInsightsAsync(productId, filters);
            return Ok(insights);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des insights produit {ProductId}", productId);
            return StatusCode(500, new { error = "Erreur interne du serveur" });
        }
    }
}
