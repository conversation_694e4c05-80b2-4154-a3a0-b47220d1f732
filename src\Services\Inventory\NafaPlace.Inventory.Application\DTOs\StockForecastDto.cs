using NafaPlace.Inventory.Domain.Enums;

namespace NafaPlace.Inventory.Application.DTOs;

public class StockForecastDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public int CurrentStock { get; set; }
    public int PredictedDemand { get; set; }
    public int RecommendedReorderQuantity { get; set; }
    public DateTime PredictedStockoutDate { get; set; }
    public int DaysUntilStockout { get; set; }
    public ForecastAccuracy Accuracy { get; set; }
    public List<DemandPredictionDto> DailyPredictions { get; set; } = new();
    public StockRecommendationDto Recommendation { get; set; } = new();
}

public class DemandPredictionDto
{
    public DateTime Date { get; set; }
    public int PredictedDemand { get; set; }
    public int PredictedStock { get; set; }
    public double Confidence { get; set; }
}

public class StockRecommendationDto
{
    public RecommendationType Type { get; set; }
    public string Message { get; set; } = string.Empty;
    public int SuggestedQuantity { get; set; }
    public DateTime SuggestedOrderDate { get; set; }
    public decimal EstimatedCost { get; set; }
    public Priority Priority { get; set; }
}

public class ForecastParametersDto
{
    public int ProductId { get; set; }
    public int ForecastDays { get; set; } = 30;
    public int HistoryDays { get; set; } = 90;
    public bool IncludeSeasonality { get; set; } = true;
    public bool IncludeTrends { get; set; } = true;
    public double SafetyStockPercentage { get; set; } = 0.2; // 20% de stock de sécurité
}

public class BulkForecastRequestDto
{
    public List<int>? ProductIds { get; set; }
    public int? SellerId { get; set; }
    public int? CategoryId { get; set; }
    public ForecastParametersDto Parameters { get; set; } = new();
    public bool OnlyLowStock { get; set; } = false;
}

public class ForecastSummaryDto
{
    public int TotalProducts { get; set; }
    public int ProductsNeedingReorder { get; set; }
    public int ProductsAtRisk { get; set; }
    public decimal TotalRecommendedOrderValue { get; set; }
    public List<StockForecastDto> CriticalProducts { get; set; } = new();
    public List<StockForecastDto> ReorderRecommendations { get; set; } = new();
}

public enum ForecastAccuracy
{
    Low,
    Medium,
    High,
    VeryHigh
}

public enum RecommendationType
{
    NoAction,
    Monitor,
    Reorder,
    UrgentReorder,
    ReduceStock
}

public enum Priority
{
    Low,
    Medium,
    High,
    Critical
}
