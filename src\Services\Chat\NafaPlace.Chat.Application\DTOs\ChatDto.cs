using NafaPlace.Chat.Domain.Enums;

namespace NafaPlace.Chat.Application.DTOs;

public class ChatConversationDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public ConversationType Type { get; set; }
    public ConversationStatus Status { get; set; }
    public ConversationPriority Priority { get; set; }
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string? CustomerEmail { get; set; }
    public string? AssignedAgentId { get; set; }
    public string? AssignedAgentName { get; set; }
    public string? DepartmentId { get; set; }
    public string? DepartmentName { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? ClosedAt { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public int MessageCount { get; set; }
    public int UnreadCount { get; set; }
    public TimeSpan? ResponseTime { get; set; }
    public TimeSpan? ResolutionTime { get; set; }
    public int? SatisfactionRating { get; set; }
    public string? SatisfactionComment { get; set; }
}

public class ChatMessageDto
{
    public int Id { get; set; }
    public int ConversationId { get; set; }
    public string SenderId { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public MessageType Type { get; set; }
    public string Content { get; set; } = string.Empty;
    public List<MessageAttachmentDto> Attachments { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public DateTime SentAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public bool IsRead { get; set; }
    public bool IsFromAgent { get; set; }
    public bool IsFromBot { get; set; }
    public string? ReplyToMessageId { get; set; }
    public MessageStatus Status { get; set; }
}

public class MessageAttachmentDto
{
    public int Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileUrl { get; set; } = string.Empty;
    public string? ThumbnailUrl { get; set; }
    public DateTime UploadedAt { get; set; }
}

public class ChatAgentDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Avatar { get; set; }
    public AgentStatus Status { get; set; }
    public List<string> Departments { get; set; } = new();
    public List<string> Skills { get; set; } = new();
    public List<string> Languages { get; set; } = new();
    public int MaxConcurrentChats { get; set; } = 5;
    public int CurrentChatCount { get; set; }
    public DateTime? LastActiveAt { get; set; }
    public AgentWorkingHours WorkingHours { get; set; } = new();
    public Dictionary<string, object> Settings { get; set; } = new();
    public DateTime CreatedAt { get; set; }
}

public class AgentWorkingHours
{
    public Dictionary<DayOfWeek, WorkingDay> Schedule { get; set; } = new();
    public string TimeZone { get; set; } = "UTC";
}

public class WorkingDay
{
    public bool IsWorkingDay { get; set; }
    public TimeSpan StartTime { get; set; }
    public TimeSpan EndTime { get; set; }
    public List<BreakPeriod> Breaks { get; set; } = new();
}

public class BreakPeriod
{
    public TimeSpan StartTime { get; set; }
    public TimeSpan EndTime { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class ChatDepartmentDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? Color { get; set; }
    public string? Icon { get; set; }
    public List<string> AgentIds { get; set; } = new();
    public List<string> Skills { get; set; } = new();
    public ChatRoutingRules RoutingRules { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public int Priority { get; set; }
    public Dictionary<string, object> Settings { get; set; } = new();
}

public class ChatRoutingRules
{
    public RoutingStrategy Strategy { get; set; } = RoutingStrategy.RoundRobin;
    public List<RoutingCondition> Conditions { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class RoutingCondition
{
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
}

public class CreateConversationDto
{
    public string Title { get; set; } = string.Empty;
    public ConversationType Type { get; set; } = ConversationType.Support;
    public ConversationPriority Priority { get; set; } = ConversationPriority.Normal;
    public string CustomerId { get; set; } = string.Empty;
    public string? DepartmentId { get; set; }
    public string? AssignedAgentId { get; set; }
    public List<string> Tags { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string? InitialMessage { get; set; }
}

public class SendMessageDto
{
    public int ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType Type { get; set; } = MessageType.Text;
    public List<MessageAttachmentDto> Attachments { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string? ReplyToMessageId { get; set; }
}

public class ChatFilterDto
{
    public ConversationStatus? Status { get; set; }
    public ConversationType? Type { get; set; }
    public ConversationPriority? Priority { get; set; }
    public string? AssignedAgentId { get; set; }
    public string? DepartmentId { get; set; }
    public string? CustomerId { get; set; }
    public List<string>? Tags { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public bool? HasUnreadMessages { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortBy { get; set; } = "UpdatedAt";
    public bool SortDescending { get; set; } = true;
}

public class ChatStatsDto
{
    public int TotalConversations { get; set; }
    public int ActiveConversations { get; set; }
    public int PendingConversations { get; set; }
    public int ClosedConversations { get; set; }
    public int TotalMessages { get; set; }
    public double AverageResponseTime { get; set; }
    public double AverageResolutionTime { get; set; }
    public double CustomerSatisfactionScore { get; set; }
    public int OnlineAgents { get; set; }
    public int BusyAgents { get; set; }
    public Dictionary<string, int> ConversationsByDepartment { get; set; } = new();
    public Dictionary<string, int> ConversationsByPriority { get; set; } = new();
    public Dictionary<string, double> ResponseTimeByAgent { get; set; } = new();
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class AgentPerformanceDto
{
    public string AgentId { get; set; } = string.Empty;
    public string AgentName { get; set; } = string.Empty;
    public int TotalConversations { get; set; }
    public int ActiveConversations { get; set; }
    public int ClosedConversations { get; set; }
    public int TotalMessages { get; set; }
    public double AverageResponseTime { get; set; }
    public double AverageResolutionTime { get; set; }
    public double CustomerSatisfactionScore { get; set; }
    public int SatisfactionRatings { get; set; }
    public TimeSpan TotalOnlineTime { get; set; }
    public TimeSpan TotalActiveTime { get; set; }
    public Dictionary<string, int> ConversationsByStatus { get; set; } = new();
    public List<string> TopTags { get; set; } = new();
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class ConversationSummaryDto
{
    public int ConversationId { get; set; }
    public string Summary { get; set; } = string.Empty;
    public List<string> KeyTopics { get; set; } = new();
    public List<string> ActionItems { get; set; } = new();
    public ConversationSentiment Sentiment { get; set; }
    public List<string> SuggestedTags { get; set; } = new();
    public string? Resolution { get; set; }
    public DateTime GeneratedAt { get; set; }
    public string GeneratedBy { get; set; } = string.Empty;
}

public class ChatNotificationDto
{
    public string Type { get; set; } = string.Empty;
    public int ConversationId { get; set; }
    public string? MessageId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class TypingIndicatorDto
{
    public int ConversationId { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public bool IsTyping { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class ChatPresenceDto
{
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public UserPresenceStatus Status { get; set; }
    public DateTime LastSeen { get; set; }
    public List<int> ActiveConversations { get; set; } = new();
}

// Enums
public enum ConversationType
{
    Support,
    Sales,
    Technical,
    Billing,
    General,
    Complaint,
    Feedback
}

public enum ConversationStatus
{
    Open,
    Pending,
    InProgress,
    Waiting,
    Resolved,
    Closed,
    Archived
}

public enum ConversationPriority
{
    Low,
    Normal,
    High,
    Urgent,
    Critical
}

public enum MessageType
{
    Text,
    Image,
    File,
    Audio,
    Video,
    System,
    Bot,
    QuickReply,
    Card,
    Carousel
}

public enum MessageStatus
{
    Sent,
    Delivered,
    Read,
    Failed
}

public enum AgentStatus
{
    Online,
    Away,
    Busy,
    Offline,
    DoNotDisturb
}

public enum RoutingStrategy
{
    RoundRobin,
    LeastBusy,
    MostExperienced,
    SkillBased,
    Random,
    Manual
}

public enum ConversationSentiment
{
    Positive,
    Neutral,
    Negative,
    Mixed,
    Unknown
}

public enum UserPresenceStatus
{
    Online,
    Away,
    Offline
}
