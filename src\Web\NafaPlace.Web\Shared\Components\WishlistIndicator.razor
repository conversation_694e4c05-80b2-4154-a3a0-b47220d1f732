@using NafaPlace.Web.Services
@using System.Security.Claims
@using Microsoft.AspNetCore.Components.Authorization
@inject IWishlistService WishlistService
@inject IJSRuntime JSRuntime
@implements IDisposable

<a href="/wishlist" class="action-item">
    <i class="bi bi-heart action-icon"></i>
    <span class="action-label">Favoris</span>
    <span class="action-badge">@_itemCount</span>
</a>

@code {
    private int _itemCount = 0;
    private string? _userId;
    private Timer? _refreshTimer;
    private bool _isUpdating = false;
    private bool _disposed = false;

    [CascadingParameter]
    private Task<AuthenticationState>? AuthenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            if (AuthenticationStateTask != null)
            {
                var authState = await AuthenticationStateTask;
                var user = authState.User;

                if (user.Identity?.IsAuthenticated == true)
                {
                    _userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                    Console.WriteLine($"🔍 DEBUG WishlistIndicator: Utilisateur authentifié avec ID: {_userId}");
                }
                else
                {
                    Console.WriteLine("🔍 DEBUG WishlistIndicator: Utilisateur non authentifié");
                    _userId = await GetOrCreateGuestUserId();
                    Console.WriteLine($"🔍 DEBUG WishlistIndicator: ID invité créé: {_userId}");
                }
            }
            else
            {
                Console.WriteLine("⚠️ DEBUG WishlistIndicator: AuthenticationStateTask est null");
                _userId = await GetOrCreateGuestUserId();
            }

            await UpdateWishlistCount();

            // Mettre à jour le compteur toutes les 2 minutes (réduire la fréquence)
            _refreshTimer = new Timer(async _ =>
            {
                if (!_disposed)
                {
                    await UpdateWishlistCount();
                }
            }, null, TimeSpan.Zero, TimeSpan.FromMinutes(2));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'initialisation du WishlistIndicator: {ex.Message}");
        }
    }

    private async Task UpdateWishlistCount()
    {
        // Éviter les appels simultanés
        if (_isUpdating || _disposed)
        {
            return;
        }

        _isUpdating = true;

        try
        {
            if (string.IsNullOrEmpty(_userId))
            {
                // Essayer de récupérer l'ID invité
                _userId = await GetOrCreateGuestUserId();
            }

            var count = await WishlistService.GetWishlistItemCountAsync(_userId);

            if (count != _itemCount && !_disposed)
            {
                _itemCount = count;
                // Utiliser InvokeAsync seulement si nécessaire
                if (!_disposed)
                {
                    await InvokeAsync(() =>
                    {
                        if (!_disposed)
                        {
                            StateHasChanged();
                        }
                    });
                }
            }
        }
        catch (Exception ex)
        {
            // Réduire les logs pour éviter le spam
            Console.WriteLine($"Erreur lors de la mise à jour du compteur de wishlist: {ex.Message}");
        }
        finally
        {
            _isUpdating = false;
        }
    }

    public async Task RefreshWishlistCount()
    {
        await UpdateWishlistCount();
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Random.Shared.Next(1, int.MaxValue)}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }

    public void Dispose()
    {
        _disposed = true;
        _refreshTimer?.Dispose();
        _refreshTimer = null;
    }
}
