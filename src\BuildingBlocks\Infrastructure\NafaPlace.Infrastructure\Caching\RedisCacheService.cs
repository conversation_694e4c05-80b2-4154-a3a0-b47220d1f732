using Microsoft.Extensions.Logging;
using NafaPlace.Common.CQRS;
using StackExchange.Redis;
using System.Text.Json;

namespace NafaPlace.Infrastructure.Caching;

/// <summary>
/// Service de cache Redis avancé avec support des patterns et compression
/// </summary>
public class RedisCacheService : ICacheService
{
    private readonly IDatabase _database;
    private readonly ILogger<RedisCacheService> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public RedisCacheService(IConnectionMultiplexer redis, ILogger<RedisCacheService> logger)
    {
        _database = redis.GetDatabase();
        _logger = logger;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var value = await _database.StringGetAsync(key);
            
            if (!value.HasValue)
            {
                _logger.LogDebug("Cache miss for key: {Key}", key);
                return default;
            }

            _logger.LogDebug("Cache hit for key: {Key}", key);
            return JsonSerializer.Deserialize<T>(value!, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting value from cache for key: {Key}", key);
            return default;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan expiration, CancellationToken cancellationToken = default)
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            await _database.StringSetAsync(key, serializedValue, expiration);
            
            _logger.LogDebug("Cached value for key: {Key} with expiration: {Expiration}", key, expiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting value in cache for key: {Key}", key);
        }
    }

    public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            await _database.KeyDeleteAsync(key);
            _logger.LogDebug("Removed key from cache: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing key from cache: {Key}", key);
        }
    }

    public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        try
        {
            var server = _database.Multiplexer.GetServer(_database.Multiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern).ToArray();
            
            if (keys.Length > 0)
            {
                await _database.KeyDeleteAsync(keys);
                _logger.LogDebug("Removed {Count} keys matching pattern: {Pattern}", keys.Length, pattern);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing keys by pattern: {Pattern}", pattern);
        }
    }

    /// <summary>
    /// Obtient ou définit une valeur avec une fonction de récupération
    /// </summary>
    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getItem, TimeSpan expiration, CancellationToken cancellationToken = default)
    {
        var cachedValue = await GetAsync<T>(key, cancellationToken);
        
        if (cachedValue != null)
        {
            return cachedValue;
        }

        var item = await getItem();
        await SetAsync(key, item, expiration, cancellationToken);
        
        return item;
    }

    /// <summary>
    /// Incrémente une valeur numérique dans le cache
    /// </summary>
    public async Task<long> IncrementAsync(string key, long value = 1, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _database.StringIncrementAsync(key, value);
            
            if (expiration.HasValue)
            {
                await _database.KeyExpireAsync(key, expiration.Value);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing key: {Key}", key);
            throw;
        }
    }

    /// <summary>
    /// Décrémente une valeur numérique dans le cache
    /// </summary>
    public async Task<long> DecrementAsync(string key, long value = 1, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.StringDecrementAsync(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error decrementing key: {Key}", key);
            throw;
        }
    }

    /// <summary>
    /// Vérifie si une clé existe dans le cache
    /// </summary>
    public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.KeyExistsAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if key exists: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Définit l'expiration d'une clé existante
    /// </summary>
    public async Task<bool> ExpireAsync(string key, TimeSpan expiration, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.KeyExpireAsync(key, expiration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting expiration for key: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Obtient le temps de vie restant d'une clé
    /// </summary>
    public async Task<TimeSpan?> GetTtlAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.KeyTimeToLiveAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting TTL for key: {Key}", key);
            return null;
        }
    }

    /// <summary>
    /// Ajoute un élément à une liste
    /// </summary>
    public async Task<long> ListPushAsync<T>(string key, T value, CancellationToken cancellationToken = default)
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await _database.ListLeftPushAsync(key, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error pushing to list: {Key}", key);
            throw;
        }
    }

    /// <summary>
    /// Récupère et supprime un élément d'une liste
    /// </summary>
    public async Task<T?> ListPopAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var value = await _database.ListLeftPopAsync(key);
            
            if (!value.HasValue)
            {
                return default;
            }

            return JsonSerializer.Deserialize<T>(value!, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error popping from list: {Key}", key);
            return default;
        }
    }

    /// <summary>
    /// Obtient la longueur d'une liste
    /// </summary>
    public async Task<long> ListLengthAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _database.ListLengthAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting list length: {Key}", key);
            return 0;
        }
    }

    /// <summary>
    /// Ajoute un élément à un ensemble
    /// </summary>
    public async Task<bool> SetAddAsync<T>(string key, T value, CancellationToken cancellationToken = default)
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await _database.SetAddAsync(key, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding to set: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Vérifie si un élément existe dans un ensemble
    /// </summary>
    public async Task<bool> SetContainsAsync<T>(string key, T value, CancellationToken cancellationToken = default)
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await _database.SetContainsAsync(key, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking set membership: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Supprime un élément d'un ensemble
    /// </summary>
    public async Task<bool> SetRemoveAsync<T>(string key, T value, CancellationToken cancellationToken = default)
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
            return await _database.SetRemoveAsync(key, serializedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing from set: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// Obtient tous les éléments d'un ensemble
    /// </summary>
    public async Task<List<T>> SetMembersAsync<T>(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var values = await _database.SetMembersAsync(key);
            var result = new List<T>();
            
            foreach (var value in values)
            {
                if (value.HasValue)
                {
                    var item = JsonSerializer.Deserialize<T>(value!, _jsonOptions);
                    if (item != null)
                    {
                        result.Add(item);
                    }
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting set members: {Key}", key);
            return new List<T>();
        }
    }
}
