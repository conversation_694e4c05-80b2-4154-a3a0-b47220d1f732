using Microsoft.EntityFrameworkCore;
using NafaPlace.Notifications.Application.DTOs;
using NafaPlace.Notifications.Application.Interfaces;
using NafaPlace.Notifications.Domain.Models;

namespace NafaPlace.Notifications.Application.Services;

public class NotificationService : INotificationService
{
    private readonly INotificationsDbContext _context;

    public NotificationService(INotificationsDbContext context)
    {
        _context = context;
    }

    public async Task<NotificationDto> CreateNotificationAsync(CreateNotificationRequest request)
    {
        var notification = new Notification
        {
            UserId = request.UserId,
            Title = request.Title,
            Message = request.Message,
            Type = request.Type,
            Data = request.Data,
            ExpiresAt = request.ExpiresAt,
            Priority = request.Priority,
            ActionUrl = request.ActionUrl,
            ImageUrl = request.ImageUrl,
            CreatedAt = DateTime.UtcNow
        };

        _context.Notifications.Add(notification);
        await _context.SaveChangesAsync();

        return MapToDto(notification);
    }

    public async Task<List<NotificationDto>> CreateBulkNotificationAsync(BulkNotificationRequest request)
    {
        var notifications = request.UserIds.Select(userId => new Notification
        {
            UserId = userId,
            Title = request.Title,
            Message = request.Message,
            Type = request.Type,
            Data = request.Data,
            ExpiresAt = request.ExpiresAt,
            Priority = request.Priority,
            ActionUrl = request.ActionUrl,
            ImageUrl = request.ImageUrl,
            CreatedAt = DateTime.UtcNow
        }).ToList();

        _context.Notifications.AddRange(notifications);
        await _context.SaveChangesAsync();

        return notifications.Select(MapToDto).ToList();
    }

    public async Task<NotificationsPagedResult> GetUserNotificationsAsync(string userId, int page = 1, int pageSize = 20, bool unreadOnly = false)
    {
        var query = _context.Notifications
            .Where(n => n.UserId == userId && (n.ExpiresAt == null || n.ExpiresAt > DateTime.UtcNow));

        if (unreadOnly)
        {
            query = query.Where(n => !n.IsRead);
        }

        var totalCount = await query.CountAsync();
        var unreadCount = await _context.Notifications
            .CountAsync(n => n.UserId == userId && !n.IsRead && (n.ExpiresAt == null || n.ExpiresAt > DateTime.UtcNow));

        var notifications = await query
            .OrderByDescending(n => n.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new NotificationsPagedResult
        {
            Notifications = notifications.Select(MapToDto).ToList(),
            TotalCount = totalCount,
            UnreadCount = unreadCount,
            PageNumber = page,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }

    public async Task<NotificationDto?> GetNotificationByIdAsync(int id)
    {
        var notification = await _context.Notifications.FindAsync(id);
        return notification == null ? null : MapToDto(notification);
    }

    public async Task<bool> MarkAsReadAsync(int id, string userId)
    {
        var notification = await _context.Notifications
            .FirstOrDefaultAsync(n => n.Id == id && n.UserId == userId);

        if (notification == null) return false;

        notification.IsRead = true;
        notification.ReadAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<int> MarkAllAsReadAsync(string userId)
    {
        var notifications = await _context.Notifications
            .Where(n => n.UserId == userId && !n.IsRead)
            .ToListAsync();

        foreach (var notification in notifications)
        {
            notification.IsRead = true;
            notification.ReadAt = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();
        return notifications.Count;
    }

    public async Task<bool> DeleteNotificationAsync(int id, string userId)
    {
        var notification = await _context.Notifications
            .FirstOrDefaultAsync(n => n.Id == id && n.UserId == userId);

        if (notification == null) return false;

        _context.Notifications.Remove(notification);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<int> DeleteAllReadAsync(string userId)
    {
        var notifications = await _context.Notifications
            .Where(n => n.UserId == userId && n.IsRead)
            .ToListAsync();

        _context.Notifications.RemoveRange(notifications);
        await _context.SaveChangesAsync();

        return notifications.Count;
    }

    public async Task<NotificationStatsDto> GetUserStatsAsync(string userId)
    {
        var totalNotifications = await _context.Notifications
            .CountAsync(n => n.UserId == userId);

        var unreadNotifications = await _context.Notifications
            .CountAsync(n => n.UserId == userId && !n.IsRead);

        var todayNotifications = await _context.Notifications
            .CountAsync(n => n.UserId == userId && n.CreatedAt.Date == DateTime.UtcNow.Date);

        var notificationsByType = await _context.Notifications
            .Where(n => n.UserId == userId)
            .GroupBy(n => n.Type)
            .Select(g => new { Type = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Type, x => x.Count);

        return new NotificationStatsDto
        {
            TotalNotifications = totalNotifications,
            UnreadNotifications = unreadNotifications,
            TodayNotifications = todayNotifications,
            NotificationsByType = notificationsByType
        };
    }

    public async Task<List<NotificationDto>> GetRecentNotificationsAsync(string userId, int count = 5)
    {
        var notifications = await _context.Notifications
            .Where(n => n.UserId == userId && (n.ExpiresAt == null || n.ExpiresAt > DateTime.UtcNow))
            .OrderByDescending(n => n.CreatedAt)
            .Take(count)
            .ToListAsync();

        return notifications.Select(MapToDto).ToList();
    }

    public async Task<NotificationTemplateDto> CreateTemplateAsync(CreateNotificationTemplateRequest request)
    {
        var template = new NotificationTemplate
        {
            Name = request.Name,
            Code = request.Code,
            Subject = request.Subject,
            Body = request.Body,
            Language = request.Language,
            TitleTemplate = request.TitleTemplate,
            MessageTemplate = request.MessageTemplate,
            Type = request.Type,
            Channel = request.Channel,
            Priority = request.Priority,
            Description = request.Description,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.NotificationTemplates.Add(template);
        await _context.SaveChangesAsync();

        return MapToDto(template);
    }

    public async Task<List<NotificationTemplateDto>> GetTemplatesAsync()
    {
        var templates = await _context.NotificationTemplates
            .Where(t => t.IsActive)
            .OrderBy(t => t.Name)
            .ToListAsync();

        return templates.Select(MapToDto).ToList();
    }

    public async Task<NotificationDto> CreateFromTemplateAsync(int templateId, string userId, Dictionary<string, string> parameters)
    {
        var template = await _context.NotificationTemplates.FindAsync(templateId);
        if (template == null || !template.IsActive)
        {
            throw new ArgumentException("Template not found or inactive");
        }

        var title = template.TitleTemplate ?? string.Empty;
        var message = template.MessageTemplate ?? string.Empty;

        foreach (var param in parameters)
        {
            title = title.Replace($"{{{param.Key}}}", param.Value);
            message = message.Replace($"{{{param.Key}}}", param.Value);
        }

        var request = new CreateNotificationRequest
        {
            UserId = userId,
            Title = title,
            Message = message,
            Type = template.Type,
            Priority = template.Priority
        };

        return await CreateNotificationAsync(request);
    }

    // System notification methods
    public async Task NotifyOrderStatusChangeAsync(string userId, string orderId, string status)
    {
        await CreateFromTemplateAsync(1, userId, new Dictionary<string, string>
        {
            { "OrderId", orderId },
            { "Status", status }
        });
    }

    public async Task NotifyPaymentStatusChangeAsync(string userId, string orderId, string status)
    {
        await CreateFromTemplateAsync(2, userId, new Dictionary<string, string>
        {
            { "OrderId", orderId },
            { "Status", status }
        });
    }

    public async Task NotifyNewReviewAsync(string sellerId, string productName, int rating)
    {
        await CreateFromTemplateAsync(3, sellerId, new Dictionary<string, string>
        {
            { "ProductName", productName },
            { "Rating", rating.ToString() }
        });
    }

    public async Task NotifyLowStockAsync(string sellerId, string productName, int currentStock)
    {
        await CreateFromTemplateAsync(4, sellerId, new Dictionary<string, string>
        {
            { "ProductName", productName },
            { "CurrentStock", currentStock.ToString() }
        });
    }

    private static NotificationDto MapToDto(Notification notification)
    {
        return new NotificationDto
        {
            Id = notification.Id,
            UserId = notification.UserId,
            Title = notification.Title,
            Message = notification.Message,
            Type = notification.Type,
            Data = notification.Data,
            IsRead = notification.IsRead,
            CreatedAt = notification.CreatedAt,
            ReadAt = notification.ReadAt,
            ExpiresAt = notification.ExpiresAt,
            Priority = notification.Priority,
            ActionUrl = notification.ActionUrl,
            ImageUrl = notification.ImageUrl
        };
    }

    private static NotificationTemplateDto MapToDto(NotificationTemplate template)
    {
        return new NotificationTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            Code = template.Code,
            Channel = template.Channel,
            Subject = template.Subject,
            Body = template.Body,
            TitleTemplate = template.TitleTemplate ?? string.Empty,
            MessageTemplate = template.MessageTemplate ?? string.Empty,
            Type = template.Type,
            Priority = template.Priority,
            Language = template.Language,
            Variables = template.Variables,
            Description = template.Description,
            IsActive = template.IsActive,
            CreatedAt = template.CreatedAt,
            UpdatedAt = template.UpdatedAt
        };
    }
}
