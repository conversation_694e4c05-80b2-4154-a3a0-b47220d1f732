using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Domain.Models;

namespace NafaPlace.Reviews.Application.Services;

public partial class ReviewService : IReviewService
{
    // Admin-specific operations
    
    public async Task<AdminReviewStatsDto> GetAdminReviewStatsAsync()
    {
        var totalReviews = await _reviewRepository.GetTotalReviewsCountAsync();
        var pendingReviews = await _reviewRepository.GetPendingReviewsCountAsync();
        var approvedReviews = await _reviewRepository.GetApprovedReviewsCountAsync();
        var rejectedReviews = await _reviewRepository.GetRejectedReviewsCountAsync();
        var reportedReviews = await _reviewRepository.GetReportedReviewsCountAsync();
        var averageRating = await _reviewRepository.GetOverallAverageRatingAsync();
        var ratingDistribution = await _reviewRepository.GetOverallRatingDistributionAsync();
        var reviewsByPeriod = await _reviewRepository.GetReviewsByPeriodAsync();
        
        return new AdminReviewStatsDto
        {
            TotalReviews = totalReviews,
            PendingReviews = pendingReviews,
            ApprovedReviews = approvedReviews,
            RejectedReviews = rejectedReviews,
            ReportedReviews = reportedReviews,
            AverageRating = averageRating,
            RatingDistribution = ratingDistribution,
            ReviewsByPeriod = reviewsByPeriod
        };
    }
    
    public async Task<AdminReviewsPagedResponse> GetAdminReviewsAsync(AdminReviewFilterRequest request)
    {
        var reviews = await _reviewRepository.GetAdminReviewsAsync(
            request.Status,
            request.Rating,
            request.IsVerifiedPurchase,
            request.DateFilter,
            request.SearchTerm,
            request.Page,
            request.PageSize);
            
        var totalCount = await _reviewRepository.CountAdminReviewsAsync(
            request.Status,
            request.Rating,
            request.IsVerifiedPurchase,
            request.DateFilter,
            request.SearchTerm);
            
        var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);
        
        var adminReviews = new List<AdminReviewDto>();
        
        foreach (var review in reviews)
        {
            // Get product info from Catalog API
            var productInfo = await _productInfoService.GetProductInfoAsync(review.ProductId);

            var adminReview = new AdminReviewDto
            {
                Id = review.Id,
                ProductId = review.ProductId,
                ProductName = productInfo?.Name ?? "Produit inconnu",
                ProductImageUrl = productInfo?.ImageUrl ?? "/images/products/default.jpg",
                SellerId = productInfo?.SellerId.ToString() ?? "unknown",
                SellerName = productInfo?.SellerName ?? "Vendeur inconnu",
                UserId = review.UserId,
                UserName = review.UserName,
                UserEmail = $"{review.UserName.ToLower().Replace(" ", "")}@email.com", // Temporary
                Rating = review.Rating,
                Title = review.Title,
                Comment = review.Comment,
                IsApproved = review.Status switch
                {
                    ReviewStatus.Published => true,
                    ReviewStatus.Rejected => false,
                    _ => null
                },
                IsVerifiedPurchase = review.IsVerifiedPurchase,
                HelpfulCount = review.HelpfulCount,
                ReportCount = review.ReportCount,
                CreatedAt = review.CreatedAt,
                UpdatedAt = review.UpdatedAt,
                ApprovedAt = review.Status == ReviewStatus.Published ? review.UpdatedAt : null,
                ApprovedBy = review.Status == ReviewStatus.Published ? "admin" : null,
                RejectionReason = review.Status == ReviewStatus.Rejected ? "Avis non conforme" : null
            };
            
            adminReviews.Add(adminReview);
        }
        
        return new AdminReviewsPagedResponse
        {
            Reviews = adminReviews,
            TotalCount = totalCount,
            TotalPages = totalPages,
            CurrentPage = request.Page,
            PageSize = request.PageSize
        };
    }

}
