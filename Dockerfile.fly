# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier la solution et tous les fichiers de projet
COPY . .

# Restaurer les dépendances
RUN dotnet restore "NafaPlace.sln"

# Build la solution complète
RUN dotnet build "NafaPlace.sln" -c Release

# Publish all microservices
FROM build AS publish-identity
WORKDIR "/src/src/Services/Identity/NafaPlace.Identity.API"
RUN dotnet publish "NafaPlace.Identity.API.csproj" -c Release -o /app/identity /p:UseAppHost=false

FROM build AS publish-catalog
WORKDIR "/src/src/Services/Catalog/NafaPlace.Catalog.API/NafaPlace.Catalog.API"
RUN dotnet publish "NafaPlace.Catalog.API.csproj" -c Release -o /app/catalog /p:UseAppHost=false

FROM build AS publish-cart
WORKDIR "/src/src/Services/Cart/NafaPlace.Cart.API"
RUN dotnet publish "NafaPlace.Cart.API.csproj" -c Release -o /app/cart /p:UseAppHost=false

FROM build AS publish-order
WORKDIR "/src/src/Services/Order/NafaPlace.Order.API"
RUN dotnet publish "NafaPlace.Order.API.csproj" -c Release -o /app/order /p:UseAppHost=false

FROM build AS publish-payment
WORKDIR "/src/src/Services/Payment/NafaPlace.Payment.API"
RUN dotnet publish "NafaPlace.Payment.API.csproj" -c Release -o /app/payment /p:UseAppHost=false

FROM build AS publish-gateway
WORKDIR "/src/src/ApiGateways/Web/NafaPlace.ApiGateway"
RUN dotnet publish "NafaPlace.ApiGateway.csproj" -c Release -o /app/gateway /p:UseAppHost=false

FROM build AS publish-chat
WORKDIR "/src/src/Services/Chat/NafaPlace.Chat.API"
RUN dotnet publish "NafaPlace.Chat.API.csproj" -c Release -o /app/chat /p:UseAppHost=false

# Final stage - Multi-service container with supervisor
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app

# Install supervisor and nginx for managing multiple processes and serving static files
RUN apt-get update && apt-get install -y supervisor nginx && rm -rf /var/lib/apt/lists/*

# Configuration pour le marché africain
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
ENV TZ=Africa/Conakry
ENV LANG=fr_GN.UTF-8
ENV LANGUAGE=fr_GN.UTF-8
ENV LC_ALL=fr_GN.UTF-8

# Copy all published applications
COPY --from=publish-identity /app/identity ./identity/
COPY --from=publish-catalog /app/catalog ./catalog/
COPY --from=publish-cart /app/cart ./cart/
COPY --from=publish-order /app/order ./order/
COPY --from=publish-payment /app/payment ./payment/
COPY --from=publish-gateway /app/gateway ./gateway/
COPY --from=publish-chat /app/chat ./chat/

# Create supervisor configuration
RUN mkdir -p /etc/supervisor/conf.d
COPY <<EOF /etc/supervisor/conf.d/supervisord.conf
[supervisord]
nodaemon=true
user=root

[program:identity-api]
command=dotnet /app/identity/NafaPlace.Identity.API.dll
directory=/app/identity
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5001",ASPNETCORE_ENVIRONMENT="Staging",ConnectionStrings__DefaultConnection="%(ENV_DATABASE_URL)s",JwtSettings__Secret="%(ENV_JWT_SECRET)s",JwtSettings__Issuer="NafaPlace-Test",JwtSettings__Audience="NafaPlaceApi-Test",JwtSettings__TokenLifetimeMinutes="60"

[program:catalog-api]
command=dotnet /app/catalog/NafaPlace.Catalog.API.dll
directory=/app/catalog
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5002",ASPNETCORE_ENVIRONMENT="Staging",ConnectionStrings__DefaultConnection="%(ENV_DATABASE_URL)s",CloudinarySettings__CloudName="%(ENV_CLOUDINARY_CLOUD_NAME)s",CloudinarySettings__ApiKey="%(ENV_CLOUDINARY_API_KEY)s",CloudinarySettings__ApiSecret="%(ENV_CLOUDINARY_API_SECRET)s"

[program:cart-api]
command=dotnet /app/cart/NafaPlace.Cart.API.dll
directory=/app/cart
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5003",ASPNETCORE_ENVIRONMENT="Staging",ConnectionStrings__Redis="%(ENV_REDIS_URL)s"

[program:order-api]
command=dotnet /app/order/NafaPlace.Order.API.dll
directory=/app/order
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5004",ASPNETCORE_ENVIRONMENT="Staging",ConnectionStrings__DefaultConnection="%(ENV_DATABASE_URL)s"

[program:payment-api]
command=dotnet /app/payment/NafaPlace.Payment.API.dll
directory=/app/payment
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5005",ASPNETCORE_ENVIRONMENT="Staging",StripeSettings__PublishableKey="%(ENV_STRIPE_PUBLISHABLE_KEY)s",StripeSettings__SecretKey="%(ENV_STRIPE_SECRET_KEY)s"

[program:api-gateway]
command=dotnet /app/gateway/NafaPlace.ApiGateway.dll
directory=/app/gateway
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:8080",ASPNETCORE_ENVIRONMENT="Staging"

[program:chat-api]
command=dotnet /app/chat/NafaPlace.Chat.API.dll
directory=/app/chat
autostart=true
autorestart=true
environment=ASPNETCORE_URLS="http://+:5007",ASPNETCORE_ENVIRONMENT="Staging"
EOF

# Create nginx configurations for Blazor WebAssembly apps
COPY <<EOF /etc/nginx/web.conf
events {
    worker_connections 1024;
}
http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 8080;
        server_name _;
        root /app/web/wwwroot;
        index index.html;

        # API Gateway proxy
        location /api/ {
            proxy_pass http://localhost:5000/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        # Blazor WebAssembly files
        location /_framework/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files \$uri \$uri/ =404;
        }

        # Static files
        location ~* \.(css|js|json|ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files \$uri =404;
        }

        location / {
            try_files \$uri \$uri/ /index.html;
        }
    }
}
EOF

COPY <<EOF /etc/nginx/admin.conf
events {
    worker_connections 1024;
}
http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 8081;
        server_name _;
        root /app/admin/wwwroot;
        index index.html;

        # API proxy
        location /api/ {
            proxy_pass http://localhost:5000/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        # Blazor WebAssembly files
        location /_framework/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files \$uri \$uri/ =404;
        }

        # Static files
        location ~* \.(css|js|json|ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files \$uri =404;
        }

        location / {
            try_files \$uri \$uri/ /index.html;
        }
    }
}
EOF

COPY <<EOF /etc/nginx/seller.conf
events {
    worker_connections 1024;
}
http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 8082;
        server_name _;
        root /app/seller/wwwroot;
        index index.html;

        # API proxy
        location /api/ {
            proxy_pass http://localhost:5000/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        # Blazor WebAssembly files
        location /_framework/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files \$uri \$uri/ =404;
        }

        # Static files
        location ~* \.(css|js|json|ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files \$uri =404;
        }

        location / {
            try_files \$uri \$uri/ /index.html;
        }
    }
}
EOF

EXPOSE 8080 8081 8082 8083

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
