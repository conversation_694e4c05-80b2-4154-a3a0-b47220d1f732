# Script de démarrage et test complet de NafaPlace
# Auteur: Assistant IA
# Date: 2025-01-28

param(
    [switch]$SkipBuild,
    [switch]$SkipMigrations,
    [switch]$SkipTests,
    [switch]$StopOnly
)

Write-Host "🚀 NafaPlace - Script de démarrage et test complet" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Fonction pour arrêter tous les services
function Stop-AllServices {
    Write-Host "🛑 Arrêt de tous les services..." -ForegroundColor Yellow
    
    try {
        docker-compose -f docker-compose.all-services.yml down -v
        Write-Host "✅ Tous les services arrêtés" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Erreur lors de l'arrêt: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Si on veut seulement arrêter
if ($StopOnly) {
    Stop-AllServices
    return
}

# Fonction pour construire tous les services
function Build-AllServices {
    Write-Host "🔨 Construction de tous les services..." -ForegroundColor Yellow
    
    try {
        # Construire les images Docker
        docker-compose -f docker-compose.all-services.yml build --no-cache
        Write-Host "✅ Construction terminée" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Erreur lors de la construction: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour exécuter les migrations
function Run-Migrations {
    Write-Host "🗄️ Exécution des migrations de base de données..." -ForegroundColor Yellow
    
    $services = @(
        "catalog",
        "order", 
        "notification",
        "analytics",
        "chat",
        "recommendation",
        "loyalty",
        "localization"
    )
    
    foreach ($service in $services) {
        try {
            Write-Host "📊 Migration $service..." -ForegroundColor Cyan
            
            # Attendre que la base de données soit prête
            Start-Sleep -Seconds 5
            
            # Exécuter les migrations via dotnet ef (si disponible)
            $migrationPath = "src/Services/$($service.Substring(0,1).ToUpper() + $service.Substring(1))/NafaPlace.$($service.Substring(0,1).ToUpper() + $service.Substring(1)).Infrastructure"
            
            if (Test-Path $migrationPath) {
                Push-Location $migrationPath
                dotnet ef database update --verbose
                Pop-Location
                Write-Host "✅ Migration $service terminée" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "⚠️ Erreur migration $service : $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

# Fonction pour démarrer tous les services
function Start-AllServices {
    Write-Host "🚀 Démarrage de tous les services..." -ForegroundColor Yellow
    
    try {
        # Démarrer d'abord la base de données et Redis
        Write-Host "📊 Démarrage de la base de données..." -ForegroundColor Cyan
        docker-compose -f docker-compose.all-services.yml up -d postgres redis
        
        # Attendre que la base soit prête
        Write-Host "⏳ Attente de la base de données..." -ForegroundColor Cyan
        Start-Sleep -Seconds 30
        
        # Démarrer les services API
        Write-Host "🔧 Démarrage des services API..." -ForegroundColor Cyan
        docker-compose -f docker-compose.all-services.yml up -d catalog-api order-api notification-api
        Start-Sleep -Seconds 20
        
        docker-compose -f docker-compose.all-services.yml up -d analytics-api chat-api recommendation-api
        Start-Sleep -Seconds 20
        
        docker-compose -f docker-compose.all-services.yml up -d loyalty-api localization-api search-api inventory-api
        Start-Sleep -Seconds 20
        
        # Démarrer l'API Gateway
        Write-Host "🌐 Démarrage de l'API Gateway..." -ForegroundColor Cyan
        docker-compose -f docker-compose.all-services.yml up -d api-gateway
        Start-Sleep -Seconds 15
        
        # Démarrer les applications web
        Write-Host "🖥️ Démarrage des applications web..." -ForegroundColor Cyan
        docker-compose -f docker-compose.all-services.yml up -d web-app admin-portal seller-portal
        
        Write-Host "✅ Tous les services démarrés" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Erreur lors du démarrage: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour vérifier l'état des services
function Check-ServicesStatus {
    Write-Host "🔍 Vérification de l'état des services..." -ForegroundColor Yellow
    
    $services = @{
        "PostgreSQL" = "nafaplace-postgres"
        "Redis" = "nafaplace-redis"
        "Catalog API" = "nafaplace-catalog-api"
        "Order API" = "nafaplace-order-api"
        "Notification API" = "nafaplace-notification-api"
        "Analytics API" = "nafaplace-analytics-api"
        "Chat API" = "nafaplace-chat-api"
        "Recommendation API" = "nafaplace-recommendation-api"
        "Loyalty API" = "nafaplace-loyalty-api"
        "Localization API" = "nafaplace-localization-api"
        "Search API" = "nafaplace-search-api"
        "Inventory API" = "nafaplace-inventory-api"
        "API Gateway" = "nafaplace-api-gateway"
        "Web App" = "nafaplace-web-app"
        "Admin Portal" = "nafaplace-admin-portal"
        "Seller Portal" = "nafaplace-seller-portal"
    }
    
    $runningCount = 0
    $totalCount = $services.Count
    
    foreach ($service in $services.GetEnumerator()) {
        try {
            $status = docker ps --filter "name=$($service.Value)" --format "table {{.Status}}" | Select-Object -Skip 1
            if ($status -and $status.Contains("Up")) {
                Write-Host "✅ $($service.Key): En cours d'exécution" -ForegroundColor Green
                $runningCount++
            } else {
                Write-Host "❌ $($service.Key): Arrêté" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "❌ $($service.Key): Erreur de vérification" -ForegroundColor Red
        }
    }
    
    $successRate = [math]::Round(($runningCount / $totalCount) * 100, 2)
    Write-Host "`n📊 Services en cours: $runningCount/$totalCount ($successRate%)" -ForegroundColor $(if ($successRate -ge 80) { "Green" } else { "Yellow" })
    
    return $successRate -ge 80
}

# Fonction pour afficher les URLs d'accès
function Show-AccessUrls {
    Write-Host "`n🌐 URLs d'accès aux services:" -ForegroundColor Magenta
    Write-Host "================================" -ForegroundColor Magenta
    Write-Host "🛒 Site Web Principal:    http://localhost:8080" -ForegroundColor Cyan
    Write-Host "👨‍💼 Portail Admin:         http://localhost:8081" -ForegroundColor Cyan
    Write-Host "🏪 Portail Vendeur:       http://localhost:8082" -ForegroundColor Cyan
    Write-Host "🔗 API Gateway:           http://localhost:5000" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📡 APIs des services:" -ForegroundColor Yellow
    Write-Host "  • Catalog:              http://localhost:5243" -ForegroundColor White
    Write-Host "  • Order:                http://localhost:5004" -ForegroundColor White
    Write-Host "  • Notification:         http://localhost:5005" -ForegroundColor White
    Write-Host "  • Analytics:            http://localhost:5006" -ForegroundColor White
    Write-Host "  • Chat:                 http://localhost:5007" -ForegroundColor White
    Write-Host "  • Recommendation:       http://localhost:5008" -ForegroundColor White
    Write-Host "  • Loyalty:              http://localhost:5009" -ForegroundColor White
    Write-Host "  • Localization:         http://localhost:5010" -ForegroundColor White
    Write-Host "  • Search:               http://localhost:5011" -ForegroundColor White
    Write-Host "  • Inventory:            http://localhost:5012" -ForegroundColor White
    Write-Host ""
    Write-Host "🗄️ Base de données PostgreSQL: localhost:5432" -ForegroundColor Yellow
    Write-Host "🔴 Redis Cache:               localhost:6379" -ForegroundColor Yellow
}

# Fonction principale
function Start-CompleteTest {
    Write-Host "🎯 Démarrage du processus complet..." -ForegroundColor Green
    
    # Arrêter les services existants
    Stop-AllServices
    Start-Sleep -Seconds 5
    
    # Construction (si pas de skip)
    if (-not $SkipBuild) {
        $buildSuccess = Build-AllServices
        if (-not $buildSuccess) {
            Write-Host "❌ Échec de la construction. Arrêt du processus." -ForegroundColor Red
            return
        }
    }
    
    # Démarrage des services
    $startSuccess = Start-AllServices
    if (-not $startSuccess) {
        Write-Host "❌ Échec du démarrage. Arrêt du processus." -ForegroundColor Red
        return
    }
    
    # Attendre que tous les services soient prêts
    Write-Host "⏳ Attente de la stabilisation des services..." -ForegroundColor Yellow
    Start-Sleep -Seconds 60
    
    # Migrations (si pas de skip)
    if (-not $SkipMigrations) {
        Run-Migrations
    }
    
    # Vérification de l'état
    $servicesOk = Check-ServicesStatus
    
    if ($servicesOk) {
        Show-AccessUrls
        
        # Tests (si pas de skip)
        if (-not $SkipTests) {
            Write-Host "`n🧪 Lancement des tests..." -ForegroundColor Magenta
            Start-Sleep -Seconds 10
            & "$PSScriptRoot\test-all-services.ps1"
        }
        
        Write-Host "`n🎉 NafaPlace est opérationnel !" -ForegroundColor Green
        Write-Host "Vous pouvez maintenant accéder aux différents portails." -ForegroundColor Green
    } else {
        Write-Host "`n⚠️ Certains services ne sont pas opérationnels." -ForegroundColor Yellow
        Write-Host "Vérifiez les logs avec: docker-compose -f docker-compose.all-services.yml logs" -ForegroundColor Yellow
    }
}

# Exécution du script principal
Start-CompleteTest

Write-Host "`n🏁 Script terminé !" -ForegroundColor Green
Write-Host "Pour arrêter tous les services: .\start-and-test.ps1 -StopOnly" -ForegroundColor Cyan
