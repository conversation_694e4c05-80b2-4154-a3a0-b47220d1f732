using Microsoft.EntityFrameworkCore;
using NafaPlace.Delivery.Domain.Models;

namespace NafaPlace.Delivery.Infrastructure.Data;

public class DeliveryDbContext : DbContext
{
    public DeliveryDbContext(DbContextOptions<DeliveryDbContext> options) : base(options)
    {
    }

    public DbSet<DeliveryZone> DeliveryZones { get; set; }
    public DbSet<Carrier> Carriers { get; set; }
    public DbSet<CarrierZone> CarrierZones { get; set; }
    public DbSet<DeliveryOrder> DeliveryOrders { get; set; }
    public DbSet<DeliveryTracking> DeliveryTrackings { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // DeliveryZone configuration
        modelBuilder.Entity<DeliveryZone>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.BaseDeliveryFee).HasColumnType("decimal(18,2)");
            entity.Property(e => e.FreeDeliveryThreshold).HasColumnType("decimal(18,2)");
        });

        // Carrier configuration
        modelBuilder.Entity<Carrier>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.ContactEmail).HasMaxLength(100);
            entity.Property(e => e.ContactPhone).HasMaxLength(20);
        });

        // CarrierZone configuration
        modelBuilder.Entity<CarrierZone>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.DeliveryFee).HasColumnType("decimal(18,2)");
            entity.Property(e => e.FreeDeliveryThreshold).HasColumnType("decimal(18,2)");
            entity.Property(e => e.SameDayDeliveryFee).HasColumnType("decimal(18,2)");
            entity.Property(e => e.ExpressDeliveryFee).HasColumnType("decimal(18,2)");

            entity.HasOne(e => e.Carrier)
                  .WithMany(c => c.CarrierZones)
                  .HasForeignKey(e => e.CarrierId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Zone)
                  .WithMany(z => z.CarrierZones)
                  .HasForeignKey(e => e.ZoneId)
                  .OnDelete(DeleteBehavior.Cascade);

            // Unique constraint to prevent duplicate carrier-zone combinations
            entity.HasIndex(e => new { e.CarrierId, e.ZoneId }).IsUnique();
        });

        // DeliveryOrder configuration
        modelBuilder.Entity<DeliveryOrder>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrderId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.TrackingNumber).IsRequired().HasMaxLength(50);
            entity.Property(e => e.CustomerId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.CustomerEmail).IsRequired().HasMaxLength(100);
            entity.Property(e => e.CustomerPhone).IsRequired().HasMaxLength(20);
            entity.Property(e => e.DeliveryAddress).IsRequired().HasMaxLength(500);
            entity.Property(e => e.DeliveryCity).HasMaxLength(100);
            entity.Property(e => e.DeliveryRegion).HasMaxLength(100);
            entity.Property(e => e.DeliveryPostalCode).HasMaxLength(20);
            entity.Property(e => e.DeliveryCountry).HasMaxLength(100);
            entity.Property(e => e.Currency).IsRequired().HasMaxLength(3);
            entity.Property(e => e.OrderValue).HasColumnType("decimal(18,2)");
            entity.Property(e => e.DeliveryFee).HasColumnType("decimal(18,2)");
            entity.Property(e => e.InsuranceFee).HasColumnType("decimal(18,2)");
            entity.Property(e => e.AdditionalFees).HasColumnType("decimal(18,2)");
            entity.Property(e => e.TotalFee).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Weight).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Volume).HasColumnType("decimal(18,2)");
            entity.Property(e => e.PackageDescription).HasMaxLength(500);
            entity.Property(e => e.DeliveryInstructions).HasMaxLength(1000);
            entity.Property(e => e.SpecialRequirements).HasMaxLength(1000);
            entity.Property(e => e.DeliveryPersonName).HasMaxLength(100);
            entity.Property(e => e.DeliveryPersonPhone).HasMaxLength(20);
            entity.Property(e => e.ReceivedByName).HasMaxLength(100);
            entity.Property(e => e.ReceivedByPhone).HasMaxLength(20);
            entity.Property(e => e.DeliveryNotes).HasMaxLength(500);
            entity.Property(e => e.DeliveryPhotoUrl).HasMaxLength(200);
            entity.Property(e => e.SignatureUrl).HasMaxLength(200);
            entity.Property(e => e.CustomerFeedback).HasMaxLength(1000);
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity.Property(e => e.UpdatedBy).HasMaxLength(50);

            entity.HasOne(e => e.Carrier)
                  .WithMany(c => c.DeliveryOrders)
                  .HasForeignKey(e => e.CarrierId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Zone)
                  .WithMany(z => z.DeliveryOrders)
                  .HasForeignKey(e => e.ZoneId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // DeliveryTracking configuration
        modelBuilder.Entity<DeliveryTracking>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.Description).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Location).HasMaxLength(100);
            entity.Property(e => e.EventBy).HasMaxLength(100);
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.PhotoUrl).HasMaxLength(200);

            entity.HasOne(e => e.DeliveryOrder)
                  .WithMany(d => d.TrackingEvents)
                  .HasForeignKey(e => e.DeliveryOrderId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // DeliveryRoute configuration
        modelBuilder.Entity<DeliveryRoute>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
            entity.Property(e => e.DriverId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.DriverName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.DriverPhone).HasMaxLength(20);

            entity.HasOne(e => e.Carrier)
                  .WithMany()
                  .HasForeignKey(e => e.CarrierId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // RouteDelivery configuration
        modelBuilder.Entity<RouteDelivery>(entity =>
        {
            entity.HasKey(e => e.Id);

            entity.HasOne(e => e.Route)
                  .WithMany(r => r.RouteDeliveries)
                  .HasForeignKey(e => e.RouteId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.DeliveryOrder)
                  .WithMany()
                  .HasForeignKey(e => e.DeliveryOrderId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // Seed data - disabled for now, can be added manually via admin portal
        // SeedData(modelBuilder);
    }

    private void SeedData(ModelBuilder modelBuilder)
    {
        // Seed delivery zones for Guinea
        modelBuilder.Entity<DeliveryZone>().HasData(
            new DeliveryZone { Id = 1, Name = "Conakry Centre", Code = "CKY-CTR", Description = "Centre-ville de Conakry", BaseDeliveryFee = 15000, FreeDeliveryThreshold = 500000, IsActive = true, EstimatedDeliveryDays = 1, MaxDeliveryDays = 2, Currency = "GNF" },
            new DeliveryZone { Id = 2, Name = "Conakry Périphérie", Code = "CKY-PER", Description = "Banlieue de Conakry", BaseDeliveryFee = 25000, FreeDeliveryThreshold = 500000, IsActive = true, EstimatedDeliveryDays = 2, MaxDeliveryDays = 3, Currency = "GNF" },
            new DeliveryZone { Id = 3, Name = "Kindia", Code = "KIN", Description = "Région de Kindia", BaseDeliveryFee = 50000, FreeDeliveryThreshold = 750000, IsActive = true, EstimatedDeliveryDays = 3, MaxDeliveryDays = 5, Currency = "GNF" },
            new DeliveryZone { Id = 4, Name = "Boké", Code = "BOK", Description = "Région de Boké", BaseDeliveryFee = 60000, FreeDeliveryThreshold = 750000, IsActive = true, EstimatedDeliveryDays = 4, MaxDeliveryDays = 6, Currency = "GNF" },
            new DeliveryZone { Id = 5, Name = "Labé", Code = "LAB", Description = "Région de Labé", BaseDeliveryFee = 70000, FreeDeliveryThreshold = 1000000, IsActive = true, EstimatedDeliveryDays = 5, MaxDeliveryDays = 7, Currency = "GNF" }
        );

        // Seed carriers
        modelBuilder.Entity<Carrier>().HasData(
            new Carrier { Id = 1, Name = "NafaPlace Express", Code = "NPE", Description = "Service de livraison express NafaPlace", ContactEmail = "<EMAIL>", ContactPhone = "+************", IsActive = true, Type = CarrierType.Internal },
            new Carrier { Id = 2, Name = "Guinée Livraison", Code = "GDL", Description = "Service de livraison national", ContactEmail = "<EMAIL>", ContactPhone = "+224621000002", IsActive = true, Type = CarrierType.ThirdParty },
            new Carrier { Id = 3, Name = "Rapid Delivery GN", Code = "RDG", Description = "Livraison rapide en Guinée", ContactEmail = "<EMAIL>", ContactPhone = "+224621000003", IsActive = true, Type = CarrierType.ThirdParty }
        );
    }
}
