using NafaPlace.Chat.Application.DTOs;

namespace NafaPlace.Chat.Application.Services;

public interface IFAQService
{
    // Gestion des FAQ
    Task<int> CreateFAQItemAsync(CreateFAQItemDto faqItem);
    Task<FAQItemDto?> GetFAQItemAsync(int id);
    Task<List<FAQItemDto>> GetFAQItemsAsync(FAQCategory? category = null, bool? isActive = null);
    Task<bool> UpdateFAQItemAsync(UpdateFAQItemDto faqItem);
    Task<bool> DeleteFAQItemAsync(int id);
    Task<bool> ToggleFAQActiveStatusAsync(int id, bool isActive);
    
    // Recherche et suggestions
    Task<FAQSearchResultDto> SearchFAQsAsync(FAQSearchDto searchRequest);
    Task<List<FAQItemDto>> GetSimilarFAQsAsync(string question, int limit = 5);
    Task<List<FAQItemDto>> GetRelatedFAQsAsync(int faqId, int limit = 5);
    Task<List<FAQItemDto>> GetPopularFAQsAsync(int limit = 10);
    Task<List<FAQItemDto>> GetFeaturedFAQsAsync();
    Task<List<string>> GetFAQSuggestionsAsync(string partialQuery, int limit = 5);
    
    // Analytics et feedback
    Task<bool> RecordFAQViewAsync(int faqId, string? userId = null);
    Task<bool> RecordFAQFeedbackAsync(int faqId, bool wasHelpful, string? userId = null, string? comment = null);
    Task<Dictionary<string, object>> GetFAQAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<FAQItemDto>> GetLeastHelpfulFAQsAsync(int limit = 10);
    Task<List<FAQItemDto>> GetMostViewedFAQsAsync(int limit = 10);
    
    // Chatbot et IA
    Task<ChatbotResponseDto> ProcessChatbotMessageAsync(string message, string sessionId, string? userId = null, Dictionary<string, object>? context = null);
    Task<List<SmartSuggestionDto>> GetSmartSuggestionsAsync(string userMessage, string? context = null);
    Task<AutoResponseDto> GenerateAutoResponseAsync(string userMessage, string? conversationContext = null);
    Task<ConversationAnalysisDto> AnalyzeConversationAsync(int conversationId);
    
    // Gestion du chatbot
    Task<ChatbotDto?> GetChatbotAsync(int chatbotId);
    Task<List<ChatbotDto>> GetChatbotsAsync();
    Task<bool> UpdateChatbotAsync(ChatbotDto chatbot);
    Task<bool> TrainChatbotAsync(int chatbotId, List<ChatbotTrainingDataDto> trainingData);
    Task<ChatbotStats> GetChatbotStatsAsync(int chatbotId, DateTime? startDate = null, DateTime? endDate = null);
    
    // Interactions chatbot
    Task<int> LogChatbotInteractionAsync(ChatbotInteractionDto interaction);
    Task<List<ChatbotInteractionDto>> GetChatbotInteractionsAsync(string? sessionId = null, string? userId = null, int limit = 50);
    Task<bool> UpdateInteractionFeedbackAsync(int interactionId, bool wasHelpful);
    Task<Dictionary<string, object>> GetInteractionAnalyticsAsync(DateTime? startDate = null, DateTime? endDate = null);
    
    // Base de connaissances
    Task<int> CreateKnowledgeBaseAsync(KnowledgeBaseDto knowledgeBase);
    Task<KnowledgeBaseDto?> GetKnowledgeBaseAsync(int id);
    Task<List<KnowledgeBaseDto>> GetKnowledgeBasesAsync();
    Task<bool> UpdateKnowledgeBaseAsync(KnowledgeBaseDto knowledgeBase);
    Task<bool> DeleteKnowledgeBaseAsync(int id);
    
    // Articles de la base de connaissances
    Task<int> CreateKnowledgeArticleAsync(KnowledgeArticleDto article);
    Task<KnowledgeArticleDto?> GetKnowledgeArticleAsync(int id);
    Task<List<KnowledgeArticleDto>> GetKnowledgeArticlesAsync(ArticleCategory? category = null, bool? isPublished = null);
    Task<bool> UpdateKnowledgeArticleAsync(KnowledgeArticleDto article);
    Task<bool> DeleteKnowledgeArticleAsync(int id);
    Task<List<KnowledgeArticleDto>> SearchKnowledgeArticlesAsync(string query, ArticleCategory? category = null);
    
    // Entraînement et apprentissage automatique
    Task<bool> AddTrainingDataAsync(ChatbotTrainingDataDto trainingData);
    Task<List<ChatbotTrainingDataDto>> GetTrainingDataAsync(string? intent = null);
    Task<bool> UpdateTrainingDataAsync(ChatbotTrainingDataDto trainingData);
    Task<bool> DeleteTrainingDataAsync(int id);
    Task<bool> RetrainChatbotAsync(int chatbotId);
    
    // Détection d'intention et entités
    Task<string> DetectIntentAsync(string message, Dictionary<string, object>? context = null);
    Task<List<string>> ExtractEntitiesAsync(string message);
    Task<double> CalculateConfidenceAsync(string message, string intent);
    Task<List<string>> GenerateResponseVariationsAsync(string baseResponse);
    
    // Gestion des catégories et tags
    Task<List<string>> GetFAQCategoriesAsync();
    Task<List<string>> GetPopularTagsAsync(int limit = 20);
    Task<bool> AddTagToFAQAsync(int faqId, string tag);
    Task<bool> RemoveTagFromFAQAsync(int faqId, string tag);
    Task<Dictionary<string, int>> GetTagUsageStatsAsync();
    
    // Import/Export
    Task<bool> ImportFAQsFromFileAsync(Stream fileStream, string fileName);
    Task<byte[]> ExportFAQsAsync(FAQCategory? category = null, string format = "json");
    Task<bool> ImportKnowledgeBaseAsync(Stream fileStream, string fileName);
    Task<byte[]> ExportKnowledgeBaseAsync(int knowledgeBaseId, string format = "json");
    
    // Maintenance et optimisation
    Task<int> CleanupOldInteractionsAsync(int daysToKeep = 90);
    Task<bool> OptimizeFAQSearchIndexAsync();
    Task<List<string>> GetUnhandledQuestionsAsync(int limit = 50);
    Task<List<string>> SuggestNewFAQsAsync(int limit = 10);
    Task<bool> MergeDuplicateFAQsAsync(int sourceFaqId, int targetFaqId);
    
    // Intégrations
    Task<bool> SyncWithExternalKnowledgeBaseAsync(string externalSystemId);
    Task<List<FAQItemDto>> ImportFromWebsiteAsync(string websiteUrl);
    Task<bool> GenerateFAQFromTicketsAsync(List<int> ticketIds);
    Task<bool> UpdateFAQFromConversationAsync(int conversationId, int faqId);
    
    // Personnalisation et localisation
    Task<List<FAQItemDto>> GetPersonalizedFAQsAsync(string userId, int limit = 10);
    Task<FAQItemDto?> TranslateFAQAsync(int faqId, string targetLanguage);
    Task<List<string>> GetSupportedLanguagesAsync();
    Task<bool> AddFAQTranslationAsync(int faqId, string language, string question, string answer);
    
    // Métriques avancées
    Task<Dictionary<string, object>> GetFAQEffectivenessMetricsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, object>> GetChatbotPerformanceMetricsAsync(int chatbotId, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<string>> GetTopSearchQueriesAsync(int limit = 20);
    Task<Dictionary<string, double>> GetFAQSatisfactionScoresAsync();
    Task<List<string>> GetMissedOpportunitiesAsync(int limit = 20);
    
    // Configuration et paramètres
    Task<Dictionary<string, object>> GetFAQConfigAsync();
    Task<bool> UpdateFAQConfigAsync(Dictionary<string, object> config);
    Task<bool> TestChatbotAsync(int chatbotId, string testMessage);
    Task<Dictionary<string, bool>> GetServiceHealthAsync();
    
    // Workflows et automatisation
    Task<bool> CreateAutoFAQFromTicketAsync(int ticketId);
    Task<bool> SuggestFAQUpdatesAsync();
    Task<List<string>> GetOutdatedFAQsAsync(int daysThreshold = 365);
    Task<bool> ScheduleFAQReviewAsync(int faqId, DateTime reviewDate);
    Task<List<FAQItemDto>> GetFAQsForReviewAsync();
    
    // Collaboration et workflow
    Task<bool> SubmitFAQForApprovalAsync(int faqId, string submittedBy);
    Task<bool> ApproveFAQAsync(int faqId, string approvedBy);
    Task<bool> RejectFAQAsync(int faqId, string rejectedBy, string reason);
    Task<List<FAQItemDto>> GetPendingFAQsAsync();
    Task<bool> AssignFAQReviewerAsync(int faqId, string reviewerId);
    
    // Recherche sémantique et IA avancée
    Task<List<FAQItemDto>> SemanticSearchAsync(string query, int limit = 10);
    Task<string> GenerateAnswerFromContextAsync(string question, List<string> contextDocuments);
    Task<List<string>> ExtractKeyPhrasesAsync(string text);
    Task<double> CalculateSemanticSimilarityAsync(string text1, string text2);
    Task<List<string>> GenerateQuestionVariationsAsync(string originalQuestion);
    
    // Intégration avec le chat
    Task HandleChatMessageForFAQAsync(int conversationId, string message);
    Task<List<FAQItemDto>> SuggestFAQsForConversationAsync(int conversationId);
    Task<bool> CreateFAQFromChatAsync(int conversationId, string question, string answer);
    Task<AutoResponseDto> GenerateChatResponseAsync(string userMessage, int conversationId);
}
