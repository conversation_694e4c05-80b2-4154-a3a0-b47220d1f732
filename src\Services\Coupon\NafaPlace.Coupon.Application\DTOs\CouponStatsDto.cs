namespace NafaPlace.Coupon.Application.DTOs;

public class CouponStatsDto
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int TotalUsages { get; set; }
    public int MaxUsages { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public decimal TotalDiscountGiven { get; set; }
    public int UniqueUsers { get; set; }
    public DateTime? LastUsed { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsActive { get; set; }
    public bool IsExpired { get; set; }
    public double UsagePercentage { get; set; }
}
