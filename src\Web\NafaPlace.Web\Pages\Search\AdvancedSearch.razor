@page "/search"
@page "/search/{SearchQuery}"
@using NafaPlace.Web.Services
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Components.Search
@inject IAdvancedSearchService AdvancedSearchService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>Recherche avancée - NafaPlace</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar avec filtres -->
        <div class="col-lg-3 col-md-4 mb-4">
            <div class="sticky-top" style="top: 20px;">
                <AdvancedFilters 
                    MinPrice="@searchRequest.MinPrice"
                    MaxPrice="@searchRequest.MaxPrice"
                    SelectedCategoryIds="@searchRequest.CategoryIds"
                    SelectedBrands="@searchRequest.Brands"
                    MinRating="@searchRequest.MinRating"
                    InStockOnly="@searchRequest.InStockOnly"
                    HasDiscount="@searchRequest.HasDiscount"
                    HasFreeShipping="@searchRequest.HasFreeShipping"
                    IsNewArrival="@searchRequest.IsNewArrival"
                    OnFiltersChanged="OnFiltersChanged" />
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="col-lg-9 col-md-8">
            <!-- Barre de recherche -->
            <div class="search-header mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <SearchAutocomplete 
                            Placeholder="Rechercher des produits..."
                            OnSearch="OnSearchSubmitted" />
                    </div>
                </div>
            </div>

            <!-- Résultats de recherche -->
            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2 text-muted">Recherche en cours...</p>
                </div>
            }
            else if (searchResult != null)
            {
                <!-- Métadonnées de recherche -->
                <div class="search-metadata mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-1">
                                @searchResult.Metadata.TotalResults résultat(s) trouvé(s)
                                @if (!string.IsNullOrEmpty(searchResult.Metadata.OriginalQuery))
                                {
                                    <span class="text-muted">pour "@searchResult.Metadata.OriginalQuery"</span>
                                }
                            </h5>
                            <small class="text-muted">
                                Recherche effectuée en @searchResult.Metadata.SearchTimeMs.ToString("F0")ms
                            </small>
                        </div>
                        
                        <!-- Options de tri -->
                        <div class="sort-options">
                            <select class="form-select form-select-sm" style="width: auto;" 
                                    @bind="sortOption" @bind:event="onchange">
                                <option value="relevance">Pertinence</option>
                                <option value="price_asc">Prix croissant</option>
                                <option value="price_desc">Prix décroissant</option>
                                <option value="name_asc">Nom A-Z</option>
                                <option value="name_desc">Nom Z-A</option>
                                <option value="newest">Plus récents</option>
                                <option value="rating">Mieux notés</option>
                            </select>
                        </div>
                    </div>

                    <!-- Filtres appliqués -->
                    @if (searchResult.Metadata.AppliedFilters.Any())
                    {
                        <div class="applied-filters mt-2">
                            <small class="text-muted me-2">Filtres actifs:</small>
                            @foreach (var filter in searchResult.Metadata.AppliedFilters)
                            {
                                <span class="badge bg-primary me-1">@filter</span>
                            }
                        </div>
                    }
                </div>

                <!-- Suggestions de correction -->
                @if (searchResult.Suggestions.Any())
                {
                    <div class="search-suggestions mb-4">
                        <div class="alert alert-info">
                            <i class="bi bi-lightbulb me-2"></i>
                            <strong>Suggestions:</strong>
                            @foreach (var suggestion in searchResult.Suggestions.Take(3))
                            {
                                <button class="btn btn-sm btn-outline-info ms-2" 
                                        @onclick="() => SearchSuggestion(suggestion.Text)">
                                    @suggestion.Text (@suggestion.ResultCount)
                                </button>
                            }
                        </div>
                    </div>
                }

                <!-- Grille de produits -->
                @if (searchResult.Products.Items.Any())
                {
                    <div class="products-grid">
                        <div class="row g-4">
                            @foreach (var product in searchResult.Products.Items)
                            {
                                <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6">
                                    <div class="product-card card h-100 shadow-sm">
                                        <div class="product-image position-relative">
                                            @if (product.Images.Any())
                                            {
                                                <img src="@product.Images.First().Url" 
                                                     alt="@product.Name" 
                                                     class="card-img-top" 
                                                     style="height: 200px; object-fit: cover;" />
                                            }
                                            else
                                            {
                                                <div class="placeholder-image d-flex align-items-center justify-content-center bg-light" 
                                                     style="height: 200px;">
                                                    <i class="bi bi-image text-muted" style="font-size: 2rem;"></i>
                                                </div>
                                            }
                                            
                                            @if (product.HasDiscount)
                                            {
                                                <span class="badge bg-danger position-absolute top-0 end-0 m-2">
                                                    -@product.DiscountPercentage%
                                                </span>
                                            }
                                        </div>
                                        
                                        <div class="card-body d-flex flex-column">
                                            <h6 class="card-title">
                                                <a href="/catalog/product/@product.Id" class="text-decoration-none text-dark">
                                                    @product.Name
                                                </a>
                                            </h6>
                                            
                                            <p class="card-text text-muted small flex-grow-1">
                                                @(product.Description.Length > 80 ? 
                                                  product.Description.Substring(0, 80) + "..." : 
                                                  product.Description)
                                            </p>
                                            
                                            <div class="product-meta">
                                                <div class="price mb-2">
                                                    <span class="h6 text-primary mb-0">
                                                        @product.Price.ToString("N0") GNF
                                                    </span>
                                                </div>
                                                
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">@product.CategoryName</small>
                                                    <small class="text-muted">Stock: @product.CurrentStock</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Pagination -->
                    @if (searchResult.Products.TotalPages > 1)
                    {
                        <nav class="mt-5" aria-label="Navigation des résultats">
                            <ul class="pagination justify-content-center">
                                <li class="page-item @(searchResult.Products.Page <= 1 ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => ChangePage(searchResult.Products.Page - 1)">
                                        Précédent
                                    </button>
                                </li>
                                
                                @for (int i = Math.Max(1, searchResult.Products.Page - 2); 
                                      i <= Math.Min(searchResult.Products.TotalPages, searchResult.Products.Page + 2); 
                                      i++)
                                {
                                    var pageNumber = i;
                                    <li class="page-item @(pageNumber == searchResult.Products.Page ? "active" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(pageNumber)">
                                            @pageNumber
                                        </button>
                                    </li>
                                }
                                
                                <li class="page-item @(searchResult.Products.Page >= searchResult.Products.TotalPages ? "disabled" : "")">
                                    <button class="page-link" @onclick="() => ChangePage(searchResult.Products.Page + 1)">
                                        Suivant
                                    </button>
                                </li>
                            </ul>
                        </nav>
                    }
                }
                else
                {
                    <!-- Aucun résultat -->
                    <div class="no-results text-center py-5">
                        <i class="bi bi-search display-1 text-muted mb-3"></i>
                        <h4>Aucun produit trouvé</h4>
                        <p class="text-muted">
                            Essayez de modifier vos critères de recherche ou utilisez des mots-clés différents.
                        </p>
                        
                        @if (searchResult.Suggestions.Any())
                        {
                            <div class="mt-3">
                                <p class="mb-2">Suggestions:</p>
                                @foreach (var suggestion in searchResult.Suggestions.Take(5))
                                {
                                    <button class="btn btn-outline-primary me-2 mb-2" 
                                            @onclick="() => SearchSuggestion(suggestion.Text)">
                                        @suggestion.Text
                                    </button>
                                }
                            </div>
                        }
                    </div>
                }
            }
            else if (!string.IsNullOrEmpty(SearchQuery))
            {
                <!-- Erreur de recherche -->
                <div class="alert alert-warning text-center">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Une erreur s'est produite lors de la recherche. Veuillez réessayer.
                </div>
            }
        </div>
    </div>
</div>

<style>
    .product-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    }

    .placeholder-image {
        background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), 
                    linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), 
                    linear-gradient(45deg, transparent 75%, #f8f9fa 75%), 
                    linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
        background-size: 20px 20px;
        background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    }

    .sort-options select {
        min-width: 150px;
    }

    .applied-filters .badge {
        font-size: 0.75rem;
    }

    .search-suggestions .btn {
        font-size: 0.875rem;
    }
</style>

@code {
    [Parameter] public string? SearchQuery { get; set; }

    private ProductSearchRequest searchRequest = new();
    private ProductSearchResult? searchResult;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        if (!string.IsNullOrEmpty(SearchQuery))
        {
            searchRequest.SearchTerm = SearchQuery;
            await PerformSearch();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(SearchQuery) && SearchQuery != searchRequest.SearchTerm)
        {
            searchRequest.SearchTerm = SearchQuery;
            searchRequest.Page = 1; // Reset to first page
            await PerformSearch();
        }
    }

    private async Task OnSearchSubmitted(string query)
    {
        searchRequest.SearchTerm = query;
        searchRequest.Page = 1;
        Navigation.NavigateTo($"/search/{Uri.EscapeDataString(query)}");
    }

    private async Task OnFiltersChanged()
    {
        searchRequest.Page = 1; // Reset to first page when filters change
        await PerformSearch();
    }

    private async Task SearchSuggestion(string suggestion)
    {
        searchRequest.SearchTerm = suggestion;
        searchRequest.Page = 1;
        Navigation.NavigateTo($"/search/{Uri.EscapeDataString(suggestion)}");
    }

    private async Task ChangePage(int page)
    {
        if (page < 1 || (searchResult != null && page > searchResult.Products.TotalPages))
            return;

        searchRequest.Page = page;
        await PerformSearch();

        // Scroll to top
        await JSRuntime.InvokeVoidAsync("window.scrollTo", 0, 0);
    }

    private async Task PerformSearch()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Apply sort option
            ApplySortOption();

            searchResult = await AdvancedSearchService.SearchProductsAdvancedAsync(searchRequest);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la recherche: {ex.Message}");
            searchResult = null;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ApplySortOption()
    {
        switch (sortOption)
        {
            case "price_asc":
                searchRequest.SortBy = "price";
                searchRequest.SortDescending = false;
                break;
            case "price_desc":
                searchRequest.SortBy = "price";
                searchRequest.SortDescending = true;
                break;
            case "name_asc":
                searchRequest.SortBy = "name";
                searchRequest.SortDescending = false;
                break;
            case "name_desc":
                searchRequest.SortBy = "name";
                searchRequest.SortDescending = true;
                break;
            case "newest":
                searchRequest.SortBy = "CreatedAt";
                searchRequest.SortDescending = true;
                break;
            case "rating":
                searchRequest.SortBy = "rating";
                searchRequest.SortDescending = true;
                break;
            case "relevance":
            default:
                searchRequest.SortBy = "relevance";
                searchRequest.SortDescending = true;
                break;
        }
    }

    // Déclencher la recherche quand l'option de tri change
    private string _sortOption = "relevance";
    private string sortOption
    {
        get => _sortOption;
        set
        {
            if (_sortOption != value)
            {
                _sortOption = value;
                searchRequest.Page = 1; // Reset to first page
                _ = PerformSearch();
            }
        }
    }
}
