using NafaPlace.Chat.Application.DTOs;

namespace NafaPlace.Chat.Application.Services;

public interface IChatService
{
    // Gestion des conversations
    Task<int> CreateConversationAsync(CreateConversationDto conversation);
    Task<ChatConversationDto?> GetConversationAsync(int conversationId);
    Task<List<ChatConversationDto>> GetConversationsAsync(ChatFilterDto filter);
    Task<List<ChatConversationDto>> GetUserConversationsAsync(string userId, ConversationStatus? status = null);
    Task<List<ChatConversationDto>> GetAgentConversationsAsync(string agentId, ConversationStatus? status = null);
    Task<bool> UpdateConversationAsync(int conversationId, Dictionary<string, object> updates);
    Task<bool> CloseConversationAsync(int conversationId, string reason = "");
    Task<bool> ReopenConversationAsync(int conversationId);
    Task<bool> ArchiveConversationAsync(int conversationId);
    Task<bool> DeleteConversationAsync(int conversationId);
    
    // Gestion des messages
    Task<int> SendMessageAsync(SendMessageDto message, string senderId);
    Task<ChatMessageDto?> GetMessageAsync(int messageId);
    Task<List<ChatMessageDto>> GetConversationMessagesAsync(int conversationId, int page = 1, int pageSize = 50);
    Task<bool> MarkMessageAsReadAsync(int messageId, string userId);
    Task<bool> MarkConversationAsReadAsync(int conversationId, string userId);
    Task<bool> DeleteMessageAsync(int messageId, string userId);
    Task<bool> EditMessageAsync(int messageId, string newContent, string userId);
    
    // Assignation et routage
    Task<bool> AssignConversationAsync(int conversationId, string agentId);
    Task<bool> UnassignConversationAsync(int conversationId);
    Task<bool> TransferConversationAsync(int conversationId, string newAgentId, string? reason = null);
    Task<string?> FindBestAgentAsync(CreateConversationDto conversation);
    Task<List<ChatAgentDto>> GetAvailableAgentsAsync(string? departmentId = null);
    Task<bool> AutoAssignConversationAsync(int conversationId);
    Task<bool> RequestAgentAssignmentAsync(int conversationId, string reason);
    
    // Gestion des agents
    Task<bool> SetAgentStatusAsync(string agentId, AgentStatus status);
    Task<AgentStatus> GetAgentStatusAsync(string agentId);
    Task<ChatAgentDto?> GetAgentAsync(string agentId);
    Task<List<ChatAgentDto>> GetAgentsAsync(string? departmentId = null);
    Task<bool> UpdateAgentAsync(ChatAgentDto agent);
    Task<bool> IsAgentAvailableAsync(string agentId);
    Task<int> GetAgentActiveChatsCountAsync(string agentId);
    
    // Gestion des départements
    Task<string> CreateDepartmentAsync(ChatDepartmentDto department);
    Task<ChatDepartmentDto?> GetDepartmentAsync(string departmentId);
    Task<List<ChatDepartmentDto>> GetDepartmentsAsync();
    Task<bool> UpdateDepartmentAsync(ChatDepartmentDto department);
    Task<bool> DeleteDepartmentAsync(string departmentId);
    Task<bool> AddAgentToDepartmentAsync(string departmentId, string agentId);
    Task<bool> RemoveAgentFromDepartmentAsync(string departmentId, string agentId);
    
    // Tags et métadonnées
    Task<bool> AddTagToConversationAsync(int conversationId, string tag);
    Task<bool> RemoveTagFromConversationAsync(int conversationId, string tag);
    Task<List<string>> GetPopularTagsAsync(int limit = 20);
    Task<bool> UpdateConversationMetadataAsync(int conversationId, Dictionary<string, object> metadata);
    
    // Recherche et filtrage
    Task<List<ChatConversationDto>> SearchConversationsAsync(string query, ChatFilterDto? filter = null);
    Task<List<ChatMessageDto>> SearchMessagesAsync(string query, int? conversationId = null);
    Task<List<ChatConversationDto>> GetConversationsByTagAsync(string tag);
    Task<List<ChatConversationDto>> GetUnassignedConversationsAsync();
    Task<List<ChatConversationDto>> GetOverdueConversationsAsync(TimeSpan threshold);
    
    // Statistiques et analytics
    Task<ChatStatsDto> GetChatStatsAsync(DateTime? startDate = null, DateTime? endDate = null, string? departmentId = null);
    Task<AgentPerformanceDto> GetAgentPerformanceAsync(string agentId, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<AgentPerformanceDto>> GetAllAgentsPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, object>> GetDepartmentStatsAsync(string departmentId, DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, int>> GetConversationVolumeAsync(DateTime startDate, DateTime endDate, string groupBy = "day");
    Task<Dictionary<string, double>> GetResponseTimeStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    
    // Satisfaction client
    Task<bool> SubmitSatisfactionRatingAsync(int conversationId, int rating, string? comment = null);
    Task<double> GetAverageSatisfactionRatingAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<SatisfactionFeedbackDto>> GetSatisfactionFeedbackAsync(DateTime? startDate = null, DateTime? endDate = null);
    
    // Notifications et temps réel
    Task SendChatNotificationAsync(string userId, ChatNotificationDto notification);
    Task SendTypingIndicatorAsync(int conversationId, string userId, bool isTyping);
    Task UpdateUserPresenceAsync(string userId, UserPresenceStatus status);
    Task<List<ChatPresenceDto>> GetOnlineUsersAsync(int conversationId);
    
    // Intégrations et webhooks
    Task HandleCustomerMessageAsync(int conversationId, string customerId, string message);
    Task HandleAgentJoinedAsync(int conversationId, string agentId);
    Task HandleAgentLeftAsync(int conversationId, string agentId);
    Task HandleConversationEscalatedAsync(int conversationId, string reason);
    
    // Rapports et exports
    Task<byte[]> ExportConversationsAsync(ChatFilterDto filter, string format = "csv");
    Task<byte[]> ExportChatStatsAsync(DateTime startDate, DateTime endDate, string format = "pdf");
    Task<ConversationSummaryDto> GenerateConversationSummaryAsync(int conversationId);
    Task<List<string>> GetConversationKeywordsAsync(int conversationId);
    
    // Configuration et maintenance
    Task<Dictionary<string, object>> GetChatConfigAsync();
    Task<bool> UpdateChatConfigAsync(Dictionary<string, object> config);
    Task<int> CleanupOldConversationsAsync(int daysToKeep = 90);
    Task<int> ArchiveInactiveConversationsAsync(int daysInactive = 30);
    Task<bool> TestChatServiceAsync();
    
    // Gestion des fichiers et médias
    Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType, int conversationId);
    Task<Stream> DownloadFileAsync(string fileUrl);
    Task<bool> DeleteFileAsync(string fileUrl);
    Task<List<MessageAttachmentDto>> GetConversationFilesAsync(int conversationId);
    
    // Modération et sécurité
    Task<bool> IsMessageAppropriateAsync(string content);
    Task<bool> FlagConversationAsync(int conversationId, string reason);
    Task<List<ChatConversationDto>> GetFlaggedConversationsAsync();
    Task<bool> BlockUserAsync(string userId, string reason);
    Task<bool> UnblockUserAsync(string userId);
    Task<List<string>> GetBlockedUsersAsync();
    
    // Templates et réponses rapides
    Task<int> CreateQuickReplyAsync(QuickReplyDto quickReply);
    Task<List<QuickReplyDto>> GetQuickRepliesAsync(string? departmentId = null);
    Task<bool> UpdateQuickReplyAsync(QuickReplyDto quickReply);
    Task<bool> DeleteQuickReplyAsync(int quickReplyId);
    Task<List<QuickReplyDto>> SearchQuickRepliesAsync(string query);
    
    // Escalation et workflow
    Task<bool> EscalateConversationAsync(int conversationId, string reason, ConversationPriority newPriority);
    Task<bool> CreateFollowUpTaskAsync(int conversationId, string description, DateTime dueDate);
    Task<List<FollowUpTaskDto>> GetFollowUpTasksAsync(string? agentId = null);
    Task<bool> CompleteFollowUpTaskAsync(int taskId);
    
    // Intégration avec d'autres services
    Task HandleOrderInquiryAsync(int conversationId, int orderId);
    Task HandleProductInquiryAsync(int conversationId, int productId);
    Task HandlePaymentIssueAsync(int conversationId, int orderId, string issue);
    Task HandleRefundRequestAsync(int conversationId, int orderId, string reason);
    Task HandleTechnicalSupportAsync(int conversationId, string issue, Dictionary<string, object> systemInfo);
}

public class SatisfactionFeedbackDto
{
    public int ConversationId { get; set; }
    public int Rating { get; set; }
    public string? Comment { get; set; }
    public string CustomerId { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string? AgentId { get; set; }
    public string? AgentName { get; set; }
    public DateTime SubmittedAt { get; set; }
}

public class QuickReplyDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<string> Tags { get; set; } = new();
    public string? DepartmentId { get; set; }
    public bool IsActive { get; set; } = true;
    public int UsageCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class FollowUpTaskDto
{
    public int Id { get; set; }
    public int ConversationId { get; set; }
    public string Description { get; set; } = string.Empty;
    public string? AssignedAgentId { get; set; }
    public string? AssignedAgentName { get; set; }
    public DateTime DueDate { get; set; }
    public TaskStatus Status { get; set; }
    public TaskPriority Priority { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public enum TaskStatus
{
    Pending,
    InProgress,
    Completed,
    Cancelled,
    Overdue
}

public enum TaskPriority
{
    Low,
    Normal,
    High,
    Urgent
}
