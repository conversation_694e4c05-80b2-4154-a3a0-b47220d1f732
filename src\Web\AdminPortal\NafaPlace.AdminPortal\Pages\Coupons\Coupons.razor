@page "/coupons"
@using NafaPlace.AdminPortal.Services
@inject CouponService CouponService
@inject IJSRuntime JSRuntime
@inject NotificationService NotificationService

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-ticket-alt me-2"></i>
                        Gestion des Coupons
                    </h3>
                    <button class="btn btn-primary" @onclick="OpenCreateCouponModal">
                        <i class="fas fa-plus me-2"></i>
                        Nouveau Coupon
                    </button>
                </div>
                <div class="card-body">
                    <!-- Filtres -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" @onchange="OnFilterChange">
                                <option value="">Tous les coupons</option>
                                <option value="true">Actifs seulement</option>
                                <option value="false">Inactifs seulement</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" placeholder="Rechercher par code ou nom..." @bind="searchTerm" @oninput="OnSearchInput" />
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-outline-secondary" @onclick="RefreshCoupons">
                                <i class="fas fa-sync-alt me-2"></i>
                                Actualiser
                            </button>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                    }
                    else if (coupons.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Code</th>
                                        <th>Nom</th>
                                        <th>Type</th>
                                        <th>Valeur</th>
                                        <th>Période</th>
                                        <th>Utilisations</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var coupon in filteredCoupons)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@coupon.Code</strong>
                                            </td>
                                            <td>@coupon.Name</td>
                                            <td>
                                                <span class="badge @GetTypeBadgeClass(coupon.Type)">
                                                    @GetTypeText(coupon.Type)
                                                </span>
                                            </td>
                                            <td>
                                                @if (coupon.Type == CouponType.Percentage)
                                                {
                                                    <text>@coupon.Value%</text>
                                                }
                                                else if (coupon.Type == CouponType.FixedAmount)
                                                {
                                                    <text>@coupon.Value.ToString("N0") GNF</text>
                                                }
                                                else
                                                {
                                                    <text>-</text>
                                                }
                                            </td>
                                            <td>
                                                <small>
                                                    Du @coupon.StartDate.ToString("dd/MM/yyyy")<br />
                                                    Au @coupon.EndDate.ToString("dd/MM/yyyy")
                                                </small>
                                            </td>
                                            <td>
                                                @coupon.UsageCount
                                                @if (coupon.UsageLimit.HasValue)
                                                {
                                                    <text> / @coupon.UsageLimit</text>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge @(coupon.IsActive ? "bg-success" : "bg-danger")">
                                                    @(coupon.IsActive ? "Actif" : "Inactif")
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => EditCoupon(coupon)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" @onclick="() => ViewCouponStats(coupon)">
                                                        <i class="fas fa-chart-bar"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteCoupon(coupon)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun coupon trouvé</h5>
                            <p class="text-muted">Créez votre premier coupon pour commencer.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Création/Édition Coupon -->
<div class="modal fade" id="couponModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    @(isEditMode ? "Modifier le Coupon" : "Nouveau Coupon")
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <EditForm Model="currentCoupon" OnValidSubmit="SaveCoupon">
                    <DataAnnotationsValidator />
                    <ValidationSummary class="text-danger" />

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Code du Coupon *</label>
                                @if (isEditMode)
                                {
                                    <InputText class="form-control" @bind-Value="currentCoupon.Code" placeholder="Ex: WELCOME10" readonly />
                                    <small class="form-text text-muted">Le code du coupon ne peut pas être modifié après création</small>
                                }
                                else
                                {
                                    <InputText class="form-control" @bind-Value="currentCoupon.Code" placeholder="Ex: WELCOME10" />
                                }
                                <ValidationMessage For="@(() => currentCoupon.Code)" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nom du Coupon *</label>
                                <InputText class="form-control" @bind-Value="currentCoupon.Name" placeholder="Ex: Bienvenue 10%" />
                                <ValidationMessage For="@(() => currentCoupon.Name)" />
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <InputTextArea class="form-control" @bind-Value="currentCoupon.Description" rows="3" placeholder="Description du coupon..." />
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Type de Coupon *</label>
                                <InputSelect class="form-select" @bind-Value="currentCoupon.Type">
                                    <option value="@CouponType.FixedAmount">Montant Fixe</option>
                                    <option value="@CouponType.Percentage">Pourcentage</option>
                                    <option value="@CouponType.FreeShipping">Livraison Gratuite</option>
                                </InputSelect>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">
                                    Valeur *
                                    @if (currentCoupon.Type == CouponType.Percentage)
                                    {
                                        <small class="text-muted">(en %)</small>
                                    }
                                    else if (currentCoupon.Type == CouponType.FixedAmount)
                                    {
                                        <small class="text-muted">(en GNF)</small>
                                    }
                                </label>
                                <InputNumber class="form-control" @bind-Value="currentCoupon.Value" />
                                <ValidationMessage For="@(() => currentCoupon.Value)" />
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date de Début *</label>
                                <InputDate class="form-control" @bind-Value="currentCoupon.StartDate" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date de Fin *</label>
                                <InputDate class="form-control" @bind-Value="currentCoupon.EndDate" />
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Montant Minimum de Commande (GNF)</label>
                                <InputNumber class="form-control" @bind-Value="currentCoupon.MinimumOrderAmount" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Réduction Maximum (GNF)</label>
                                <InputNumber class="form-control" @bind-Value="currentCoupon.MaximumDiscountAmount" />
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Limite d'Utilisation Totale</label>
                                <InputNumber class="form-control" @bind-Value="currentCoupon.UsageLimit" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Limite par Utilisateur</label>
                                <InputNumber class="form-control" @bind-Value="currentCoupon.UsageLimitPerUser" />
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <InputCheckbox class="form-check-input" @bind-Value="currentCoupon.IsActive" />
                            <label class="form-check-label">Coupon Actif</label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <InputCheckbox class="form-check-input" @bind-Value="currentCoupon.ApplicableToAllProducts" />
                            <label class="form-check-label">Applicable à Tous les Produits</label>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">
                            @(isEditMode ? "Modifier" : "Créer")
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

@code {
    private List<CouponDto> coupons = new();
    private List<CouponDto> filteredCoupons = new();
    private bool isLoading = true;
    private string searchTerm = "";
    private bool? activeFilter = null;
    private bool isEditMode = false;
    private CreateCouponRequest currentCoupon = new();
    private int currentCouponId = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadCoupons();
    }

    private async Task LoadCoupons()
    {
        isLoading = true;
        try
        {
            coupons = await CouponService.GetCouponsAsync();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur lors du chargement des coupons: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ApplyFilters()
    {
        filteredCoupons = coupons.Where(c =>
            (string.IsNullOrEmpty(searchTerm) ||
             c.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             c.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) &&
            (!activeFilter.HasValue || c.IsActive == activeFilter.Value)
        ).ToList();
    }

    private async Task OnFilterChange(ChangeEventArgs e)
    {
        var value = e.Value?.ToString();
        activeFilter = string.IsNullOrEmpty(value) ? null : bool.Parse(value);
        ApplyFilters();
        await Task.CompletedTask;
    }

    private async Task OnSearchInput(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? "";
        ApplyFilters();
        await Task.CompletedTask;
    }

    private async Task RefreshCoupons()
    {
        await LoadCoupons();
    }

    private async Task OpenCreateCouponModal()
    {
        isEditMode = false;
        currentCouponId = 0; // Réinitialiser l'ID pour la création
        currentCoupon = new CreateCouponRequest
        {
            StartDate = DateTime.Today,
            EndDate = DateTime.Today.AddDays(30),
            Type = CouponType.Percentage,
            IsActive = true,
            ApplicableToAllProducts = true,
            Currency = "GNF"
        };
        await JSRuntime.InvokeVoidAsync("showModal", "couponModal");
    }

    private async Task EditCoupon(CouponDto coupon)
    {
        isEditMode = true;
        currentCouponId = coupon.Id; // Stocker l'ID du coupon en cours de modification
        currentCoupon = new CreateCouponRequest
        {
            Code = coupon.Code,
            Name = coupon.Name,
            Description = coupon.Description,
            Type = coupon.Type,
            Value = coupon.Value,
            MinimumOrderAmount = coupon.MinimumOrderAmount,
            MaximumDiscountAmount = coupon.MaximumDiscountAmount,
            StartDate = coupon.StartDate,
            EndDate = coupon.EndDate,
            UsageLimit = coupon.UsageLimit,
            UsageLimitPerUser = coupon.UsageLimitPerUser,
            IsActive = coupon.IsActive,
            ApplicableToAllProducts = coupon.ApplicableToAllProducts,
            ApplicableProductIds = coupon.ApplicableProductIds,
            ApplicableCategoryIds = coupon.ApplicableCategoryIds,
            ApplicableSellerIds = coupon.ApplicableSellerIds,
            ExcludedProductIds = coupon.ExcludedProductIds,
            ExcludedCategoryIds = coupon.ExcludedCategoryIds
        };
        await JSRuntime.InvokeVoidAsync("showModal", "couponModal");
    }

    private async Task SaveCoupon()
    {
        try
        {
            if (isEditMode)
            {
                var updateRequest = new UpdateCouponRequest
                {
                    Name = currentCoupon.Name,
                    Description = currentCoupon.Description,
                    Value = currentCoupon.Value,
                    MinimumOrderAmount = currentCoupon.MinimumOrderAmount,
                    MaximumDiscountAmount = currentCoupon.MaximumDiscountAmount,
                    StartDate = currentCoupon.StartDate,
                    EndDate = currentCoupon.EndDate,
                    UsageLimit = currentCoupon.UsageLimit,
                    UsageLimitPerUser = currentCoupon.UsageLimitPerUser,
                    IsActive = currentCoupon.IsActive,
                    ApplicableToAllProducts = currentCoupon.ApplicableToAllProducts,
                    ApplicableProductIds = currentCoupon.ApplicableProductIds,
                    ApplicableCategoryIds = currentCoupon.ApplicableCategoryIds,
                    ApplicableSellerIds = currentCoupon.ApplicableSellerIds,
                    ExcludedProductIds = currentCoupon.ExcludedProductIds,
                    ExcludedCategoryIds = currentCoupon.ExcludedCategoryIds
                };

                if (currentCouponId > 0)
                {
                    var result = await CouponService.UpdateCouponAsync(currentCouponId, updateRequest);
                    if (result != null)
                    {
                        NotificationService.Success("Coupon modifié avec succès!");
                        await JSRuntime.InvokeVoidAsync("hideModal", "couponModal");

                        // Petit délai pour s'assurer que la modification est bien terminée côté serveur
                        await Task.Delay(500);
                        await LoadCoupons();
                    }
                    else
                    {
                        NotificationService.Error("Erreur lors de la modification du coupon");
                    }
                }
            }
            else
            {
                var result = await CouponService.CreateCouponAsync(currentCoupon);
                if (result != null)
                {
                    NotificationService.Success("Coupon créé avec succès!");
                    await JSRuntime.InvokeVoidAsync("hideModal", "couponModal");
                    await LoadCoupons();
                }
                else
                {
                    NotificationService.Error("Erreur lors de la création du coupon");
                }
            }
        }
        catch (Exception ex)
        {
            NotificationService.Error($"Erreur: {ex.Message}");
        }
    }

    private async Task DeleteCoupon(CouponDto coupon)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", $"Êtes-vous sûr de vouloir supprimer le coupon '{coupon.Code}' ?"))
        {
            try
            {
                var success = await CouponService.DeleteCouponAsync(coupon.Id);
                if (success)
                {
                    NotificationService.Success("Coupon supprimé avec succès!");
                    await LoadCoupons();
                }
                else
                {
                    NotificationService.Error("Erreur lors de la suppression du coupon");
                }
            }
            catch (Exception ex)
            {
                NotificationService.Error($"Erreur: {ex.Message}");
            }
        }
    }

    private async Task ViewCouponStats(CouponDto coupon)
    {
        // TODO: Implémenter la vue des statistiques
        NotificationService.Info("Statistiques du coupon - À implémenter");
        await Task.CompletedTask;
    }

    private string GetTypeBadgeClass(CouponType type)
    {
        return type switch
        {
            CouponType.FixedAmount => "bg-primary",
            CouponType.Percentage => "bg-success",
            CouponType.FreeShipping => "bg-info",
            CouponType.BuyXGetY => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetTypeText(CouponType type)
    {
        return type switch
        {
            CouponType.FixedAmount => "Montant Fixe",
            CouponType.Percentage => "Pourcentage",
            CouponType.FreeShipping => "Livraison Gratuite",
            CouponType.BuyXGetY => "Achetez X Obtenez Y",
            _ => "Inconnu"
        };
    }
}
