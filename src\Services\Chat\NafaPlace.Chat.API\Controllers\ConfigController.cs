using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Chat.Application.Services;
using System.Security.Claims;

namespace NafaPlace.Chat.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ConfigController : ControllerBase
{
    private readonly IChatService _chatService;
    private readonly ILogger<ConfigController> _logger;

    public ConfigController(IChatService chatService, ILogger<ConfigController> logger)
    {
        _chatService = chatService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> GetChatConfig()
    {
        try
        {
            var config = await _chatService.GetChatConfigAsync();
            return Ok(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la configuration");
            return Ok(new Dictionary<string, object>
            {
                ["maxFileSize"] = 10 * 1024 * 1024, // 10MB
                ["allowedFileTypes"] = new[] { "image/jpeg", "image/png", "image/gif", "application/pdf", "text/plain" },
                ["maxMessageLength"] = 2000,
                ["typingIndicatorTimeout"] = 3000,
                ["autoAssignAgents"] = true,
                ["businessHours"] = new { start = "09:00", end = "18:00" }
            });
        }
    }

    [HttpPut]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> UpdateChatConfig([FromBody] Dictionary<string, object> config)
    {
        try
        {
            var success = await _chatService.UpdateChatConfigAsync(config);
            
            if (success)
            {
                return Ok(new { message = "Configuration mise à jour avec succès" });
            }

            return BadRequest("Impossible de mettre à jour la configuration");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la configuration");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpPut("presence")]
    [Authorize]
    public async Task<IActionResult> UpdatePresence([FromBody] UpdatePresenceRequest request)
    {
        try
        {
            var userId = GetUserId();
            await _chatService.UpdateUserPresenceAsync(userId, request.Status);
            
            return Ok(new { message = "Présence mise à jour" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la présence");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("presence/{conversationId}")]
    [Authorize]
    public async Task<IActionResult> GetConversationPresence(int conversationId)
    {
        try
        {
            var presence = await _chatService.GetOnlineUsersAsync(conversationId);
            return Ok(presence);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la présence");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    [HttpGet("health")]
    public async Task<IActionResult> HealthCheck()
    {
        try
        {
            var isHealthy = await _chatService.TestChatServiceAsync();
            
            if (isHealthy)
            {
                return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
            }

            return StatusCode(503, new { status = "unhealthy", timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du test de santé");
            return StatusCode(503, new { status = "unhealthy", error = ex.Message, timestamp = DateTime.UtcNow });
        }
    }

    [HttpGet("stats")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetChatStats([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        try
        {
            var stats = await _chatService.GetChatStatsAsync(
                startDate ?? DateTime.UtcNow.AddDays(-30),
                endDate ?? DateTime.UtcNow);

            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    private string GetUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? 
               User.FindFirst("sub")?.Value ?? 
               "anonymous";
    }
}

public class UpdatePresenceRequest
{
    public UserPresenceStatus Status { get; set; }
}
