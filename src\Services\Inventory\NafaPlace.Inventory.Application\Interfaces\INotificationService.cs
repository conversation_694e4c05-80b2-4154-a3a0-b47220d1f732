namespace NafaPlace.Inventory.Application.Interfaces;

public interface INotificationService
{
    Task SendLowStockNotificationAsync(int productId, string productName, int currentStock, int threshold, string sellerId);
    Task SendOutOfStockNotificationAsync(int productId, string productName, string sellerId);
    Task SendStockMovementNotificationAsync(int productId, string productName, int quantity, string movementType, string sellerId);
    Task SendBulkNotificationAsync(List<string> userIds, string title, string message, string type = "info");
}
