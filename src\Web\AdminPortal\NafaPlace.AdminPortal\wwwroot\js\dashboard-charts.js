// Variables globales pour stocker les instances des graphiques
let salesChart = null;
let categoriesChart = null;
let chartsInitialized = false;

window.initializeDashboardCharts = function() {
    // Éviter les initialisations multiples
    if (chartsInitialized) {
        console.log('Charts already initialized, skipping...');
        return;
    }

    // Détruire les graphiques existants s'ils existent
    if (salesChart) {
        salesChart.destroy();
        salesChart = null;
    }
    if (categoriesChart) {
        categoriesChart.destroy();
        categoriesChart = null;
    }

    // Sales Chart
    const salesCtx = document.getElementById('salesChart');
    if (salesCtx) {
        salesChart = new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
                datasets: [{
                    label: 'Ventes',
                    data: [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: '#E73C30',
                    backgroundColor: 'rgba(231, 60, 48, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: false, // Désactiver complètement les animations
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Orders Chart
    const ordersCtx = document.getElementById('ordersChart');
    if (ordersCtx) {
        new Chart(ordersCtx, {
            type: 'bar',
            data: {
                labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
                datasets: [{
                    label: 'Commandes',
                    data: [65, 59, 80, 81, 56, 55, 40],
                    backgroundColor: '#F96302',
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Categories Chart
    const categoriesCtx = document.getElementById('categoriesChart');
    if (categoriesCtx) {
        categoriesChart = new Chart(categoriesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Mode', 'Électronique', 'Maison', 'Sport', 'Livres'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: [
                        '#E73C30',
                        '#F96302',
                        '#28a745',
                        '#17a2b8',
                        '#6f42c1'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: false, // Désactiver complètement les animations
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    // Marquer comme initialisé
    chartsInitialized = true;
    console.log('Charts initialized successfully');
};

// Fonction pour réinitialiser les graphiques
window.resetDashboardCharts = function() {
    chartsInitialized = false;
    if (salesChart) {
        salesChart.destroy();
        salesChart = null;
    }
    if (categoriesChart) {
        categoriesChart.destroy();
        categoriesChart = null;
    }
    console.log('Charts reset');
};
