# Script pour créer les migrations Entity Framework pour le service Inventory
# Exécuter depuis le répertoire Infrastructure

Write-Host "Création des migrations pour le service Inventory..." -ForegroundColor Green

# Naviguer vers le répertoire Infrastructure
Set-Location "C:\Users\<USER>\Documents\nafaplace\nafaplace\src\Services\Inventory\NafaPlace.Inventory.Infrastructure"

# Créer la migration initiale
Write-Host "Création de la migration initiale..." -ForegroundColor Yellow
dotnet ef migrations add InitialInventoryMigration --startup-project ..\NafaPlace.Inventory.API\NafaPlace.Inventory.API.csproj --context InventoryDbContext

Write-Host "Migration créée avec succès!" -ForegroundColor Green
Write-Host "Pour appliquer la migration, exécutez:" -ForegroundColor Cyan
Write-Host "dotnet ef database update --startup-project ..\NafaPlace.Inventory.API\NafaPlace.Inventory.API.csproj --context InventoryDbContext" -ForegroundColor Cyan
