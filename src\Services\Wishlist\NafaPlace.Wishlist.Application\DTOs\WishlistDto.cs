namespace NafaPlace.Wishlist.Application.DTOs;

public class WishlistDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsPublic { get; set; }
    public DateTime LastUpdated { get; set; }
    public List<WishlistItemDto> Items { get; set; } = new();
    public int ItemCount { get; set; }
    public decimal TotalValue { get; set; }
    public string Currency { get; set; } = "GNF";
}

public class WishlistItemDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal ProductPrice { get; set; }
    public string Currency { get; set; } = "GNF";
    public string? ProductImageUrl { get; set; }
    public string? ProductBrand { get; set; }
    public int? CategoryId { get; set; }
    public string? CategoryName { get; set; }
    public bool IsAvailable { get; set; }
    public DateTime AddedAt { get; set; }
}

public class WishlistSummaryDto
{
    public int Id { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int ItemCount { get; set; }
    public decimal TotalValue { get; set; }
    public string Currency { get; set; } = "GNF";
    public DateTime LastUpdated { get; set; }
}
