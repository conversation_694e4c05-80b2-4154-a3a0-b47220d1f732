/* CSS Global pour le Chat Flottant - Force le positionnement */

/* Sélecteur très spécifique pour éviter les conflits */
body > .floating-chat-widget,
body > #floating-chat-widget,
.floating-chat-widget {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 2147483647 !important; /* Valeur maximale */
    pointer-events: auto !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
    margin: 0 !important;
    padding: 0 !important;
    top: auto !important;
    left: auto !important;
    width: auto !important;
    height: auto !important;
    max-width: none !important;
    max-height: none !important;
    min-width: 0 !important;
    min-height: 0 !important;
    background: transparent !important;
    border: none !important;
    outline: none !important;
    box-sizing: border-box !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* S'assurer que le bouton de chat est toujours visible */
body > .floating-chat-widget .chat-toggle-btn,
.floating-chat-widget .chat-toggle-btn {
    position: relative !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    z-index: inherit !important;
}

/* S'assurer que la fenêtre de chat est correctement positionnée */
body > .floating-chat-widget .chat-window,
.floating-chat-widget .chat-window {
    position: absolute !important;
    bottom: 80px !important;
    right: 0 !important;
    z-index: inherit !important;
}

/* Responsive pour mobile */
@media (max-width: 768px) {
    body > .floating-chat-widget,
    body > #floating-chat-widget,
    .floating-chat-widget {
        bottom: 15px !important;
        right: 15px !important;
    }
}

/* Éviter que d'autres éléments interfèrent */
body {
    position: relative !important;
}

/* Forcer le widget à rester au-dessus de tout */
.floating-chat-widget * {
    box-sizing: border-box !important;
}

/* Éviter les transformations CSS qui pourraient affecter le positionnement */
.floating-chat-widget {
    will-change: auto !important;
    contain: none !important;
    isolation: auto !important;
}
