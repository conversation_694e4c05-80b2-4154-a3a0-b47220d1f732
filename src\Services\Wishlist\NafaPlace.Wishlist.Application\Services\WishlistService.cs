using Microsoft.Extensions.Logging;
using NafaPlace.Wishlist.Application.DTOs;
using NafaPlace.Wishlist.Application.DTOs.Requests;
using NafaPlace.Wishlist.Domain.Models;
using NafaPlace.Wishlist.Infrastructure.Repositories;

namespace NafaPlace.Wishlist.Application.Services;

public class WishlistService : IWishlistService
{
    private readonly IWishlistRepository _wishlistRepository;
    private readonly ILogger<WishlistService> _logger;

    public WishlistService(IWishlistRepository wishlistRepository, ILogger<WishlistService> logger)
    {
        _wishlistRepository = wishlistRepository;
        _logger = logger;
    }

    public async Task<WishlistDto> GetUserWishlistAsync(string userId)
    {
        var wishlist = await _wishlistRepository.GetUserWishlistAsync(userId);
        if (wishlist == null)
        {
            // Create default wishlist if none exists
            wishlist = await _wishlistRepository.CreateWishlistAsync(new Domain.Models.Wishlist
            {
                UserId = userId,
                Name = "Ma liste de souhaits",
                IsPublic = false
            });
        }

        return MapToDto(wishlist);
    }

    public async Task<WishlistDto> CreateWishlistAsync(string userId, CreateWishlistRequest request)
    {
        var wishlist = new Domain.Models.Wishlist
        {
            UserId = userId,
            Name = request.Name,
            Description = request.Description,
            IsPublic = request.IsPublic
        };

        var createdWishlist = await _wishlistRepository.CreateWishlistAsync(wishlist);
        return MapToDto(createdWishlist);
    }

    public async Task<WishlistDto> UpdateWishlistAsync(string userId, int wishlistId, UpdateWishlistRequest request)
    {
        var wishlist = await _wishlistRepository.GetWishlistByIdAsync(wishlistId);
        if (wishlist == null || wishlist.UserId != userId)
        {
            throw new UnauthorizedAccessException("Wishlist not found or access denied");
        }

        if (!string.IsNullOrEmpty(request.Name))
            wishlist.Name = request.Name;
        
        if (request.Description != null)
            wishlist.Description = request.Description;
        
        if (request.IsPublic.HasValue)
            wishlist.IsPublic = request.IsPublic.Value;

        wishlist.LastUpdated = DateTime.UtcNow;

        var updatedWishlist = await _wishlistRepository.UpdateWishlistAsync(wishlist);
        return MapToDto(updatedWishlist);
    }

    public async Task<bool> DeleteWishlistAsync(string userId, int wishlistId)
    {
        var wishlist = await _wishlistRepository.GetWishlistByIdAsync(wishlistId);
        if (wishlist == null || wishlist.UserId != userId)
        {
            return false;
        }

        return await _wishlistRepository.DeleteWishlistAsync(wishlistId);
    }

    public async Task<List<WishlistSummaryDto>> GetUserWishlistsAsync(string userId)
    {
        var wishlists = await _wishlistRepository.GetUserWishlistsAsync(userId);
        return wishlists.Select(MapToSummaryDto).ToList();
    }

    public async Task<WishlistItemDto> AddToWishlistAsync(string userId, AddToWishlistRequest request)
    {
        // Get or create user's default wishlist
        var wishlist = await _wishlistRepository.GetUserWishlistAsync(userId);
        if (wishlist == null)
        {
            wishlist = await _wishlistRepository.CreateWishlistAsync(new Domain.Models.Wishlist
            {
                UserId = userId,
                Name = "Ma liste de souhaits",
                IsPublic = false
            });
        }

        // Check if item already exists
        var existingItem = await _wishlistRepository.GetWishlistItemAsync(userId, request.ProductId);
        if (existingItem != null)
        {
            return MapToItemDto(existingItem);
        }

        var wishlistItem = new WishlistItem
        {
            UserId = userId,
            ProductId = request.ProductId,
            ProductName = request.ProductName,
            ProductPrice = request.ProductPrice,
            Currency = request.Currency,
            ProductImageUrl = request.ProductImageUrl,
            ProductBrand = request.ProductBrand,
            CategoryId = request.CategoryId,
            CategoryName = request.CategoryName,
            IsAvailable = true
        };

        var addedItem = await _wishlistRepository.AddItemToWishlistAsync(wishlist.Id, wishlistItem);
        return MapToItemDto(addedItem);
    }

    public async Task<bool> RemoveFromWishlistAsync(string userId, int productId)
    {
        return await _wishlistRepository.RemoveItemFromWishlistAsync(userId, productId);
    }

    public async Task<bool> IsProductInWishlistAsync(string userId, int productId)
    {
        var item = await _wishlistRepository.GetWishlistItemAsync(userId, productId);
        return item != null;
    }

    public async Task<WishlistItemDto?> GetWishlistItemAsync(string userId, int productId)
    {
        var item = await _wishlistRepository.GetWishlistItemAsync(userId, productId);
        return item != null ? MapToItemDto(item) : null;
    }

    public async Task<List<WishlistItemDto>> GetWishlistItemsAsync(string userId, int page = 1, int pageSize = 20)
    {
        var items = await _wishlistRepository.GetWishlistItemsAsync(userId, page, pageSize);
        return items.Select(MapToItemDto).ToList();
    }

    public async Task<bool> ClearWishlistAsync(string userId)
    {
        return await _wishlistRepository.ClearWishlistAsync(userId);
    }

    public async Task<int> GetWishlistItemCountAsync(string userId)
    {
        return await _wishlistRepository.GetWishlistItemCountAsync(userId);
    }

    public async Task<bool> MoveToCartAsync(string userId, int productId)
    {
        // This would integrate with the Cart service
        // For now, just remove from wishlist
        return await RemoveFromWishlistAsync(userId, productId);
    }

    public async Task<List<WishlistItemDto>> GetRecentlyAddedItemsAsync(string userId, int count = 5)
    {
        var items = await _wishlistRepository.GetRecentlyAddedItemsAsync(userId, count);
        return items.Select(MapToItemDto).ToList();
    }

    public async Task<bool> UpdateProductAvailabilityAsync(int productId, bool isAvailable)
    {
        return await _wishlistRepository.UpdateProductAvailabilityAsync(productId, isAvailable);
    }

    private static WishlistDto MapToDto(Domain.Models.Wishlist wishlist)
    {
        return new WishlistDto
        {
            Id = wishlist.Id,
            UserId = wishlist.UserId,
            Name = wishlist.Name ?? "Ma liste de souhaits",
            Description = wishlist.Description,
            IsPublic = wishlist.IsPublic,
            LastUpdated = wishlist.LastUpdated,
            Items = wishlist.Items?.Select(MapToItemDto).ToList() ?? new List<WishlistItemDto>(),
            ItemCount = wishlist.ItemCount,
            TotalValue = wishlist.TotalValue,
            Currency = "GNF"
        };
    }

    private static WishlistSummaryDto MapToSummaryDto(Domain.Models.Wishlist wishlist)
    {
        return new WishlistSummaryDto
        {
            Id = wishlist.Id,
            UserId = wishlist.UserId,
            Name = wishlist.Name ?? "Ma liste de souhaits",
            ItemCount = wishlist.ItemCount,
            TotalValue = wishlist.TotalValue,
            Currency = "GNF",
            LastUpdated = wishlist.LastUpdated
        };
    }

    private static WishlistItemDto MapToItemDto(WishlistItem item)
    {
        return new WishlistItemDto
        {
            Id = item.Id,
            UserId = item.UserId,
            ProductId = item.ProductId,
            ProductName = item.ProductName,
            ProductPrice = item.ProductPrice,
            Currency = item.Currency,
            ProductImageUrl = item.ProductImageUrl,
            ProductBrand = item.ProductBrand,
            CategoryId = item.CategoryId,
            CategoryName = item.CategoryName,
            IsAvailable = item.IsAvailable,
            AddedAt = item.AddedAt
        };
    }
}
