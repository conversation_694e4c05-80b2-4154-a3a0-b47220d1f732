using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Delivery.Application.Services;
using NafaPlace.Delivery.Application.DTOs;

namespace NafaPlace.Delivery.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DeliveryController : ControllerBase
{
    private readonly IDeliveryService _deliveryService;
    private readonly ILogger<DeliveryController> _logger;

    public DeliveryController(IDeliveryService deliveryService, ILogger<DeliveryController> logger)
    {
        _deliveryService = deliveryService;
        _logger = logger;
    }

    [HttpGet("zones")]
    public async Task<ActionResult<List<DeliveryZoneDto>>> GetDeliveryZones()
    {
        try
        {
            var zones = await _deliveryService.GetDeliveryZonesAsync();
            return Ok(zones);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery zones");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("calculate-fee")]
    public async Task<ActionResult<decimal>> CalculateDeliveryFee([FromQuery] string address, [FromQuery] decimal orderAmount)
    {
        try
        {
            var fee = await _deliveryService.CalculateDeliveryFeeAsync(address, orderAmount);
            return Ok(fee);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating delivery fee");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("calculate-advanced-fee")]
    public async Task<ActionResult<decimal>> CalculateAdvancedDeliveryFee([FromBody] AdvancedFeeCalculationRequest request)
    {
        try
        {
            var fee = await _deliveryService.CalculateAdvancedDeliveryFeeAsync(
                request.ZoneId,
                request.OrderAmount,
                request.Weight,
                request.Volume,
                request.DeliveryType
            );

            if (fee < 0)
                return BadRequest("Service de livraison non disponible pour cette zone");

            return Ok(fee);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating advanced delivery fee");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("quotes")]
    public async Task<ActionResult<List<DeliveryQuoteDto>>> GetDeliveryQuotes([FromBody] DeliveryQuoteRequest request)
    {
        try
        {
            var quotes = await _deliveryService.GetDeliveryQuotesAsync(
                request.DeliveryAddress,
                request.OrderValue,
                request.Weight,
                request.Volume,
                request.DeliveryLatitude,
                request.DeliveryLongitude
            );

            return Ok(quotes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery quotes");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("orders")]
    [Authorize]
    public async Task<ActionResult<DeliveryOrderDto>> CreateDeliveryOrder([FromBody] CreateDeliveryOrderRequest request)
    {
        try
        {
            var deliveryOrder = await _deliveryService.CreateDeliveryOrderAsync(request);
            return Ok(deliveryOrder);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating delivery order");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("orders/{id}")]
    public async Task<ActionResult<DeliveryOrderDto>> GetDeliveryOrder(int id)
    {
        try
        {
            var deliveryOrder = await _deliveryService.GetDeliveryOrderAsync(id);
            if (deliveryOrder == null)
                return NotFound();

            return Ok(deliveryOrder);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery order {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("orders/tracking/{trackingNumber}")]
    public async Task<ActionResult<List<DeliveryTrackingDto>>> GetDeliveryTracking(string trackingNumber)
    {
        try
        {
            var tracking = await _deliveryService.GetDeliveryTrackingByTrackingNumberAsync(trackingNumber);
            return Ok(tracking);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery tracking for {TrackingNumber}", trackingNumber);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("orders/{id}/status")]
    [Authorize]
    public async Task<ActionResult> UpdateDeliveryStatus(int id, [FromBody] UpdateDeliveryStatusRequest request)
    {
        try
        {
            var result = await _deliveryService.UpdateDeliveryStatusAsync(id, request);
            if (!result)
                return NotFound();

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating delivery status for order {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { status = "healthy", service = "delivery-api", timestamp = DateTime.UtcNow });
    }
}
