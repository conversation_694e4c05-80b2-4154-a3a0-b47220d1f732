# Script de test rapide pour NafaPlace
# Auteur: Assistant IA
# Date: 2025-01-28

Write-Host "🧪 Test rapide des fonctionnalités NafaPlace" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost:5000" # API Gateway
$services = @{
    "Analytics" = "http://localhost:5006"
    "Chat" = "http://localhost:5007"
    "Recommendation" = "http://localhost:5008"
    "Loyalty" = "http://localhost:5009"
    "Localization" = "http://localhost:5010"
}

# Test de santé des services
function Test-ServiceHealth {
    param($serviceName, $url)
    
    try {
        $response = Invoke-RestMethod -Uri "$url/health" -Method Get -TimeoutSec 5
        Write-Host "✅ $serviceName : Service opérationnel" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ $serviceName : Service indisponible" -ForegroundColor Red
        return $false
    }
}

# Test des fonctionnalités principales
function Test-MainFeatures {
    Write-Host "`n🔍 Test des fonctionnalités principales..." -ForegroundColor Cyan
    
    # Test Analytics
    try {
        $analyticsResponse = Invoke-RestMethod -Uri "$($services.Analytics)/api/analytics/kpis" -Method Get -TimeoutSec 10
        Write-Host "✅ Analytics : KPIs récupérés" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️ Analytics : Erreur lors du test" -ForegroundColor Yellow
    }
    
    # Test Chat
    try {
        $chatResponse = Invoke-RestMethod -Uri "$($services.Chat)/api/faq" -Method Get -TimeoutSec 10
        Write-Host "✅ Chat : FAQ récupérées" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️ Chat : Erreur lors du test" -ForegroundColor Yellow
    }
    
    # Test Localization
    try {
        $localizationResponse = Invoke-RestMethod -Uri "$($services.Localization)/api/languages" -Method Get -TimeoutSec 10
        Write-Host "✅ Localization : Langues récupérées" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️ Localization : Erreur lors du test" -ForegroundColor Yellow
    }
}

# Test de connectivité de base
function Test-BasicConnectivity {
    Write-Host "🌐 Test de connectivité de base..." -ForegroundColor Cyan
    
    $healthyServices = 0
    $totalServices = $services.Count
    
    foreach ($service in $services.GetEnumerator()) {
        if (Test-ServiceHealth $service.Key $service.Value) {
            $healthyServices++
        }
        Start-Sleep -Seconds 1
    }
    
    $healthRate = [math]::Round(($healthyServices / $totalServices) * 100, 2)
    Write-Host "`n📊 Santé des services : $healthyServices/$totalServices ($healthRate%)" -ForegroundColor $(if ($healthRate -ge 80) { "Green" } else { "Yellow" })
    
    return $healthRate -ge 80
}

# Test des bases de données
function Test-DatabaseConnectivity {
    Write-Host "`n🗄️ Test de connectivité des bases de données..." -ForegroundColor Cyan
    
    try {
        # Test PostgreSQL
        $pgTest = docker exec nafaplace-postgres pg_isready -U nafaplace
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PostgreSQL : Connecté" -ForegroundColor Green
        } else {
            Write-Host "❌ PostgreSQL : Non connecté" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ PostgreSQL : Erreur de test" -ForegroundColor Red
    }
    
    try {
        # Test Redis
        $redisTest = docker exec nafaplace-redis redis-cli ping
        if ($redisTest -eq "PONG") {
            Write-Host "✅ Redis : Connecté" -ForegroundColor Green
        } else {
            Write-Host "❌ Redis : Non connecté" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Redis : Erreur de test" -ForegroundColor Red
    }
}

# Test des portails web
function Test-WebPortals {
    Write-Host "`n🖥️ Test des portails web..." -ForegroundColor Cyan
    
    $portals = @{
        "Site Web Principal" = "http://localhost:8080"
        "Portail Admin" = "http://localhost:8081"
        "Portail Vendeur" = "http://localhost:8082"
    }
    
    foreach ($portal in $portals.GetEnumerator()) {
        try {
            $response = Invoke-WebRequest -Uri $portal.Value -Method Get -TimeoutSec 10 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ $($portal.Key) : Accessible" -ForegroundColor Green
            } else {
                Write-Host "⚠️ $($portal.Key) : Code de statut $($response.StatusCode)" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "❌ $($portal.Key) : Non accessible" -ForegroundColor Red
        }
    }
}

# Affichage des informations système
function Show-SystemInfo {
    Write-Host "`n💻 Informations système..." -ForegroundColor Cyan
    
    # Conteneurs Docker
    try {
        $containers = docker ps --filter "name=nafaplace" --format "table {{.Names}}\t{{.Status}}" | Select-Object -Skip 1
        Write-Host "🐳 Conteneurs Docker actifs :" -ForegroundColor Yellow
        $containers | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
    }
    catch {
        Write-Host "❌ Impossible de récupérer les informations Docker" -ForegroundColor Red
    }
    
    # Utilisation des ressources
    try {
        $dockerStats = docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | Select-Object -Skip 1 | Select-Object -First 5
        Write-Host "`n📊 Utilisation des ressources (top 5) :" -ForegroundColor Yellow
        $dockerStats | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
    }
    catch {
        Write-Host "❌ Impossible de récupérer les statistiques Docker" -ForegroundColor Red
    }
}

# Fonction principale
function Start-QuickTest {
    Write-Host "🚀 Démarrage du test rapide..." -ForegroundColor Green
    
    # Test de connectivité de base
    $servicesOk = Test-BasicConnectivity
    
    # Test des bases de données
    Test-DatabaseConnectivity
    
    # Test des portails web
    Test-WebPortals
    
    # Test des fonctionnalités (si les services sont OK)
    if ($servicesOk) {
        Test-MainFeatures
    }
    
    # Informations système
    Show-SystemInfo
    
    # Résumé
    Write-Host "`n📋 RÉSUMÉ DU TEST RAPIDE" -ForegroundColor Magenta
    Write-Host "=========================" -ForegroundColor Magenta
    
    if ($servicesOk) {
        Write-Host "✅ La plupart des services sont opérationnels" -ForegroundColor Green
        Write-Host "🌐 Vous pouvez accéder aux portails web" -ForegroundColor Green
        Write-Host "🔗 API Gateway disponible sur : http://localhost:5000" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Certains services ne sont pas opérationnels" -ForegroundColor Yellow
        Write-Host "🔧 Vérifiez les logs avec : docker-compose logs" -ForegroundColor Yellow
    }
    
    Write-Host "`n🎯 URLs principales :" -ForegroundColor Cyan
    Write-Host "   • Site Web : http://localhost:8080" -ForegroundColor White
    Write-Host "   • Admin : http://localhost:8081" -ForegroundColor White
    Write-Host "   • Vendeur : http://localhost:8082" -ForegroundColor White
}

# Exécution du test
Start-QuickTest

Write-Host "`n🏁 Test rapide terminé !" -ForegroundColor Green
