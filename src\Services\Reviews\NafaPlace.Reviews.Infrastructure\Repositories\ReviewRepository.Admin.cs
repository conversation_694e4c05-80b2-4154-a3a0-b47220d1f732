using Microsoft.EntityFrameworkCore;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.Infrastructure.Data;

namespace NafaPlace.Reviews.Infrastructure.Repositories;

public partial class ReviewRepository : IReviewRepository
{
    // Admin-specific operations
    
    public async Task<int> GetTotalReviewsCountAsync()
    {
        return await _context.Reviews.CountAsync();
    }
    
    public async Task<int> GetPendingReviewsCountAsync()
    {
        return await _context.Reviews.CountAsync(r => r.Status == ReviewStatus.Pending);
    }
    
    public async Task<int> GetApprovedReviewsCountAsync()
    {
        return await _context.Reviews.CountAsync(r => r.Status == ReviewStatus.Published);
    }
    
    public async Task<int> GetRejectedReviewsCountAsync()
    {
        return await _context.Reviews.CountAsync(r => r.Status == ReviewStatus.Rejected);
    }
    
    public async Task<int> GetReportedReviewsCountAsync()
    {
        return await _context.Reviews.CountAsync(r => r.ReportCount > 0);
    }
    
    public async Task<double> GetOverallAverageRatingAsync()
    {
        var reviews = await _context.Reviews.Where(r => r.Status == ReviewStatus.Published).ToListAsync();
        return reviews.Any() ? reviews.Average(r => r.Rating) : 0;
    }
    
    public async Task<Dictionary<int, int>> GetOverallRatingDistributionAsync()
    {
        var distribution = await _context.Reviews
            .Where(r => r.Status == ReviewStatus.Published)
            .GroupBy(r => r.Rating)
            .Select(g => new { Rating = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Rating, x => x.Count);
            
        // Ensure all ratings 1-5 are present
        for (int i = 1; i <= 5; i++)
        {
            if (!distribution.ContainsKey(i))
                distribution[i] = 0;
        }
        
        return distribution;
    }
    
    public async Task<Dictionary<string, int>> GetReviewsByPeriodAsync()
    {
        var now = DateTime.UtcNow;
        var thisWeek = now.AddDays(-7);
        var thisMonth = now.AddMonths(-1);
        var thisQuarter = now.AddMonths(-3);
        
        var weekCount = await _context.Reviews.CountAsync(r => r.CreatedAt >= thisWeek);
        var monthCount = await _context.Reviews.CountAsync(r => r.CreatedAt >= thisMonth);
        var quarterCount = await _context.Reviews.CountAsync(r => r.CreatedAt >= thisQuarter);
        
        return new Dictionary<string, int>
        {
            { "Cette semaine", weekCount },
            { "Ce mois", monthCount },
            { "Ce trimestre", quarterCount }
        };
    }
    
    public async Task<IEnumerable<Review>> GetAdminReviewsAsync(
        string? status, 
        int? rating, 
        bool? isVerifiedPurchase, 
        string? dateFilter, 
        string? searchTerm, 
        int page, 
        int pageSize)
    {
        var query = _context.Reviews
            .Include(r => r.ReviewHelpfuls)
            .Include(r => r.Replies)
            .Include(r => r.Reports)
            .AsQueryable();
        
        // Apply status filter
        if (!string.IsNullOrEmpty(status))
        {
            query = status.ToLower() switch
            {
                "approved" => query.Where(r => r.Status == ReviewStatus.Published),
                "pending" => query.Where(r => r.Status == ReviewStatus.Pending),
                "rejected" => query.Where(r => r.Status == ReviewStatus.Rejected),
                _ => query
            };
        }
        
        // Apply rating filter
        if (rating.HasValue && rating > 0)
        {
            query = query.Where(r => r.Rating == rating);
        }
        
        // Apply verified purchase filter
        if (isVerifiedPurchase.HasValue)
        {
            query = query.Where(r => r.IsVerifiedPurchase == isVerifiedPurchase);
        }
        
        // Apply date filter
        if (!string.IsNullOrEmpty(dateFilter))
        {
            var now = DateTime.UtcNow;
            query = dateFilter.ToLower() switch
            {
                "today" => query.Where(r => r.CreatedAt.Date == now.Date),
                "week" => query.Where(r => r.CreatedAt >= now.AddDays(-7)),
                "month" => query.Where(r => r.CreatedAt >= now.AddMonths(-1)),
                "quarter" => query.Where(r => r.CreatedAt >= now.AddMonths(-3)),
                _ => query
            };
        }
        
        // Apply search filter
        if (!string.IsNullOrEmpty(searchTerm))
        {
            var searchLower = searchTerm.ToLower();
            query = query.Where(r => 
                r.Title.ToLower().Contains(searchLower) ||
                r.Comment.ToLower().Contains(searchLower) ||
                r.UserName.ToLower().Contains(searchLower));
        }
        
        return await query
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }
    
    public async Task<int> CountAdminReviewsAsync(
        string? status, 
        int? rating, 
        bool? isVerifiedPurchase, 
        string? dateFilter, 
        string? searchTerm)
    {
        var query = _context.Reviews.AsQueryable();
        
        // Apply same filters as GetAdminReviewsAsync
        if (!string.IsNullOrEmpty(status))
        {
            query = status.ToLower() switch
            {
                "approved" => query.Where(r => r.Status == ReviewStatus.Published),
                "pending" => query.Where(r => r.Status == ReviewStatus.Pending),
                "rejected" => query.Where(r => r.Status == ReviewStatus.Rejected),
                _ => query
            };
        }
        
        if (rating.HasValue && rating > 0)
        {
            query = query.Where(r => r.Rating == rating);
        }
        
        if (isVerifiedPurchase.HasValue)
        {
            query = query.Where(r => r.IsVerifiedPurchase == isVerifiedPurchase);
        }
        
        if (!string.IsNullOrEmpty(dateFilter))
        {
            var now = DateTime.UtcNow;
            query = dateFilter.ToLower() switch
            {
                "today" => query.Where(r => r.CreatedAt.Date == now.Date),
                "week" => query.Where(r => r.CreatedAt >= now.AddDays(-7)),
                "month" => query.Where(r => r.CreatedAt >= now.AddMonths(-1)),
                "quarter" => query.Where(r => r.CreatedAt >= now.AddMonths(-3)),
                _ => query
            };
        }
        
        if (!string.IsNullOrEmpty(searchTerm))
        {
            var searchLower = searchTerm.ToLower();
            query = query.Where(r => 
                r.Title.ToLower().Contains(searchLower) ||
                r.Comment.ToLower().Contains(searchLower) ||
                r.UserName.ToLower().Contains(searchLower));
        }
        
        return await query.CountAsync();
    }
}
