using Microsoft.JSInterop;

namespace NafaPlace.Web.Services;

public interface ISafeJSRuntimeService
{
    Task ShowToastAsync(string message, string type = "info", int duration = 3000);
    Task<T> InvokeAsync<T>(string identifier, params object[] args);
    Task InvokeVoidAsync(string identifier, params object[] args);
}

public class SafeJSRuntimeService : ISafeJSRuntimeService
{
    private readonly IJSRuntime _jsRuntime;
    private readonly ILogger<SafeJSRuntimeService> _logger;
    private readonly Dictionary<string, DateTime> _lastCalls = new();
    private readonly object _lock = new();

    public SafeJSRuntimeService(IJSRuntime jsRuntime, ILogger<SafeJSRuntimeService> logger)
    {
        _jsRuntime = jsRuntime;
        _logger = logger;
    }

    public async Task ShowToastAsync(string message, string type = "info", int duration = 3000)
    {
        try
        {
            // Éviter les appels répétitifs de toast
            var key = $"toast_{message}_{type}";
            lock (_lock)
            {
                if (_lastCalls.TryGetValue(key, out var lastCall) && 
                    DateTime.Now - lastCall < TimeSpan.FromSeconds(1))
                {
                    return; // Ignorer l'appel répétitif
                }
                _lastCalls[key] = DateTime.Now;
            }

            await _jsRuntime.InvokeVoidAsync("showToast", message, type, duration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'affichage du toast: {Message}", message);
        }
    }

    public async Task<T> InvokeAsync<T>(string identifier, params object[] args)
    {
        try
        {
            return await _jsRuntime.InvokeAsync<T>(identifier, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'appel JSRuntime: {Identifier}", identifier);
            return default(T)!;
        }
    }

    public async Task InvokeVoidAsync(string identifier, params object[] args)
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync(identifier, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'appel JSRuntime void: {Identifier}", identifier);
        }
    }
}
