using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using NafaPlace.Delivery.Application.Services;
using NafaPlace.Delivery.Application.DTOs;

namespace NafaPlace.Delivery.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DeliveryZoneController : ControllerBase
{
    private readonly IDeliveryService _deliveryService;
    private readonly ILogger<DeliveryZoneController> _logger;

    public DeliveryZoneController(IDeliveryService deliveryService, ILogger<DeliveryZoneController> logger)
    {
        _deliveryService = deliveryService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<ActionResult<List<DeliveryZoneDto>>> GetDeliveryZones([FromQuery] bool activeOnly = true)
    {
        try
        {
            var zones = await _deliveryService.GetDeliveryZonesAsync(activeOnly);
            return Ok(zones);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery zones");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<DeliveryZoneDto>> GetDeliveryZone(int id)
    {
        try
        {
            var zone = await _deliveryService.GetDeliveryZoneAsync(id);
            if (zone == null)
                return NotFound();

            return Ok(zone);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery zone {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("code/{code}")]
    public async Task<ActionResult<DeliveryZoneDto>> GetDeliveryZoneByCode(string code)
    {
        try
        {
            var zone = await _deliveryService.GetDeliveryZoneByCodeAsync(code);
            if (zone == null)
                return NotFound();

            return Ok(zone);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting delivery zone by code {Code}", code);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost]
    // [Authorize(Roles = "Admin")] // Temporairement désactivé pour les tests
    public async Task<ActionResult<DeliveryZoneDto>> CreateDeliveryZone([FromBody] CreateDeliveryZoneRequest request)
    {
        try
        {
            var zone = await _deliveryService.CreateDeliveryZoneAsync(request);
            return CreatedAtAction(nameof(GetDeliveryZone), new { id = zone.Id }, zone);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating delivery zone");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPut("{id}")]
    // [Authorize(Roles = "Admin")] // Temporairement désactivé pour les tests
    public async Task<ActionResult<DeliveryZoneDto>> UpdateDeliveryZone(int id, [FromBody] UpdateDeliveryZoneRequest request)
    {
        try
        {
            var zone = await _deliveryService.UpdateDeliveryZoneAsync(id, request);
            return Ok(zone);
        }
        catch (InvalidOperationException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating delivery zone {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpDelete("{id}")]
    // [Authorize(Roles = "Admin")] // Temporairement désactivé pour les tests
    public async Task<ActionResult> DeleteDeliveryZone(int id)
    {
        try
        {
            var result = await _deliveryService.DeleteDeliveryZoneAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting delivery zone {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{zoneId}/carriers")]
    public async Task<ActionResult<List<CarrierZoneDto>>> GetZoneCarriers(int zoneId)
    {
        try
        {
            var carrierZones = await _deliveryService.GetCarrierZonesAsync(null, zoneId);
            return Ok(carrierZones);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carriers for zone {ZoneId}", zoneId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("{zoneId}/calculate-fee")]
    public async Task<ActionResult<decimal>> CalculateDeliveryFee(int zoneId, [FromBody] decimal orderAmount)
    {
        try
        {
            var fee = await _deliveryService.CalculateDeliveryFeeAsync(zoneId, orderAmount);
            return Ok(fee);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating delivery fee for zone {ZoneId}", zoneId);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { status = "healthy", service = "delivery-zone-api", timestamp = DateTime.UtcNow });
    }
}
