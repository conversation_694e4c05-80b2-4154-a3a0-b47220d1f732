using NafaPlace.Notifications.Domain.Models;

namespace NafaPlace.Notifications.Application.DTOs;

public class NotificationStatsDto
{
    public int TotalNotifications { get; set; }
    public int UnreadNotifications { get; set; }
    public int ReadNotifications { get; set; }
    public int TodayNotifications { get; set; }
    public int WeekNotifications { get; set; }
    public int MonthNotifications { get; set; }
    
    public Dictionary<NotificationType, int> NotificationsByType { get; set; } = new();
    public Dictionary<NotificationPriority, int> NotificationsByPriority { get; set; } = new();
}
