using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Wishlist.Application.DTOs.Requests;

public class AddToWishlistRequest
{
    [Required]
    public int ProductId { get; set; }

    [Required]
    [MaxLength(100)]
    public required string ProductName { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal ProductPrice { get; set; }

    [Required]
    [MaxLength(3)]
    public required string Currency { get; set; } = "GNF";

    [MaxLength(500)]
    public string? ProductImageUrl { get; set; }

    [MaxLength(50)]
    public string? ProductBrand { get; set; }

    public int? CategoryId { get; set; }

    [MaxLength(100)]
    public string? CategoryName { get; set; }
}

public class CreateWishlistRequest
{
    [Required]
    [MaxLength(100)]
    public required string Name { get; set; }

    [MaxLength(500)]
    public string? Description { get; set; }

    public bool IsPublic { get; set; } = false;
}

public class UpdateWishlistRequest
{
    [MaxLength(100)]
    public string? Name { get; set; }

    [MaxLength(500)]
    public string? Description { get; set; }

    public bool? IsPublic { get; set; }
}
