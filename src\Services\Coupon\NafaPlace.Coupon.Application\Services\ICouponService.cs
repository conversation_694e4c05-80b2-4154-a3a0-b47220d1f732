using NafaPlace.Coupon.Application.DTOs;

namespace NafaPlace.Coupon.Application.Services;

public interface ICouponService
{
    // Coupon management
    Task<CouponDto> CreateCouponAsync(CreateCouponRequest request, string createdBy);
    Task<CouponDto> UpdateCouponAsync(int id, UpdateCouponRequest request, string updatedBy);
    Task<bool> DeleteCouponAsync(int id);
    Task<CouponDto?> GetCouponByIdAsync(int id);
    Task<CouponDto?> GetCouponByCodeAsync(string code);
    Task<List<CouponDto>> GetCouponsAsync(int page = 1, int pageSize = 20, bool? isActive = null);
    Task<List<CouponDto>> GetActiveCouponsAsync();

    // Coupon validation and application
    Task<CouponValidationResult> ValidateCouponAsync(string couponCode, CartForCouponValidation cart);
    Task<CouponApplicationResult> ApplyCouponAsync(string couponCode, CartForCouponValidation cart);
    Task<decimal> CalculateDiscountAsync(CouponDto coupon, CartForCouponValidation cart);

    // Coupon usage tracking
    Task<bool> RecordCouponUsageAsync(int couponId, string userId, string? orderId, decimal discountAmount);
    Task<int> GetUserCouponUsageCountAsync(int couponId, string userId);
    Task<List<CouponDto>> GetUserAvailableCouponsAsync(string userId, CartForCouponValidation cart);

    // Admin operations
    Task<bool> DeactivateCouponAsync(int id);
    Task<bool> ActivateCouponAsync(int id);
    Task<List<CouponDto>> GetExpiredCouponsAsync();
    Task<int> CleanupExpiredCouponsAsync();

    // Statistics
    Task<CouponStatsDto> GetCouponStatsAsync(int couponId);
    Task<List<DTOs.CouponUsageStatsDto>> GetCouponUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
}

public class CouponStatsDto
{
    public int CouponId { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int TotalUsages { get; set; }
    public decimal TotalDiscountGiven { get; set; }
    public string Currency { get; set; } = "GNF";
    public int UniqueUsers { get; set; }
    public DateTime? LastUsed { get; set; }
    public decimal AverageDiscountPerUse { get; set; }
}

public class CouponUsageStatsDto
{
    public DateTime Date { get; set; }
    public int TotalCouponsUsed { get; set; }
    public decimal TotalDiscountGiven { get; set; }
    public string Currency { get; set; } = "GNF";
    public int UniqueCoupons { get; set; }
    public int UniqueUsers { get; set; }
}
