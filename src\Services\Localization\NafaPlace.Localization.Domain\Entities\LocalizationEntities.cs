namespace NafaPlace.Localization.Domain.Entities;

public class Language
{
    public string Code { get; set; } = string.Empty; // ISO 639-1 code (fr, en, ar, etc.)
    public string Name { get; set; } = string.Empty; // English name
    public string NativeName { get; set; } = string.Empty; // Native name
    public string Direction { get; set; } = "ltr"; // ltr or rtl
    public bool IsActive { get; set; } = true;
    public bool IsDefault { get; set; } = false;
    public int Priority { get; set; } = 1;
    public string? DateFormat { get; set; }
    public string? TimeFormat { get; set; }
    public string? NumberFormat { get; set; }
    public string? CurrencyFormat { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class TranslationKey
{
    public int Id { get; set; }
    public string Key { get; set; } = string.Empty; // Unique key like "welcome_message"
    public string? Category { get; set; } // Category like "ui", "email", "error"
    public string DefaultValue { get; set; } = string.Empty; // Default text (usually in English)
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public string Tags { get; set; } = "[]"; // JSON array of tags
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class Translation
{
    public int Id { get; set; }
    public int TranslationKeyId { get; set; }
    public string LanguageCode { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty; // Translated text
    public bool IsApproved { get; set; } = false;
    public bool IsAutoTranslated { get; set; } = false;
    public string? TranslatedBy { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? ApprovedAt { get; set; }
    public decimal? Quality { get; set; } // Quality score 0-1
    public string? Context { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class Currency
{
    public string Code { get; set; } = string.Empty; // ISO 4217 code (USD, EUR, GNF, etc.)
    public string Name { get; set; } = string.Empty;
    public string Symbol { get; set; } = string.Empty;
    public int DecimalPlaces { get; set; } = 2;
    public bool IsActive { get; set; } = true;
    public bool IsDefault { get; set; } = false;
    public int Priority { get; set; } = 1;
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CurrencyRate
{
    public int Id { get; set; }
    public string FromCurrency { get; set; } = string.Empty;
    public string ToCurrency { get; set; } = string.Empty;
    public decimal Rate { get; set; }
    public DateTime Date { get; set; }
    public string? Source { get; set; } // API source like "fixer.io", "exchangerate-api"
    public bool IsActive { get; set; } = true;
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class LocalizationResource
{
    public int Id { get; set; }
    public string ResourceType { get; set; } = string.Empty; // "image", "audio", "video", "document"
    public string ResourceKey { get; set; } = string.Empty; // Unique identifier
    public string LanguageCode { get; set; } = string.Empty;
    public string ResourceValue { get; set; } = string.Empty; // URL or file path
    public string? MimeType { get; set; }
    public long FileSize { get; set; }
    public bool IsActive { get; set; } = true;
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class TranslationMemory
{
    public int Id { get; set; }
    public string SourceLanguage { get; set; } = string.Empty;
    public string TargetLanguage { get; set; } = string.Empty;
    public string SourceText { get; set; } = string.Empty;
    public string TargetText { get; set; } = string.Empty;
    public decimal? Quality { get; set; } // Quality score 0-1
    public string? Context { get; set; }
    public string? Domain { get; set; } // Domain like "ecommerce", "legal", "medical"
    public string? CreatedBy { get; set; }
    public int UsageCount { get; set; } = 0;
    public DateTime? LastUsedAt { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
