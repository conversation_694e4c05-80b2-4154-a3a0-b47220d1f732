using NafaPlace.Reviews.Application.DTOs;
using NafaPlace.Reviews.Application.Interfaces;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.DTOs;

namespace NafaPlace.Reviews.Application.Services;

public partial class ReviewService : IReviewService
{
    // Seller-specific operations
    
    public async Task<ReviewsPagedResult> GetReviewsBySellerIdAsync(int sellerId, int page = 1, int pageSize = 10)
    {
        var reviews = await _reviewRepository.GetReviewsBySellerIdAsync(sellerId, page, pageSize);
        var totalCount = await _reviewRepository.GetTotalReviewsCountBySellerIdAsync(sellerId);
        
        return new ReviewsPagedResult
        {
            Reviews = reviews.Select(MapToDto).ToList(),
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }
    
    public async Task<ReviewStatsDto> GetSellerReviewStatsAsync(int sellerId)
    {
        var totalReviews = await _reviewRepository.GetTotalReviewsCountBySellerIdAsync(sellerId);
        var averageRating = await _reviewRepository.GetAverageRatingBySellerIdAsync(sellerId);
        var ratingDistribution = await _reviewRepository.GetRatingDistributionBySellerIdAsync(sellerId);
        var recentReviews = await _reviewRepository.GetRecentReviewsCountBySellerIdAsync(sellerId, 30);
        var pendingReviews = await _reviewRepository.GetPendingReviewsCountBySellerIdAsync(sellerId);
        
        return new ReviewStatsDto
        {
            TotalReviews = totalReviews,
            AverageRating = averageRating,
            RatingDistribution = ratingDistribution,
            RecentReviews = recentReviews,
            PendingReviews = pendingReviews
        };
    }
}
