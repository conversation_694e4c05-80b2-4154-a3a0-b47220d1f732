services:
  # Redis pour le cache
  redis:
    image: redis:7-alpine
    container_name: nafaplace-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - nafaplace-network

  # Base de données PostgreSQL
  postgres:
    image: postgres:15
    container_name: nafaplace-postgres
    environment:
      - POSTGRES_DB=nafaplace
      - POSTGRES_USER=nafaplace
      - POSTGRES_PASSWORD=nafaplace123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - nafaplace-network

  # Service Identity
  identity-api:
    build:
      context: .
      dockerfile: src/Services/Identity/Dockerfile
    container_name: nafaplace-identity-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_identity;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5001:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Catalog
  catalog-api:
    build:
      context: .
      dockerfile: src/Services/Catalog/Dockerfile
    container_name: nafaplace-catalog-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=host.docker.internal;Database=NafaPlace.Catalog;Username=postgres;Password=NafaPlace2025@Dev
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5002:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Cart
  cart-api:
    build:
      context: .
      dockerfile: src/Services/Cart/NafaPlace.Cart.API/Dockerfile
    container_name: nafaplace-cart-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_cart;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5003:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Order
  order-api:
    build:
      context: .
      dockerfile: src/Services/Order/NafaPlace.Order.API/Dockerfile
    container_name: nafaplace-order-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_order;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5004:80"
    depends_on:
      - postgres
      - redis
      - catalog-api
    networks:
      - nafaplace-network

  # Service Payment
  payment-api:
    build:
      context: .
      dockerfile: src/Services/Payment/NafaPlace.Payment.API/Dockerfile
    container_name: nafaplace-payment-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_payment;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5005:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Chat
  chat-api:
    build:
      context: .
      dockerfile: src/Services/Chat/NafaPlace.Chat.API/Dockerfile
    container_name: nafaplace-chat-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_chat;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5007:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Reviews
  reviews-api:
    build:
      context: .
      dockerfile: src/Services/Reviews/NafaPlace.Reviews.API/Dockerfile
    container_name: nafaplace-reviews-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_reviews;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5006:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Notifications
  notifications-api:
    build:
      context: .
      dockerfile: src/Services/Notifications/NafaPlace.Notifications.API/Dockerfile
    container_name: nafaplace-notifications-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_notifications;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5008:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

  # Service Wishlist
  wishlist-api:
    build:
      context: .
      dockerfile: src/Services/Wishlist/NafaPlace.Wishlist.API/Dockerfile
    container_name: nafaplace-wishlist-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=nafaplace_wishlist;Username=nafaplace;Password=nafaplace123
      - ConnectionStrings__Redis=redis:6379
      - JwtSettings__SecretKey=your-super-secret-key-here-make-it-long-and-complex
      - JwtSettings__Issuer=NafaPlace
      - JwtSettings__Audience=NafaPlace
    ports:
      - "5009:80"
    depends_on:
      - postgres
      - redis
    networks:
      - nafaplace-network

volumes:
  postgres_data:
  redis_data:

networks:
  nafaplace-network:
    driver: bridge
