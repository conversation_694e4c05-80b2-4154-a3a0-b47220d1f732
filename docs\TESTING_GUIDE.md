# 🧪 Guide de Test Complet - NafaPlace

Ce guide détaille comment tester toutes les fonctionnalités de NafaPlace après l'implémentation des nouvelles fonctionnalités avancées.

## 📋 Vue d'ensemble des Tests

### ✅ Services à Tester
1. **🔍 Recherche Avancée** - Service Search
2. **📊 Analytics & KPIs** - Service Analytics  
3. **💬 Chat Support** - Service Chat
4. **🤖 Recommandations IA** - Service Recommendation
5. **🎁 Programme Fidélité** - Service Loyalty
6. **🌍 Multi-langues** - Service Localization
7. **🔔 Notifications** - Service Notification
8. **📦 Inventory** - Service Inventory

### 🎯 Types de Tests
- **Tests de Santé** - Vérification que les services démarrent
- **Tests d'API** - Vérification des endpoints REST
- **Tests d'Intégration** - Communication entre services
- **Tests Fonctionnels** - Scénarios utilisateur complets
- **Tests de Performance** - Charge et temps de réponse

## 🚀 Démarrage Rapide

### 1. Prérequis
```bash
# Outils requis
- Docker & Docker Compose
- PowerShell 5.1+
- .NET 8 SDK
- PostgreSQL Client (optionnel)
```

### 2. Démarrage Complet
```powershell
# Démarrer tous les services et exécuter les tests
.\scripts\start-and-test.ps1

# Ou démarrage par étapes
.\scripts\start-and-test.ps1 -SkipBuild    # Sans reconstruction
.\scripts\start-and-test.ps1 -SkipTests    # Sans tests automatiques
```

### 3. Test Rapide
```powershell
# Test rapide de santé des services
.\scripts\quick-test.ps1
```

## 🔧 Tests Détaillés par Service

### 🔍 1. Service de Recherche Avancée

#### Endpoints à Tester
```http
GET  /api/search/health
POST /api/search
GET  /api/search/autocomplete?query=smart
POST /api/search/image
GET  /api/search/suggestions
```

#### Tests Manuels
1. **Recherche Textuelle**
   - Aller sur http://localhost:8080
   - Utiliser la barre de recherche
   - Tester : "smartphone", "vêtements", "électronique"
   - Vérifier les résultats et filtres

2. **Autocomplétion**
   - Taper quelques lettres dans la recherche
   - Vérifier que les suggestions apparaissent
   - Cliquer sur une suggestion

3. **Filtres Avancés**
   - Utiliser les filtres de prix, catégorie, marque
   - Vérifier que les résultats se mettent à jour

#### Tests API
```powershell
# Test de recherche
$searchData = @{
    query = "smartphone"
    filters = @{
        category = "Electronics"
        minPrice = 100
        maxPrice = 1000
    }
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:5011/api/search" -Method Post -Body $searchData -ContentType "application/json"
```

### 📊 2. Service Analytics

#### Endpoints à Tester
```http
GET /api/analytics/health
GET /api/analytics/kpis
GET /api/analytics/sales/metrics
POST /api/analytics/events
GET /api/analytics/dashboard
```

#### Tests Manuels
1. **Tableau de Bord Admin**
   - Aller sur http://localhost:8081
   - Se connecter en tant qu'admin
   - Vérifier les KPIs sur le dashboard
   - Vérifier les graphiques de ventes

2. **Métriques Vendeur**
   - Aller sur http://localhost:8082
   - Se connecter en tant que vendeur
   - Vérifier les métriques personnelles
   - Vérifier les rapports de produits

#### Tests API
```powershell
# Test KPIs
Invoke-RestMethod -Uri "http://localhost:5006/api/analytics/kpis" -Method Get

# Test événement
$eventData = @{
    eventType = "product_view"
    userId = "test-user"
    entityType = "product"
    entityId = "1"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:5006/api/analytics/events" -Method Post -Body $eventData -ContentType "application/json"
```

### 💬 3. Service Chat Support

#### Endpoints à Tester
```http
GET /api/chat/health
POST /api/conversations
POST /api/messages
GET /api/faq
POST /api/tickets
```

#### Tests Manuels
1. **Chat en Direct**
   - Aller sur http://localhost:8080
   - Cliquer sur l'icône de chat
   - Démarrer une conversation
   - Envoyer des messages
   - Vérifier les réponses automatiques

2. **Système de Tickets**
   - Créer un ticket de support
   - Vérifier la notification
   - Répondre au ticket

3. **FAQ Automatique**
   - Poser des questions courantes
   - Vérifier les réponses automatiques
   - Tester le chatbot IA

#### Tests SignalR
```javascript
// Test de connexion SignalR (dans la console du navigateur)
const connection = new signalR.HubConnectionBuilder()
    .withUrl("http://localhost:5007/chathub")
    .build();

connection.start().then(() => {
    console.log("Connecté au chat hub");
    connection.invoke("JoinConversation", "test-conversation-id");
});
```

### 🤖 4. Service Recommandations

#### Endpoints à Tester
```http
GET /api/recommendations/health
POST /api/recommendations/personalized
GET /api/recommendations/similar/{productId}
POST /api/interactions
GET /api/recommendations/trending
```

#### Tests Manuels
1. **Recommandations Personnalisées**
   - Se connecter sur le site
   - Naviguer sur plusieurs produits
   - Vérifier les recommandations sur la page d'accueil
   - Vérifier les "Produits similaires"

2. **Recommandations Basées sur l'Historique**
   - Ajouter des produits au panier
   - Effectuer des achats
   - Vérifier que les recommandations s'adaptent

#### Tests API
```powershell
# Test recommandations personnalisées
$recoData = @{
    userId = "test-user"
    type = 1
    limit = 5
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:5008/api/recommendations/personalized" -Method Post -Body $recoData -ContentType "application/json"

# Test produits similaires
Invoke-RestMethod -Uri "http://localhost:5008/api/recommendations/similar/1" -Method Get
```

### 🎁 5. Service Fidélité

#### Endpoints à Tester
```http
GET /api/loyalty/health
GET /api/loyalty/account/{userId}
POST /api/loyalty/points/earn
GET /api/rewards/eligible/{userId}
POST /api/rewards/redeem
GET /api/badges/user/{userId}
```

#### Tests Manuels
1. **Compte de Fidélité**
   - Se connecter sur le site
   - Vérifier les points dans le profil
   - Effectuer un achat
   - Vérifier l'attribution de points

2. **Récompenses**
   - Aller dans la section récompenses
   - Racheter une récompense
   - Utiliser un code de réduction

3. **Badges et Défis**
   - Vérifier les badges obtenus
   - Participer à un défi
   - Vérifier les progrès

#### Tests API
```powershell
# Test compte fidélité
Invoke-RestMethod -Uri "http://localhost:5009/api/loyalty/account/test-user" -Method Get

# Test attribution de points
$pointsData = @{
    userId = "test-user"
    points = 100
    description = "Test d'attribution"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:5009/api/loyalty/points/earn" -Method Post -Body $pointsData -ContentType "application/json"
```

### 🌍 6. Service Localisation

#### Endpoints à Tester
```http
GET /api/localization/health
GET /api/languages
GET /api/translations/{key}/{language}
POST /api/translate/auto
GET /api/format/currency
```

#### Tests Manuels
1. **Changement de Langue**
   - Changer la langue du site (FR/EN/AR)
   - Vérifier que l'interface se traduit
   - Vérifier le formatage des prix

2. **Traduction Automatique**
   - Ajouter du nouveau contenu
   - Vérifier la traduction automatique
   - Tester les devises

#### Tests API
```powershell
# Test langues supportées
Invoke-RestMethod -Uri "http://localhost:5010/api/languages" -Method Get

# Test traduction automatique
$translateData = @{
    text = "Bonjour le monde"
    sourceLanguage = "fr"
    targetLanguage = "en"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:5010/api/translate/auto" -Method Post -Body $translateData -ContentType "application/json"
```

## 🔄 Tests d'Intégration

### Scénario Complet E-Commerce
1. **Inscription/Connexion**
   - Créer un compte → Vérifier création compte fidélité
   - Se connecter → Vérifier analytics de connexion

2. **Navigation et Recherche**
   - Rechercher des produits → Vérifier analytics de recherche
   - Voir des produits → Vérifier recommandations

3. **Achat**
   - Ajouter au panier → Vérifier analytics
   - Passer commande → Vérifier attribution points fidélité
   - Recevoir notifications → Vérifier emails/SMS

4. **Support**
   - Contacter le support → Vérifier chat
   - Créer un ticket → Vérifier workflow

### Tests de Performance
```powershell
# Test de charge simple
for ($i = 1; $i -le 100; $i++) {
    Invoke-RestMethod -Uri "http://localhost:5000/api/products" -Method Get
    Write-Progress -Activity "Test de charge" -Status "Requête $i/100" -PercentComplete ($i)
}
```

## 📊 Métriques de Succès

### Critères de Validation
- ✅ **Santé des Services** : 100% des services démarrent
- ✅ **APIs Fonctionnelles** : 95%+ des endpoints répondent
- ✅ **Intégrations** : Tous les workflows fonctionnent
- ✅ **Performance** : Temps de réponse < 2s
- ✅ **UI/UX** : Toutes les fonctionnalités accessibles

### Rapports de Test
Les scripts génèrent automatiquement des rapports dans :
- `logs/test-results.json` - Résultats détaillés
- `logs/performance-metrics.csv` - Métriques de performance
- `logs/error-summary.txt` - Résumé des erreurs

## 🚨 Dépannage

### Problèmes Courants
1. **Services ne démarrent pas**
   ```bash
   docker-compose logs [service-name]
   docker system prune -f
   ```

2. **Base de données inaccessible**
   ```bash
   docker exec -it nafaplace-postgres psql -U nafaplace
   ```

3. **Migrations échouent**
   ```powershell
   .\scripts\run-migrations.ps1 -Force
   ```

### Logs et Monitoring
- **Logs Docker** : `docker-compose logs -f`
- **Logs Application** : Dans les conteneurs `/app/logs/`
- **Métriques** : Disponibles via les endpoints `/health`

## 📞 Support

Pour toute question ou problème :
1. Vérifier les logs d'erreur
2. Consulter la documentation technique
3. Exécuter les tests de diagnostic
4. Contacter l'équipe de développement

---

**🎉 Bonne chance avec vos tests ! NafaPlace est maintenant une plateforme e-commerce complète et moderne !**
