using NafaPlace.Delivery.Application.DTOs;
using NafaPlace.Delivery.Domain.Models;

namespace NafaPlace.Delivery.Application.Services;

public interface IDeliveryService
{
    // Zone Management
    Task<List<DeliveryZoneDto>> GetDeliveryZonesAsync(bool activeOnly = true);
    Task<DeliveryZoneDto?> GetDeliveryZoneAsync(int id);
    Task<DeliveryZoneDto?> GetDeliveryZoneByCodeAsync(string code);
    Task<DeliveryZoneDto> CreateDeliveryZoneAsync(CreateDeliveryZoneRequest request);
    Task<DeliveryZoneDto> UpdateDeliveryZoneAsync(int id, UpdateDeliveryZoneRequest request);
    Task<bool> DeleteDeliveryZoneAsync(int id);

    // Carrier Management
    Task<List<CarrierDto>> GetCarriersAsync(bool activeOnly = true);
    Task<CarrierDto?> GetCarrierAsync(int id);
    Task<CarrierDto?> GetCarrierByCodeAsync(string code);
    Task<CarrierDto> CreateCarrierAsync(CreateCarrierRequest request);
    Task<CarrierDto> UpdateCarrierAsync(int id, UpdateCarrierRequest request);
    Task<bool> DeleteCarrierAsync(int id);

    // Carrier Zone Management
    Task<List<CarrierZoneDto>> GetCarrierZonesAsync(int? carrierId = null, int? zoneId = null);
    Task<CarrierZoneDto> CreateCarrierZoneAsync(CreateCarrierZoneRequest request);
    Task<CarrierZoneDto> UpdateCarrierZoneAsync(int id, UpdateCarrierZoneRequest request);
    Task<bool> DeleteCarrierZoneAsync(int id);

    // Delivery Quote and Calculation
    Task<List<DeliveryQuoteDto>> GetDeliveryQuotesAsync(DeliveryQuoteRequest request);
    Task<DeliveryQuoteDto?> GetBestDeliveryQuoteAsync(DeliveryQuoteRequest request);
    Task<decimal> CalculateDeliveryFeeAsync(int zoneId, decimal orderAmount);
    Task<decimal> CalculateDeliveryFeeAsync(string address, decimal orderValue, decimal? weight = null, DeliveryType type = DeliveryType.Standard);
    Task<decimal> CalculateAdvancedDeliveryFeeAsync(int zoneId, decimal orderAmount, decimal? weight = null, decimal? volume = null, DeliveryType deliveryType = DeliveryType.Standard);
    Task<List<DeliveryQuoteDto>> GetDeliveryQuotesAsync(string address, decimal orderAmount, decimal? weight = null, decimal? volume = null, double? latitude = null, double? longitude = null);
    Task<DeliveryZoneDto?> FindDeliveryZoneAsync(string address, double? latitude = null, double? longitude = null);

    // Delivery Order Management
    Task<DeliveryOrderDto> CreateDeliveryOrderAsync(CreateDeliveryOrderRequest request);
    Task<DeliveryOrderDto?> GetDeliveryOrderAsync(int id);
    Task<DeliveryOrderDto?> GetDeliveryOrderByTrackingNumberAsync(string trackingNumber);
    Task<List<DeliveryOrderDto>> GetCustomerDeliveryOrdersAsync(string customerId, int page = 1, int pageSize = 20);
    Task<List<DeliveryOrderDto>> GetCarrierDeliveryOrdersAsync(int carrierId, int page = 1, int pageSize = 20);
    Task<bool> CancelDeliveryOrderAsync(int id, string reason);

    // Delivery Tracking
    Task<bool> UpdateDeliveryStatusAsync(int deliveryOrderId, UpdateDeliveryStatusRequest request);
    Task<List<DeliveryTrackingDto>> GetDeliveryTrackingAsync(int deliveryOrderId);
    Task<List<DeliveryTrackingDto>> GetDeliveryTrackingByTrackingNumberAsync(string trackingNumber);
    Task<bool> AddTrackingEventAsync(int deliveryOrderId, UpdateDeliveryStatusRequest request);

    // Route Management
    Task<DeliveryRouteDto> CreateDeliveryRouteAsync(CreateDeliveryRouteRequest request);
    Task<List<DeliveryRouteDto>> GetDeliveryRoutesAsync(int? carrierId = null, DateTime? date = null);
    Task<bool> OptimizeDeliveryRouteAsync(int routeId);
    Task<bool> AssignDeliveryToRouteAsync(int deliveryOrderId, int routeId, int sequence);

    // Analytics and Reporting
    Task<DeliveryStatsDto> GetDeliveryStatsAsync(DateTime? startDate = null, DateTime? endDate = null, int? carrierId = null);
    Task<List<CarrierPerformanceDto>> GetCarrierPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<ZonePerformanceDto>> GetZonePerformanceAsync(DateTime? startDate = null, DateTime? endDate = null);

    // Customer Services
    Task<bool> RateDeliveryAsync(int deliveryOrderId, int rating, string? feedback = null);
    Task<bool> RequestDeliveryRescheduleAsync(int deliveryOrderId, DateTime newDate, string? reason = null);
    Task<List<DeliveryOrderDto>> GetDeliveriesNearLocationAsync(double latitude, double longitude, double radiusKm = 5);

    // Notifications and Alerts
    Task<bool> SendDeliveryNotificationAsync(int deliveryOrderId, string message, NotificationChannel channel = NotificationChannel.Email);
    Task<List<DeliveryOrderDto>> GetDelayedDeliveriesAsync();
    Task<List<DeliveryOrderDto>> GetFailedDeliveriesAsync();

    // Maintenance and Utilities
    Task<bool> RecalculateDeliveryFeesAsync();
    Task<int> CleanupOldTrackingEventsAsync(int daysToKeep = 90);
    Task<bool> SyncCarrierTrackingAsync(int carrierId);
}
