# Script de création des bases de données et migrations NafaPlace
# Auteur: Assistant IA
# Date: 2025-01-28

param(
    [switch]$Force,
    [switch]$SkipMigrations,
    [switch]$OnlyCreate
)

Write-Host "🗄️ Création des bases de données NafaPlace" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Configuration des services
$services = @{
    "Analytics" = @{
        "ProjectPath" = "src/Services/Analytics/NafaPlace.Analytics.Infrastructure"
        "Database" = "nafaplace_analytics"
        "Context" = "AnalyticsDbContext"
        "HasMigrations" = $true
    }
    "Chat" = @{
        "ProjectPath" = "src/Services/Chat/NafaPlace.Chat.Infrastructure"
        "Database" = "nafaplace_chat"
        "Context" = "ChatDbContext"
        "HasMigrations" = $true
    }
    "Recommendation" = @{
        "ProjectPath" = "src/Services/Recommendation/NafaPlace.Recommendation.Infrastructure"
        "Database" = "nafaplace_recommendation"
        "Context" = "RecommendationDbContext"
        "HasMigrations" = $true
    }
    "Loyalty" = @{
        "ProjectPath" = "src/Services/Loyalty/NafaPlace.Loyalty.Infrastructure"
        "Database" = "nafaplace_loyalty"
        "Context" = "LoyaltyDbContext"
        "HasMigrations" = $true
    }
    "Localization" = @{
        "ProjectPath" = "src/Services/Localization/NafaPlace.Localization.Infrastructure"
        "Database" = "nafaplace_localization"
        "Context" = "LocalizationDbContext"
        "HasMigrations" = $true
    }
}

# Configuration PostgreSQL
$pgHost = "localhost"
$pgPort = "5432"
$pgUser = "nafaplace"
$pgPassword = "nafaplace123"
$pgDatabase = "postgres"

# Fonction pour vérifier si PostgreSQL est accessible
function Test-PostgreSQLConnection {
    Write-Host "🔍 Vérification de la connexion PostgreSQL..." -ForegroundColor Yellow
    
    try {
        # Test avec docker si le conteneur existe
        $containerExists = docker ps --filter "name=nafaplace-postgres" --format "{{.Names}}" | Select-String "nafaplace-postgres"
        
        if ($containerExists) {
            Write-Host "✅ Conteneur PostgreSQL trouvé" -ForegroundColor Green
            $result = docker exec nafaplace-postgres pg_isready -U $pgUser
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ PostgreSQL est accessible" -ForegroundColor Green
                return $true
            }
        } else {
            Write-Host "⚠️ Conteneur PostgreSQL non trouvé, tentative de démarrage..." -ForegroundColor Yellow
            
            # Essayer de démarrer PostgreSQL avec Docker
            docker run -d --name nafaplace-postgres `
                -e POSTGRES_DB=nafaplace `
                -e POSTGRES_USER=$pgUser `
                -e POSTGRES_PASSWORD=$pgPassword `
                -p "${pgPort}:5432" `
                postgres:15
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ PostgreSQL démarré avec Docker" -ForegroundColor Green
                Start-Sleep -Seconds 10
                return $true
            }
        }
        
        Write-Host "❌ PostgreSQL n'est pas accessible" -ForegroundColor Red
        return $false
    }
    catch {
        Write-Host "❌ Erreur lors de la vérification PostgreSQL : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour créer une base de données
function New-Database {
    param($databaseName)
    
    Write-Host "📊 Création de la base de données : $databaseName" -ForegroundColor Cyan
    
    try {
        # Vérifier si la base existe déjà
        $checkDb = docker exec nafaplace-postgres psql -U $pgUser -d $pgDatabase -t -c "SELECT 1 FROM pg_database WHERE datname='$databaseName';" 2>$null
        
        if ($checkDb -and $checkDb.Trim() -eq "1") {
            if ($Force) {
                Write-Host "🗑️ Suppression de la base existante : $databaseName" -ForegroundColor Yellow
                docker exec nafaplace-postgres psql -U $pgUser -d $pgDatabase -c "DROP DATABASE IF EXISTS $databaseName;" | Out-Null
            } else {
                Write-Host "ℹ️ Base de données $databaseName existe déjà" -ForegroundColor Blue
                return $true
            }
        }
        
        # Créer la base de données
        $createResult = docker exec nafaplace-postgres psql -U $pgUser -d $pgDatabase -c "CREATE DATABASE $databaseName;"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Base de données $databaseName créée avec succès" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Erreur lors de la création de $databaseName" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Exception lors de la création de $databaseName : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour installer les outils EF Core
function Install-EFTools {
    Write-Host "🔧 Installation des outils Entity Framework Core..." -ForegroundColor Yellow
    
    try {
        # Vérifier si dotnet-ef est installé
        $efVersion = dotnet tool list -g | Select-String "dotnet-ef"
        
        if (-not $efVersion) {
            Write-Host "📦 Installation de dotnet-ef..." -ForegroundColor Cyan
            dotnet tool install --global dotnet-ef --version 8.0.0
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ dotnet-ef installé avec succès" -ForegroundColor Green
            } else {
                Write-Host "❌ Erreur lors de l'installation de dotnet-ef" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "✅ dotnet-ef déjà installé" -ForegroundColor Green
        }
        
        return $true
    }
    catch {
        Write-Host "❌ Erreur lors de l'installation des outils EF : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour créer les migrations
function New-ServiceMigrations {
    param($serviceName, $serviceConfig)
    
    if ($SkipMigrations) {
        Write-Host "⏭️ Migrations ignorées pour $serviceName" -ForegroundColor Yellow
        return $true
    }
    
    Write-Host "🔧 Création des migrations pour : $serviceName" -ForegroundColor Magenta
    
    $projectPath = $serviceConfig.ProjectPath
    $contextName = $serviceConfig.Context
    
    if (-not (Test-Path $projectPath)) {
        Write-Host "❌ Projet non trouvé : $projectPath" -ForegroundColor Red
        return $false
    }
    
    try {
        Push-Location $projectPath
        
        # Vérifier s'il y a déjà des migrations
        $migrationsPath = Join-Path $projectPath "Migrations"
        $hasMigrations = Test-Path $migrationsPath
        
        if ($hasMigrations -and -not $Force) {
            Write-Host "ℹ️ Migrations déjà présentes pour $serviceName" -ForegroundColor Blue
            Pop-Location
            return $true
        }
        
        if ($Force -and $hasMigrations) {
            Write-Host "🗑️ Suppression des migrations existantes..." -ForegroundColor Yellow
            Remove-Item $migrationsPath -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        # Créer la migration initiale
        $migrationName = "Initial$($serviceName)Create"
        Write-Host "📝 Création de la migration : $migrationName" -ForegroundColor Cyan
        
        dotnet ef migrations add $migrationName --context $contextName --verbose
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Migration créée pour $serviceName" -ForegroundColor Green
            Pop-Location
            return $true
        } else {
            Write-Host "❌ Erreur lors de la création de la migration pour $serviceName" -ForegroundColor Red
            Pop-Location
            return $false
        }
    }
    catch {
        Write-Host "❌ Exception lors de la création des migrations pour $serviceName : $($_.Exception.Message)" -ForegroundColor Red
        Pop-Location
        return $false
    }
}

# Fonction pour appliquer les migrations
function Update-ServiceDatabase {
    param($serviceName, $serviceConfig)
    
    Write-Host "🚀 Application des migrations pour : $serviceName" -ForegroundColor Magenta
    
    $projectPath = $serviceConfig.ProjectPath
    $databaseName = $serviceConfig.Database
    $contextName = $serviceConfig.Context
    
    try {
        Push-Location $projectPath
        
        # Construire la chaîne de connexion
        $connectionString = "Host=$pgHost;Port=$pgPort;Database=$databaseName;Username=$pgUser;Password=$pgPassword"
        
        # Appliquer les migrations
        Write-Host "📊 Application des migrations à la base $databaseName..." -ForegroundColor Cyan
        
        dotnet ef database update --context $contextName --connection $connectionString --verbose
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Migrations appliquées avec succès pour $serviceName" -ForegroundColor Green
            Pop-Location
            return $true
        } else {
            Write-Host "❌ Erreur lors de l'application des migrations pour $serviceName" -ForegroundColor Red
            Pop-Location
            return $false
        }
    }
    catch {
        Write-Host "❌ Exception lors de l'application des migrations pour $serviceName : $($_.Exception.Message)" -ForegroundColor Red
        Pop-Location
        return $false
    }
}

# Fonction pour créer toutes les bases de données
function New-AllDatabases {
    Write-Host "🚀 Création de toutes les bases de données..." -ForegroundColor Green
    
    $successCount = 0
    $totalCount = $services.Count
    
    # Vérifier la connexion PostgreSQL
    if (-not (Test-PostgreSQLConnection)) {
        Write-Host "❌ Impossible de se connecter à PostgreSQL" -ForegroundColor Red
        return
    }
    
    # Installer les outils EF Core
    if (-not (Install-EFTools)) {
        Write-Host "❌ Impossible d'installer les outils EF Core" -ForegroundColor Red
        return
    }
    
    foreach ($service in $services.GetEnumerator()) {
        $serviceName = $service.Key
        $serviceConfig = $service.Value
        $databaseName = $serviceConfig.Database
        
        Write-Host "`n" + "="*50 -ForegroundColor Gray
        Write-Host "🔧 Traitement du service : $serviceName" -ForegroundColor Magenta
        Write-Host "="*50 -ForegroundColor Gray
        
        # Créer la base de données
        $dbCreated = New-Database $databaseName
        if (-not $dbCreated) {
            Write-Host "❌ Échec de la création de la base pour $serviceName" -ForegroundColor Red
            continue
        }
        
        if ($OnlyCreate) {
            Write-Host "✅ Base de données $databaseName créée (migrations ignorées)" -ForegroundColor Green
            $successCount++
            continue
        }
        
        # Créer les migrations
        $migrationsCreated = New-ServiceMigrations $serviceName $serviceConfig
        if (-not $migrationsCreated) {
            Write-Host "❌ Échec de la création des migrations pour $serviceName" -ForegroundColor Red
            continue
        }
        
        # Appliquer les migrations
        $migrationsApplied = Update-ServiceDatabase $serviceName $serviceConfig
        if ($migrationsApplied) {
            $successCount++
            Write-Host "✅ Service $serviceName configuré avec succès" -ForegroundColor Green
        } else {
            Write-Host "❌ Échec de l'application des migrations pour $serviceName" -ForegroundColor Red
        }
        
        # Pause entre les services
        Start-Sleep -Seconds 2
    }
    
    # Résumé final
    Write-Host "`n" + "="*60 -ForegroundColor Magenta
    Write-Host "📊 RÉSUMÉ DE LA CRÉATION DES BASES DE DONNÉES" -ForegroundColor Magenta
    Write-Host "="*60 -ForegroundColor Magenta
    
    $successRate = [math]::Round(($successCount / $totalCount) * 100, 2)
    Write-Host "✅ Services configurés : $successCount/$totalCount ($successRate%)" -ForegroundColor Green
    
    if ($successRate -eq 100) {
        Write-Host "🎉 Toutes les bases de données ont été créées avec succès !" -ForegroundColor Green
        Write-Host "🚀 NafaPlace est prêt à être démarré !" -ForegroundColor Green
    } elseif ($successRate -ge 80) {
        Write-Host "⚠️ La plupart des bases ont été créées ($successRate%)" -ForegroundColor Yellow
        Write-Host "🔍 Vérifiez les erreurs ci-dessus pour les services en échec" -ForegroundColor Yellow
    } else {
        Write-Host "🚨 Plusieurs bases de données ont échoué ($successRate%)" -ForegroundColor Red
        Write-Host "🔧 Vérifiez la configuration PostgreSQL et les projets" -ForegroundColor Red
    }
    
    # Afficher les bases créées
    Write-Host "`n📋 Bases de données créées :" -ForegroundColor Cyan
    foreach ($service in $services.GetEnumerator()) {
        Write-Host "   • $($service.Value.Database)" -ForegroundColor White
    }
    
    Write-Host "`n🔗 Chaîne de connexion type :" -ForegroundColor Cyan
    Write-Host "   Host=$pgHost;Port=$pgPort;Database=[nom_base];Username=$pgUser;Password=$pgPassword" -ForegroundColor White
}

# Fonction pour afficher l'aide
function Show-DatabaseHelp {
    Write-Host "📖 Aide - Création des bases de données" -ForegroundColor Cyan
    Write-Host "=======================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Ce script crée toutes les bases de données et migrations pour NafaPlace" -ForegroundColor White
    Write-Host ""
    Write-Host "Syntaxe:" -ForegroundColor Yellow
    Write-Host "  .\create-databases.ps1 [options]" -ForegroundColor White
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Force           Recréer les bases et migrations existantes" -ForegroundColor White
    Write-Host "  -SkipMigrations  Créer seulement les bases, pas les migrations" -ForegroundColor White
    Write-Host "  -OnlyCreate      Créer seulement les bases, ne pas appliquer les migrations" -ForegroundColor White
    Write-Host ""
    Write-Host "Exemples:" -ForegroundColor Yellow
    Write-Host "  .\create-databases.ps1                    # Création standard" -ForegroundColor White
    Write-Host "  .\create-databases.ps1 -Force             # Recréer tout" -ForegroundColor White
    Write-Host "  .\create-databases.ps1 -OnlyCreate        # Bases seulement" -ForegroundColor White
    Write-Host ""
    Write-Host "Prérequis:" -ForegroundColor Yellow
    Write-Host "  • Docker avec PostgreSQL ou PostgreSQL local" -ForegroundColor White
    Write-Host "  • .NET 8 SDK" -ForegroundColor White
    Write-Host "  • Projets NafaPlace compilables" -ForegroundColor White
}

# Vérifier les paramètres d'aide
if ($args -contains "-help" -or $args -contains "--help" -or $args -contains "/?") {
    Show-DatabaseHelp
    return
}

# Exécuter la création des bases de données
New-AllDatabases

Write-Host "`n🏁 Script de création des bases de données terminé !" -ForegroundColor Green
