using Microsoft.EntityFrameworkCore;
using NafaPlace.Delivery.Application.Services;
using NafaPlace.Delivery.Application.DTOs;
using NafaPlace.Delivery.Domain.Models;
using NafaPlace.Delivery.Infrastructure.Data;

namespace NafaPlace.Delivery.Infrastructure.Repositories;

public class DeliveryRepository : IDeliveryRepository
{
    private readonly DeliveryDbContext _context;

    public DeliveryRepository(DeliveryDbContext context)
    {
        _context = context;
    }

    public async Task<List<DeliveryZoneDto>> GetDeliveryZonesAsync()
    {
        return await _context.DeliveryZones
            .Where(z => z.IsActive)
            .Select(z => new DeliveryZoneDto
            {
                Id = z.Id,
                Name = z.Name,
                Code = z.Code,
                Description = z.Description,
                Type = z.Type,
                ParentZoneCode = z.ParentZoneCode,
                Latitude = z.Latitude,
                Longitude = z.Longitude,
                Radius = z.Radius,
                BaseDeliveryFee = z.BaseDeliveryFee,
                FreeDeliveryThreshold = z.FreeDeliveryThreshold,
                EstimatedDeliveryDays = z.EstimatedDeliveryDays,
                MaxDeliveryDays = z.MaxDeliveryDays,
                SameDayDeliveryAvailable = z.SameDayDeliveryAvailable,
                SameDayDeliveryFee = z.SameDayDeliveryFee,
                ExpressDeliveryAvailable = z.ExpressDeliveryAvailable,
                ExpressDeliveryFee = z.ExpressDeliveryFee,
                Currency = z.Currency,
                IsActive = z.IsActive
            })
            .ToListAsync();
    }

    public async Task<DeliveryZoneDto?> GetDeliveryZoneByIdAsync(int zoneId)
    {
        var zone = await _context.DeliveryZones
            .Where(z => z.Id == zoneId && z.IsActive)
            .FirstOrDefaultAsync();

        if (zone == null) return null;

        return new DeliveryZoneDto
        {
            Id = zone.Id,
            Name = zone.Name,
            Code = zone.Code,
            Description = zone.Description,
            Type = zone.Type,
            ParentZoneCode = zone.ParentZoneCode,
            Latitude = zone.Latitude,
            Longitude = zone.Longitude,
            Radius = zone.Radius,
            BaseDeliveryFee = zone.BaseDeliveryFee,
            FreeDeliveryThreshold = zone.FreeDeliveryThreshold,
            EstimatedDeliveryDays = zone.EstimatedDeliveryDays,
            MaxDeliveryDays = zone.MaxDeliveryDays,
            SameDayDeliveryAvailable = zone.SameDayDeliveryAvailable,
            SameDayDeliveryFee = zone.SameDayDeliveryFee,
            ExpressDeliveryAvailable = zone.ExpressDeliveryAvailable,
            ExpressDeliveryFee = zone.ExpressDeliveryFee,
            Currency = zone.Currency,
            IsActive = zone.IsActive
        };
    }

    public async Task<DeliveryOrderDto> CreateDeliveryOrderAsync(CreateDeliveryOrderRequest request)
    {
        var deliveryOrder = new DeliveryOrder
        {
            OrderId = request.OrderId,
            TrackingNumber = GenerateTrackingNumber(),
            CustomerId = request.CustomerId,
            CustomerName = request.CustomerName,
            CustomerEmail = request.CustomerEmail,
            CustomerPhone = request.CustomerPhone,
            DeliveryAddress = request.DeliveryAddress,
            DeliveryCity = request.DeliveryCity,
            DeliveryRegion = request.DeliveryRegion,
            ZoneId = 1, // Default zone
            CarrierId = request.PreferredCarrierId ?? 1, // Default carrier
            DeliveryFee = 25000, // Default fee
            OrderValue = request.OrderValue,
            TotalFee = 25000 + request.OrderValue,
            Currency = "GNF",
            Weight = request.Weight,
            PackageCount = request.PackageCount,
            PackageDescription = request.PackageDescription,
            Type = request.Type,
            Status = DeliveryStatus.Pending,
            ScheduledDeliveryDate = request.ScheduledDeliveryDate,
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(2),
            DeliveryInstructions = request.DeliveryInstructions
        };

        _context.DeliveryOrders.Add(deliveryOrder);
        await _context.SaveChangesAsync();

        // Add initial tracking event
        var tracking = new DeliveryTracking
        {
            DeliveryOrderId = deliveryOrder.Id,
            Status = DeliveryStatus.Pending,
            Description = "Commande reçue et en cours de préparation",
            Location = "Centre de tri",
            Notes = "Commande reçue et en cours de préparation",
            EventDate = DateTime.UtcNow
        };

        _context.DeliveryTrackings.Add(tracking);
        await _context.SaveChangesAsync();

        return await GetDeliveryOrderByIdAsync(deliveryOrder.Id) ?? throw new InvalidOperationException("Failed to create delivery order");
    }

    public async Task<DeliveryOrderDto?> GetDeliveryOrderByIdAsync(int id)
    {
        var order = await _context.DeliveryOrders
            .Include(o => o.Zone)
            .Include(o => o.Carrier)
            .Include(o => o.TrackingEvents)
            .FirstOrDefaultAsync(o => o.Id == id);

        if (order == null) return null;

        return new DeliveryOrderDto
        {
            Id = order.Id,
            OrderId = order.OrderId,
            TrackingNumber = order.TrackingNumber,
            CustomerId = order.CustomerId,
            CustomerName = order.CustomerName,
            CustomerEmail = order.CustomerEmail,
            CustomerPhone = order.CustomerPhone,
            DeliveryAddress = order.DeliveryAddress,
            DeliveryCity = order.DeliveryCity,
            DeliveryRegion = order.DeliveryRegion,
            ZoneId = order.ZoneId,
            ZoneName = order.Zone?.Name ?? "",
            CarrierId = order.CarrierId,
            CarrierName = order.Carrier?.Name ?? "",
            DeliveryFee = order.DeliveryFee,
            TotalFee = order.TotalFee,
            Currency = order.Currency,
            OrderValue = order.OrderValue,
            Weight = order.Weight,
            PackageCount = order.PackageCount,
            Type = order.Type,
            Status = order.Status,
            ScheduledDeliveryDate = order.ScheduledDeliveryDate,
            EstimatedDeliveryDate = order.EstimatedDeliveryDate,
            ActualDeliveryDate = order.ActualDeliveryDate,
            DeliveryInstructions = order.DeliveryInstructions,
            DeliveryAttempts = order.DeliveryAttempts,
            CustomerRating = order.CustomerRating,
            CustomerFeedback = order.CustomerFeedback,
            CreatedAt = order.CreatedAt,
            TrackingEvents = order.TrackingEvents.Select(t => new DeliveryTrackingDto
            {
                Id = t.Id,
                DeliveryOrderId = t.DeliveryOrderId,
                Status = t.Status,
                Description = t.Description,
                Location = t.Location,
                Latitude = t.Latitude,
                Longitude = t.Longitude,
                EventDate = t.EventDate,
                EventBy = t.EventBy,
                Notes = t.Notes,
                PhotoUrl = t.PhotoUrl,
                IsCustomerVisible = t.IsCustomerVisible,
                IsAutomated = t.IsAutomated
            }).OrderByDescending(t => t.EventDate).ToList()
        };
    }

    public async Task<List<DeliveryTrackingDto>> GetDeliveryTrackingByTrackingNumberAsync(string trackingNumber)
    {
        var order = await _context.DeliveryOrders
            .Include(o => o.TrackingEvents)
            .FirstOrDefaultAsync(o => o.TrackingNumber == trackingNumber);

        if (order == null) return new List<DeliveryTrackingDto>();

        return order.TrackingEvents.Select(t => new DeliveryTrackingDto
        {
            Id = t.Id,
            DeliveryOrderId = t.DeliveryOrderId,
            Status = t.Status,
            Description = t.Description,
            Location = t.Location,
            Latitude = t.Latitude,
            Longitude = t.Longitude,
            EventDate = t.EventDate,
            EventBy = t.EventBy,
            Notes = t.Notes,
            PhotoUrl = t.PhotoUrl,
            IsCustomerVisible = t.IsCustomerVisible,
            IsAutomated = t.IsAutomated
        }).OrderByDescending(t => t.EventDate).ToList();
    }

    public async Task<bool> UpdateDeliveryStatusAsync(int deliveryOrderId, UpdateDeliveryStatusRequest request)
    {
        var order = await _context.DeliveryOrders.FindAsync(deliveryOrderId);
        if (order == null) return false;

        order.Status = request.Status;
        if (request.Status == DeliveryStatus.Delivered)
        {
            order.ActualDeliveryDate = DateTime.UtcNow;
        }

        // Add tracking event
        var tracking = new DeliveryTracking
        {
            DeliveryOrderId = deliveryOrderId,
            Status = request.Status,
            Description = request.Description,
            Location = request.Location ?? "",
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            EventDate = DateTime.UtcNow,
            EventBy = request.EventBy,
            Notes = request.Notes ?? "",
            PhotoUrl = request.PhotoUrl,
            IsCustomerVisible = request.IsCustomerVisible
        };

        _context.DeliveryTrackings.Add(tracking);
        await _context.SaveChangesAsync();

        return true;
    }

    // Zone Management Methods
    public async Task<DeliveryZoneDto?> GetDeliveryZoneByCodeAsync(string code)
    {
        var zone = await _context.DeliveryZones
            .Where(z => z.Code == code && z.IsActive)
            .FirstOrDefaultAsync();

        if (zone == null) return null;

        return MapToDeliveryZoneDto(zone);
    }

    public async Task<DeliveryZoneDto> CreateDeliveryZoneAsync(CreateDeliveryZoneRequest request)
    {
        var zone = new DeliveryZone
        {
            Name = request.Name,
            Code = request.Code,
            Description = request.Description,
            Type = request.Type,
            ParentZoneCode = request.ParentZoneCode,
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            Radius = request.Radius,
            BaseDeliveryFee = request.BaseDeliveryFee,
            FreeDeliveryThreshold = request.FreeDeliveryThreshold,
            EstimatedDeliveryDays = request.EstimatedDeliveryDays,
            MaxDeliveryDays = request.MaxDeliveryDays,
            SameDayDeliveryAvailable = request.SameDayDeliveryAvailable,
            SameDayDeliveryFee = request.SameDayDeliveryFee,
            ExpressDeliveryAvailable = request.ExpressDeliveryAvailable,
            ExpressDeliveryFee = request.ExpressDeliveryFee,
            Currency = request.Currency ?? "GNF",
            MaxWeight = request.MaxWeight,
            MaxVolume = request.MaxVolume,
            MaxOrderValue = request.MaxOrderValue,
            MinOrderValue = request.MinOrderValue,
            DeliveryStartTime = request.DeliveryStartTime,
            DeliveryEndTime = request.DeliveryEndTime,
            DeliveryDays = request.DeliveryDays,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.DeliveryZones.Add(zone);
        await _context.SaveChangesAsync();

        return MapToDeliveryZoneDto(zone);
    }

    public async Task<DeliveryZoneDto> UpdateDeliveryZoneAsync(int id, UpdateDeliveryZoneRequest request)
    {
        var zone = await _context.DeliveryZones.FindAsync(id);
        if (zone == null) throw new InvalidOperationException($"Zone with ID {id} not found");

        zone.Name = request.Name;
        zone.Description = request.Description;
        zone.BaseDeliveryFee = request.BaseDeliveryFee;
        zone.FreeDeliveryThreshold = request.FreeDeliveryThreshold;
        zone.EstimatedDeliveryDays = request.EstimatedDeliveryDays;
        zone.MaxDeliveryDays = request.MaxDeliveryDays;
        zone.SameDayDeliveryAvailable = request.SameDayDeliveryAvailable;
        zone.SameDayDeliveryFee = request.SameDayDeliveryFee;
        zone.ExpressDeliveryAvailable = request.ExpressDeliveryAvailable;
        zone.ExpressDeliveryFee = request.ExpressDeliveryFee;
        zone.MaxWeight = request.MaxWeight;
        zone.MaxVolume = request.MaxVolume;
        zone.MaxOrderValue = request.MaxOrderValue;
        zone.MinOrderValue = request.MinOrderValue;
        zone.DeliveryStartTime = request.DeliveryStartTime;
        zone.DeliveryEndTime = request.DeliveryEndTime;
        zone.DeliveryDays = request.DeliveryDays;
        zone.IsActive = request.IsActive;
        zone.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return MapToDeliveryZoneDto(zone);
    }

    public async Task<bool> DeleteDeliveryZoneAsync(int id)
    {
        var zone = await _context.DeliveryZones.FindAsync(id);
        if (zone == null) return false;

        zone.IsActive = false;
        zone.UpdatedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();
        return true;
    }

    // Carrier Management Methods
    public async Task<List<CarrierDto>> GetCarriersAsync(bool activeOnly = true)
    {
        var query = _context.Carriers.AsQueryable();

        if (activeOnly)
            query = query.Where(c => c.IsActive);

        return await query.Select(c => new CarrierDto
        {
            Id = c.Id,
            Name = c.Name,
            Code = c.Code,
            Description = c.Description,
            LogoUrl = c.LogoUrl,
            ContactEmail = c.ContactEmail,
            ContactPhone = c.ContactPhone,
            Address = c.Address,
            Website = c.Website,
            Type = c.Type,
            Rating = c.Rating,
            ReviewCount = c.ReviewCount,
            TotalDeliveries = c.TotalDeliveries,
            SuccessfulDeliveries = c.SuccessfulDeliveries,
            IsActive = c.IsActive
        }).ToListAsync();
    }

    public async Task<CarrierDto?> GetCarrierByIdAsync(int carrierId)
    {
        var carrier = await _context.Carriers
            .Where(c => c.Id == carrierId && c.IsActive)
            .FirstOrDefaultAsync();

        if (carrier == null) return null;

        return MapToCarrierDto(carrier);
    }

    public async Task<CarrierDto?> GetCarrierByCodeAsync(string code)
    {
        var carrier = await _context.Carriers
            .Where(c => c.Code == code && c.IsActive)
            .FirstOrDefaultAsync();

        if (carrier == null) return null;

        return MapToCarrierDto(carrier);
    }

    public async Task<CarrierDto> CreateCarrierAsync(CreateCarrierRequest request)
    {
        var carrier = new Carrier
        {
            Name = request.Name,
            Code = request.Code,
            Description = request.Description,
            LogoUrl = request.LogoUrl,
            ContactEmail = request.ContactEmail,
            ContactPhone = request.ContactPhone,
            Address = request.Address,
            Website = request.Website,
            Type = request.Type,
            ApiEndpoint = request.ApiEndpoint,
            ApiKey = request.ApiKey,
            ApiSecret = request.ApiSecret,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Carriers.Add(carrier);
        await _context.SaveChangesAsync();

        return MapToCarrierDto(carrier);
    }

    public async Task<CarrierDto> UpdateCarrierAsync(int id, UpdateCarrierRequest request)
    {
        var carrier = await _context.Carriers.FindAsync(id);
        if (carrier == null) throw new InvalidOperationException($"Carrier with ID {id} not found");

        carrier.Name = request.Name;
        carrier.Description = request.Description;
        carrier.LogoUrl = request.LogoUrl;
        carrier.ContactEmail = request.ContactEmail;
        carrier.ContactPhone = request.ContactPhone;
        carrier.Address = request.Address;
        carrier.Website = request.Website;
        carrier.Type = request.Type;
        carrier.ApiEndpoint = request.ApiEndpoint;
        carrier.ApiKey = request.ApiKey;
        carrier.ApiSecret = request.ApiSecret;
        carrier.IsActive = request.IsActive;
        carrier.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return MapToCarrierDto(carrier);
    }

    public async Task<bool> DeleteCarrierAsync(int id)
    {
        var carrier = await _context.Carriers.FindAsync(id);
        if (carrier == null) return false;

        carrier.IsActive = false;
        carrier.UpdatedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();
        return true;
    }

    // Carrier Zone Management Methods
    public async Task<List<CarrierZoneDto>> GetCarrierZonesAsync(int? carrierId = null, int? zoneId = null)
    {
        var query = _context.CarrierZones
            .Include(cz => cz.Carrier)
            .Include(cz => cz.Zone)
            .AsQueryable();

        if (carrierId.HasValue)
            query = query.Where(cz => cz.CarrierId == carrierId.Value);

        if (zoneId.HasValue)
            query = query.Where(cz => cz.ZoneId == zoneId.Value);

        return await query.Select(cz => new CarrierZoneDto
        {
            Id = cz.Id,
            CarrierId = cz.CarrierId,
            CarrierName = cz.Carrier!.Name,
            ZoneId = cz.ZoneId,
            ZoneName = cz.Zone!.Name,
            DeliveryFee = cz.DeliveryFee,
            FreeDeliveryThreshold = cz.FreeDeliveryThreshold,
            EstimatedDeliveryDays = cz.EstimatedDeliveryDays,
            MaxDeliveryDays = cz.MaxDeliveryDays,
            SameDayDeliveryAvailable = cz.SameDayDeliveryAvailable,
            SameDayDeliveryFee = cz.SameDayDeliveryFee,
            ExpressDeliveryAvailable = cz.ExpressDeliveryAvailable,
            ExpressDeliveryFee = cz.ExpressDeliveryFee,
            IsActive = cz.IsActive
        }).ToListAsync();
    }

    public async Task<CarrierZoneDto> CreateCarrierZoneAsync(CreateCarrierZoneRequest request)
    {
        var carrierZone = new CarrierZone
        {
            CarrierId = request.CarrierId,
            ZoneId = request.ZoneId,
            DeliveryFee = request.DeliveryFee,
            FreeDeliveryThreshold = request.FreeDeliveryThreshold,
            EstimatedDeliveryDays = request.EstimatedDeliveryDays,
            MaxDeliveryDays = request.MaxDeliveryDays,
            SameDayDeliveryAvailable = request.SameDayDeliveryAvailable,
            SameDayDeliveryFee = request.SameDayDeliveryFee,
            ExpressDeliveryAvailable = request.ExpressDeliveryAvailable,
            ExpressDeliveryFee = request.ExpressDeliveryFee,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.CarrierZones.Add(carrierZone);
        await _context.SaveChangesAsync();

        // Reload with navigation properties
        var created = await _context.CarrierZones
            .Include(cz => cz.Carrier)
            .Include(cz => cz.Zone)
            .FirstAsync(cz => cz.Id == carrierZone.Id);

        return MapToCarrierZoneDto(created);
    }

    public async Task<CarrierZoneDto> UpdateCarrierZoneAsync(int id, UpdateCarrierZoneRequest request)
    {
        var carrierZone = await _context.CarrierZones.FindAsync(id);
        if (carrierZone == null) throw new InvalidOperationException($"CarrierZone with ID {id} not found");

        carrierZone.DeliveryFee = request.DeliveryFee;
        carrierZone.FreeDeliveryThreshold = request.FreeDeliveryThreshold;
        carrierZone.EstimatedDeliveryDays = request.EstimatedDeliveryDays;
        carrierZone.MaxDeliveryDays = request.MaxDeliveryDays;
        carrierZone.SameDayDeliveryAvailable = request.SameDayDeliveryAvailable;
        carrierZone.SameDayDeliveryFee = request.SameDayDeliveryFee;
        carrierZone.ExpressDeliveryAvailable = request.ExpressDeliveryAvailable;
        carrierZone.ExpressDeliveryFee = request.ExpressDeliveryFee;
        carrierZone.IsActive = request.IsActive;
        carrierZone.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        // Reload with navigation properties
        var updated = await _context.CarrierZones
            .Include(cz => cz.Carrier)
            .Include(cz => cz.Zone)
            .FirstAsync(cz => cz.Id == id);

        return MapToCarrierZoneDto(updated);
    }

    public async Task<bool> DeleteCarrierZoneAsync(int id)
    {
        var carrierZone = await _context.CarrierZones.FindAsync(id);
        if (carrierZone == null) return false;

        carrierZone.IsActive = false;
        carrierZone.UpdatedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();
        return true;
    }

    private string GenerateTrackingNumber()
    {
        return $"NP{DateTime.UtcNow:yyyyMMdd}{Random.Shared.Next(1000, 9999)}";
    }

    private static DeliveryZoneDto MapToDeliveryZoneDto(DeliveryZone zone)
    {
        return new DeliveryZoneDto
        {
            Id = zone.Id,
            Name = zone.Name,
            Code = zone.Code,
            Description = zone.Description,
            Type = zone.Type,
            ParentZoneCode = zone.ParentZoneCode,
            Latitude = zone.Latitude,
            Longitude = zone.Longitude,
            Radius = zone.Radius,
            BaseDeliveryFee = zone.BaseDeliveryFee,
            FreeDeliveryThreshold = zone.FreeDeliveryThreshold,
            EstimatedDeliveryDays = zone.EstimatedDeliveryDays,
            MaxDeliveryDays = zone.MaxDeliveryDays,
            SameDayDeliveryAvailable = zone.SameDayDeliveryAvailable,
            SameDayDeliveryFee = zone.SameDayDeliveryFee,
            ExpressDeliveryAvailable = zone.ExpressDeliveryAvailable,
            ExpressDeliveryFee = zone.ExpressDeliveryFee,
            Currency = zone.Currency,
            IsActive = zone.IsActive
        };
    }

    private static CarrierDto MapToCarrierDto(Carrier carrier)
    {
        return new CarrierDto
        {
            Id = carrier.Id,
            Name = carrier.Name,
            Code = carrier.Code,
            Description = carrier.Description,
            LogoUrl = carrier.LogoUrl,
            ContactEmail = carrier.ContactEmail,
            ContactPhone = carrier.ContactPhone,
            Address = carrier.Address,
            Website = carrier.Website,
            Type = carrier.Type,
            Rating = carrier.Rating,
            ReviewCount = carrier.ReviewCount,
            TotalDeliveries = carrier.TotalDeliveries,
            SuccessfulDeliveries = carrier.SuccessfulDeliveries,
            IsActive = carrier.IsActive
        };
    }

    private static CarrierZoneDto MapToCarrierZoneDto(CarrierZone carrierZone)
    {
        return new CarrierZoneDto
        {
            Id = carrierZone.Id,
            CarrierId = carrierZone.CarrierId,
            CarrierName = carrierZone.Carrier?.Name ?? "",
            ZoneId = carrierZone.ZoneId,
            ZoneName = carrierZone.Zone?.Name ?? "",
            DeliveryFee = carrierZone.DeliveryFee,
            FreeDeliveryThreshold = carrierZone.FreeDeliveryThreshold,
            EstimatedDeliveryDays = carrierZone.EstimatedDeliveryDays,
            MaxDeliveryDays = carrierZone.MaxDeliveryDays,
            SameDayDeliveryAvailable = carrierZone.SameDayDeliveryAvailable,
            SameDayDeliveryFee = carrierZone.SameDayDeliveryFee,
            ExpressDeliveryAvailable = carrierZone.ExpressDeliveryAvailable,
            ExpressDeliveryFee = carrierZone.ExpressDeliveryFee,
            IsActive = carrierZone.IsActive
        };
    }
}
