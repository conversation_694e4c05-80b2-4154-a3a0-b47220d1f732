using NafaPlace.Chat.Application.DTOs;

namespace NafaPlace.Chat.Application.Services;

public interface ITicketService
{
    // Gestion des tickets
    Task<int> CreateTicketAsync(CreateTicketDto ticket);
    Task<SupportTicketDto?> GetTicketAsync(int ticketId);
    Task<List<SupportTicketDto>> GetTicketsAsync(TicketFilterDto filter);
    Task<List<SupportTicketDto>> GetUserTicketsAsync(string userId, TicketStatus? status = null);
    Task<List<SupportTicketDto>> GetAgentTicketsAsync(string agentId, TicketStatus? status = null);
    Task<bool> UpdateTicketAsync(UpdateTicketDto update);
    Task<bool> DeleteTicketAsync(int ticketId);
    
    // Gestion des statuts
    Task<bool> ChangeTicketStatusAsync(int ticketId, TicketStatus newStatus, string changedBy, string? reason = null);
    Task<bool> ResolveTicketAsync(int ticketId, string resolution, string resolvedBy);
    Task<bool> CloseTicketAsync(int ticketId, string closedBy, string? reason = null);
    Task<bool> ReopenTicketAsync(int ticketId, string reopenedBy, string? reason = null);
    Task<bool> CancelTicketAsync(int ticketId, string cancelledBy, string? reason = null);
    
    // Assignation et transfert
    Task<bool> AssignTicketAsync(int ticketId, string agentId, string assignedBy);
    Task<bool> UnassignTicketAsync(int ticketId, string unassignedBy);
    Task<bool> TransferTicketAsync(int ticketId, string newAgentId, string transferredBy, string? reason = null);
    Task<bool> TransferToDepartmentAsync(int ticketId, string departmentId, string transferredBy, string? reason = null);
    Task<string?> FindBestAgentForTicketAsync(CreateTicketDto ticket);
    Task<bool> AutoAssignTicketAsync(int ticketId);
    
    // Messages et communication
    Task<int> AddTicketMessageAsync(int ticketId, string senderId, string content, bool isInternal = false, List<TicketAttachmentDto>? attachments = null);
    Task<List<TicketMessageDto>> GetTicketMessagesAsync(int ticketId, bool includeInternal = false);
    Task<bool> MarkTicketAsReadAsync(int ticketId, string userId);
    Task<int> GetUnreadTicketCountAsync(string userId);
    
    // Priorités et escalation
    Task<bool> ChangePriorityAsync(int ticketId, TicketPriority newPriority, string changedBy, string? reason = null);
    Task<bool> EscalateTicketAsync(int ticketId, string escalatedBy, string reason);
    Task<List<SupportTicketDto>> GetOverdueTicketsAsync(DateTime? threshold = null);
    Task<List<SupportTicketDto>> GetHighPriorityTicketsAsync();
    Task ProcessEscalationRulesAsync();
    
    // Tags et métadonnées
    Task<bool> AddTagToTicketAsync(int ticketId, string tag, string addedBy);
    Task<bool> RemoveTagFromTicketAsync(int ticketId, string tag, string removedBy);
    Task<List<string>> GetPopularTagsAsync(int limit = 20);
    Task<bool> UpdateCustomFieldsAsync(int ticketId, Dictionary<string, object> customFields, string updatedBy);
    
    // Recherche et filtrage
    Task<List<SupportTicketDto>> SearchTicketsAsync(string query, TicketFilterDto? filter = null);
    Task<List<SupportTicketDto>> GetTicketsByTagAsync(string tag);
    Task<List<SupportTicketDto>> GetUnassignedTicketsAsync();
    Task<List<SupportTicketDto>> GetTicketsByDepartmentAsync(string departmentId);
    Task<List<SupportTicketDto>> GetTicketsByCategoryAsync(TicketCategory category);
    
    // Statistiques et analytics
    Task<TicketStatsDto> GetTicketStatsAsync(DateTime? startDate = null, DateTime? endDate = null, string? departmentId = null);
    Task<AgentTicketPerformanceDto> GetAgentPerformanceAsync(string agentId, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<AgentTicketPerformanceDto>> GetAllAgentsPerformanceAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, object>> GetDepartmentTicketStatsAsync(string departmentId, DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, int>> GetTicketVolumeAsync(DateTime startDate, DateTime endDate, string groupBy = "day");
    Task<Dictionary<string, double>> GetResponseTimeStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, double>> GetResolutionTimeStatsAsync(DateTime? startDate = null, DateTime? endDate = null);
    
    // Satisfaction client
    Task<bool> SubmitSatisfactionRatingAsync(int ticketId, int rating, string? comment = null);
    Task<double> GetAverageSatisfactionRatingAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<SatisfactionFeedbackDto>> GetSatisfactionFeedbackAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<int, int>> GetSatisfactionDistributionAsync(DateTime? startDate = null, DateTime? endDate = null);
    
    // Historique et audit
    Task<List<TicketHistoryDto>> GetTicketHistoryAsync(int ticketId);
    Task LogTicketActionAsync(int ticketId, TicketHistoryAction action, string performedBy, string description, string? oldValue = null, string? newValue = null);
    Task<List<TicketHistoryDto>> GetUserTicketHistoryAsync(string userId, int limit = 50);
    
    // Templates et automatisation
    Task<int> CreateTicketTemplateAsync(TicketTemplateDto template);
    Task<List<TicketTemplateDto>> GetTicketTemplatesAsync(TicketCategory? category = null);
    Task<TicketTemplateDto?> GetTicketTemplateAsync(int templateId);
    Task<bool> UpdateTicketTemplateAsync(TicketTemplateDto template);
    Task<bool> DeleteTicketTemplateAsync(int templateId);
    Task<int> CreateTicketFromTemplateAsync(int templateId, string customerId, Dictionary<string, object>? customData = null);
    
    // SLA et règles d'escalation
    Task<int> CreateSLAAsync(TicketSLADto sla);
    Task<List<TicketSLADto>> GetSLAsAsync();
    Task<TicketSLADto?> GetApplicableSLAAsync(int ticketId);
    Task<bool> UpdateSLAAsync(TicketSLADto sla);
    Task<bool> DeleteSLAAsync(int slaId);
    Task<List<SupportTicketDto>> GetSLABreachTicketsAsync();
    Task ProcessSLAMonitoringAsync();
    
    // Règles d'escalation
    Task<int> CreateEscalationRuleAsync(TicketEscalationRuleDto rule);
    Task<List<TicketEscalationRuleDto>> GetEscalationRulesAsync();
    Task<bool> UpdateEscalationRuleAsync(TicketEscalationRuleDto rule);
    Task<bool> DeleteEscalationRuleAsync(int ruleId);
    Task<bool> TestEscalationRuleAsync(int ruleId, int ticketId);
    
    // Workflows
    Task<int> CreateWorkflowAsync(TicketWorkflowDto workflow);
    Task<List<TicketWorkflowDto>> GetWorkflowsAsync();
    Task<bool> UpdateWorkflowAsync(TicketWorkflowDto workflow);
    Task<bool> DeleteWorkflowAsync(int workflowId);
    Task<bool> ExecuteWorkflowAsync(int workflowId, int ticketId, Dictionary<string, object>? context = null);
    Task ProcessWorkflowTriggersAsync();
    
    // Rapports et exports
    Task<byte[]> ExportTicketsAsync(TicketFilterDto filter, string format = "csv");
    Task<byte[]> ExportTicketStatsAsync(DateTime startDate, DateTime endDate, string format = "pdf");
    Task<byte[]> ExportAgentPerformanceAsync(string agentId, DateTime startDate, DateTime endDate, string format = "pdf");
    Task<Dictionary<string, object>> GenerateTicketSummaryAsync(int ticketId);
    Task<List<string>> GetTicketKeywordsAsync(int ticketId);
    
    // Intégrations
    Task<int> CreateTicketFromEmailAsync(string fromEmail, string subject, string body, List<TicketAttachmentDto>? attachments = null);
    Task<int> CreateTicketFromChatAsync(int conversationId, string subject, string description);
    Task<int> CreateTicketFromWebFormAsync(Dictionary<string, object> formData);
    Task<bool> SyncTicketWithExternalSystemAsync(int ticketId, string externalSystemId);
    
    // Notifications et alertes
    Task SendTicketNotificationAsync(int ticketId, string notificationType, List<string> recipients);
    Task SendSLABreachAlertAsync(int ticketId);
    Task SendEscalationNotificationAsync(int ticketId, string reason);
    Task SendTicketAssignmentNotificationAsync(int ticketId, string agentId);
    Task SendTicketUpdateNotificationAsync(int ticketId, string updateType);
    
    // Configuration et maintenance
    Task<Dictionary<string, object>> GetTicketConfigAsync();
    Task<bool> UpdateTicketConfigAsync(Dictionary<string, object> config);
    Task<int> CleanupOldTicketsAsync(int daysToKeep = 365);
    Task<int> ArchiveResolvedTicketsAsync(int daysAfterResolution = 90);
    Task<bool> TestTicketServiceAsync();
    
    // Gestion des fichiers
    Task<string> UploadTicketAttachmentAsync(Stream fileStream, string fileName, string contentType, int ticketId, string uploadedBy);
    Task<Stream> DownloadTicketAttachmentAsync(string fileUrl);
    Task<bool> DeleteTicketAttachmentAsync(string fileUrl, int ticketId);
    Task<List<TicketAttachmentDto>> GetTicketAttachmentsAsync(int ticketId);
    
    // Modération et sécurité
    Task<bool> FlagTicketAsync(int ticketId, string reason, string flaggedBy);
    Task<List<SupportTicketDto>> GetFlaggedTicketsAsync();
    Task<bool> UnflagTicketAsync(int ticketId, string unflaggedBy);
    Task<bool> BlockCustomerAsync(string customerId, string reason, string blockedBy);
    Task<bool> UnblockCustomerAsync(string customerId, string unblockedBy);
    Task<List<string>> GetBlockedCustomersAsync();
    
    // Métriques avancées
    Task<Dictionary<string, object>> GetTicketTrendsAsync(DateTime startDate, DateTime endDate);
    Task<Dictionary<string, object>> GetCustomerSatisfactionTrendsAsync(DateTime startDate, DateTime endDate);
    Task<Dictionary<string, object>> GetAgentWorkloadAnalysisAsync(DateTime startDate, DateTime endDate);
    Task<Dictionary<string, object>> GetTicketResolutionAnalysisAsync(DateTime startDate, DateTime endDate);
    Task<List<string>> GetFrequentIssuesAsync(int limit = 10);
    Task<Dictionary<string, object>> GetTicketChannelAnalysisAsync(DateTime startDate, DateTime endDate);
    
    // Automatisation et IA
    Task<List<string>> SuggestTicketTagsAsync(int ticketId);
    Task<string?> SuggestTicketCategoryAsync(string subject, string description);
    Task<TicketPriority> SuggestTicketPriorityAsync(string subject, string description);
    Task<string?> SuggestBestAgentAsync(int ticketId);
    Task<List<string>> GetSimilarTicketsAsync(int ticketId, int limit = 5);
    Task<string?> GenerateTicketSummaryAsync(int ticketId);
    Task<List<string>> SuggestResolutionStepsAsync(int ticketId);
    
    // Intégration avec d'autres services
    Task HandleOrderIssueTicketAsync(int orderId, string issue, string customerId);
    Task HandleProductIssueTicketAsync(int productId, string issue, string customerId);
    Task HandlePaymentIssueTicketAsync(int orderId, string issue, string customerId);
    Task HandleShippingIssueTicketAsync(int orderId, string issue, string customerId);
    Task HandleRefundRequestTicketAsync(int orderId, string reason, string customerId);
    Task HandleAccountIssueTicketAsync(string issue, string customerId);
}
