using NafaPlace.Web.Models;

namespace NafaPlace.Web.Services;

public interface IAgentManagementService
{
    // Gestion des agents
    Task<List<ChatAgentDto>> GetAllAgentsAsync();
    Task<ChatAgentDto?> GetAgentAsync(string agentId);
    Task<bool> CreateAgentAsync(CreateAgentRequest request);
    Task<bool> UpdateAgentAsync(string agentId, UpdateAgentRequest request);
    Task<bool> DeleteAgentAsync(string agentId);
    Task<bool> SetAgentStatusAsync(string agentId, string status);
    
    // Assignation de conversations
    Task<bool> AssignConversationToAgentAsync(int conversationId, string agentId);
    Task<bool> UnassignConversationAsync(int conversationId);
    Task<bool> TransferConversationAsync(int conversationId, string fromAgentId, string toAgentId, string reason);
    Task<string?> FindBestAvailableAgentAsync(string? departmentId = null);
    
    // Statistiques des agents
    Task<AgentStatsDto> GetAgentStatsAsync(string agentId, DateTime startDate, DateTime endDate);
    Task<List<ConversationSummaryDto>> GetAgentConversationsAsync(string agentId, int page = 1, int pageSize = 20);
    Task<AgentPerformanceDto> GetAgentPerformanceAsync(string agentId, DateTime startDate, DateTime endDate);
    
    // Gestion des départements
    Task<List<DepartmentDto>> GetDepartmentsAsync();
    Task<bool> CreateDepartmentAsync(CreateDepartmentRequest request);
    Task<bool> UpdateDepartmentAsync(int departmentId, UpdateDepartmentRequest request);
    Task<bool> DeleteDepartmentAsync(int departmentId);
    
    // Surveillance en temps réel
    Task<List<ActiveConversationDto>> GetActiveConversationsAsync();
    Task<ChatOverviewDto> GetChatOverviewAsync();
    Task<List<WaitingConversationDto>> GetWaitingConversationsAsync();
}

// DTOs pour les requêtes
public class CreateAgentRequest
{
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Avatar { get; set; }
    public List<int> DepartmentIds { get; set; } = new();
    public List<string> Skills { get; set; } = new();
    public List<string> Languages { get; set; } = new();
    public int MaxConcurrentChats { get; set; } = 5;
    public AgentWorkingHours WorkingHours { get; set; } = new();
}

public class UpdateAgentRequest
{
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? Avatar { get; set; }
    public List<int>? DepartmentIds { get; set; }
    public List<string>? Skills { get; set; }
    public List<string>? Languages { get; set; }
    public int? MaxConcurrentChats { get; set; }
    public AgentWorkingHours? WorkingHours { get; set; }
    public bool? IsActive { get; set; }
}

public class CreateDepartmentRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
}

public class UpdateDepartmentRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
}

// DTOs pour les réponses
public class AgentStatsDto
{
    public string AgentId { get; set; } = string.Empty;
    public string AgentName { get; set; } = string.Empty;
    public int TotalConversations { get; set; }
    public int CompletedConversations { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public TimeSpan AverageResolutionTime { get; set; }
    public double AverageSatisfactionRating { get; set; }
    public int TotalMessages { get; set; }
    public TimeSpan TotalActiveTime { get; set; }
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

public class AgentPerformanceDto
{
    public string AgentId { get; set; } = string.Empty;
    public double PerformanceScore { get; set; }
    public int Rank { get; set; }
    public List<PerformanceMetric> Metrics { get; set; } = new();
    public List<string> Strengths { get; set; } = new();
    public List<string> ImprovementAreas { get; set; } = new();
}

public class PerformanceMetric
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public double Target { get; set; }
    public string Unit { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty; // "Good", "Warning", "Critical"
}

public class DepartmentDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public int AgentCount { get; set; }
    public int ActiveConversations { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class ActiveConversationDto
{
    public int Id { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string? AgentName { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime LastMessageAt { get; set; }
    public int MessageCount { get; set; }
    public TimeSpan Duration { get; set; }
}

public class WaitingConversationDto
{
    public int Id { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public TimeSpan WaitingTime { get; set; }
    public string? PreferredDepartment { get; set; }
}

public class ChatOverviewDto
{
    public int TotalActiveConversations { get; set; }
    public int TotalWaitingConversations { get; set; }
    public int OnlineAgents { get; set; }
    public int BusyAgents { get; set; }
    public int AvailableAgents { get; set; }
    public double AverageWaitTime { get; set; }
    public double AverageResponseTime { get; set; }
    public double SatisfactionRating { get; set; }
    public int TodayConversations { get; set; }
    public int TodayMessages { get; set; }
}

public class ConversationSummaryDto
{
    public int Id { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string CustomerName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? EndedAt { get; set; }
    public TimeSpan Duration { get; set; }
    public int MessageCount { get; set; }
    public int? SatisfactionRating { get; set; }
    public List<string> Tags { get; set; } = new();
}

public class AgentWorkingHours
{
    public DaySchedule Monday { get; set; } = new();
    public DaySchedule Tuesday { get; set; } = new();
    public DaySchedule Wednesday { get; set; } = new();
    public DaySchedule Thursday { get; set; } = new();
    public DaySchedule Friday { get; set; } = new();
    public DaySchedule Saturday { get; set; } = new();
    public DaySchedule Sunday { get; set; } = new();
}

public class DaySchedule
{
    public bool IsWorkingDay { get; set; } = true;
    public TimeOnly StartTime { get; set; } = new(9, 0);
    public TimeOnly EndTime { get; set; } = new(17, 0);
    public List<BreakPeriod> Breaks { get; set; } = new();
}

public class BreakPeriod
{
    public TimeOnly StartTime { get; set; }
    public TimeOnly EndTime { get; set; }
    public string Description { get; set; } = string.Empty;
}
