using Microsoft.EntityFrameworkCore;
using NafaPlace.Chat.Domain.Entities;

namespace NafaPlace.Chat.Infrastructure.Data;

public class ChatDbContext : DbContext
{
    public ChatDbContext(DbContextOptions<ChatDbContext> options) : base(options)
    {
    }

    public DbSet<Conversation> Conversations { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<Agent> Agents { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<Ticket> Tickets { get; set; }
    public DbSet<FAQ> FAQs { get; set; }
    public DbSet<ChatbotInteraction> ChatbotInteractions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configuration Conversations
        modelBuilder.Entity<Conversation>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Subject).HasMaxLength(200).IsRequired();
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.UserName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.UserEmail).HasMaxLength(200).IsRequired();
            entity.Property(e => e.AssignedAgentId).HasMaxLength(100);
            entity.Property(e => e.AssignedAgentName).HasMaxLength(100);
            entity.Property(e => e.Tags).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.IsArchived).HasDefaultValue(false);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.AssignedAgentId);
            entity.HasIndex(e => new { e.Status, e.Priority });

            entity.HasOne<Department>()
                .WithMany()
                .HasForeignKey(e => e.DepartmentId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // Configuration Messages
        modelBuilder.Entity<Message>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Content).IsRequired();
            entity.Property(e => e.SenderId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.SenderName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.IsRead).HasDefaultValue(false);
            entity.Property(e => e.IsEdited).HasDefaultValue(false);
            entity.Property(e => e.Attachments).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.ConversationId, e.CreatedAt });
            entity.HasIndex(e => e.ParentMessageId);

            entity.HasOne<Conversation>()
                .WithMany()
                .HasForeignKey(e => e.ConversationId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne<Message>()
                .WithMany()
                .HasForeignKey(e => e.ParentMessageId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // Configuration Agents
        modelBuilder.Entity<Agent>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasMaxLength(100);
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Email).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Avatar).HasMaxLength(500);
            entity.Property(e => e.MaxConcurrentChats).HasDefaultValue(5);
            entity.Property(e => e.CurrentChatCount).HasDefaultValue(0);
            entity.Property(e => e.Skills).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.Languages).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.WorkingHours).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.Status, e.IsActive });
            entity.HasIndex(e => e.DepartmentId);

            entity.HasOne<Department>()
                .WithMany()
                .HasForeignKey(e => e.DepartmentId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // Configuration Departments
        modelBuilder.Entity<Department>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Email).HasMaxLength(200);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.WorkingHours).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.AutoAssignment).HasDefaultValue(true);
            entity.Property(e => e.MaxQueueSize).HasDefaultValue(100);
            entity.Property(e => e.Priority).HasDefaultValue(1);
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        });

        // Configuration Tickets
        modelBuilder.Entity<Ticket>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TicketNumber).HasMaxLength(50).IsRequired();
            entity.Property(e => e.Subject).HasMaxLength(200).IsRequired();
            entity.Property(e => e.Description).IsRequired();
            entity.Property(e => e.UserId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.UserName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.UserEmail).HasMaxLength(200).IsRequired();
            entity.Property(e => e.AssignedAgentId).HasMaxLength(100);
            entity.Property(e => e.AssignedAgentName).HasMaxLength(100);
            entity.Property(e => e.Tags).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => e.TicketNumber).IsUnique();
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.AssignedAgentId);
            entity.HasIndex(e => new { e.Status, e.Priority });

            entity.HasOne<Conversation>()
                .WithMany()
                .HasForeignKey(e => e.ConversationId)
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne<Department>()
                .WithMany()
                .HasForeignKey(e => e.DepartmentId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // Configuration FAQs
        modelBuilder.Entity<FAQ>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Question).HasMaxLength(500).IsRequired();
            entity.Property(e => e.Answer).IsRequired();
            entity.Property(e => e.Tags).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.Keywords).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.ViewCount).HasDefaultValue(0);
            entity.Property(e => e.HelpfulCount).HasDefaultValue(0);
            entity.Property(e => e.NotHelpfulCount).HasDefaultValue(0);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsFeatured).HasDefaultValue(false);
            entity.Property(e => e.Priority).HasDefaultValue(1);
            entity.Property(e => e.Language).HasMaxLength(10).HasDefaultValue("fr");
            entity.Property(e => e.CreatedBy).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Metadata).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => new { e.Category, e.IsActive });
            entity.HasIndex(e => e.Language);
        });

        // Configuration ChatbotInteractions
        modelBuilder.Entity<ChatbotInteraction>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.SessionId).HasMaxLength(100).IsRequired();
            entity.Property(e => e.UserId).HasMaxLength(100);
            entity.Property(e => e.UserMessage).IsRequired();
            entity.Property(e => e.BotResponse).IsRequired();
            entity.Property(e => e.Intent).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Entities).HasColumnType("jsonb").HasDefaultValue("[]");
            entity.Property(e => e.WasHelpful).HasDefaultValue(false);
            entity.Property(e => e.RequiredHandoff).HasDefaultValue(false);
            entity.Property(e => e.Context).HasColumnType("jsonb").HasDefaultValue("{}");
            entity.Property(e => e.Timestamp).HasDefaultValueSql("CURRENT_TIMESTAMP");

            entity.HasIndex(e => e.SessionId);
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => new { e.Intent, e.Timestamp });
        });
    }
}
