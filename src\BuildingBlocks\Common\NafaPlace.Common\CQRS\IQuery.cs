using MediatR;

namespace NafaPlace.Common.CQRS;

/// <summary>
/// Interface de base pour toutes les requêtes
/// </summary>
/// <typeparam name="TResult">Type du résultat de la requête</typeparam>
public interface IQuery<TResult> : IRequest<TResult>
{
    /// <summary>
    /// Identifiant unique de la requête pour le tracking
    /// </summary>
    int QueryId { get; }
    
    /// <summary>
    /// Timestamp de création de la requête
    /// </summary>
    DateTime CreatedAt { get; }
    
    /// <summary>
    /// Identifiant de l'utilisateur qui exécute la requête
    /// </summary>
    string? UserId { get; }
    
    /// <summary>
    /// Indique si la requête peut être mise en cache
    /// </summary>
    bool IsCacheable { get; }
    
    /// <summary>
    /// Durée de vie du cache en secondes
    /// </summary>
    int CacheTtlSeconds { get; }
    
    /// <summary>
    /// Clé de cache personnalisée
    /// </summary>
    string? Cache<PERSON>ey { get; }
}

/// <summary>
/// Classe de base pour les requêtes
/// </summary>
/// <typeparam name="TResult">Type du résultat</typeparam>
public abstract class BaseQuery<TResult> : IQuery<TResult>
{
    protected BaseQuery()
    {
        QueryId = Random.Shared.Next(1, int.MaxValue);
        CreatedAt = DateTime.UtcNow;
        IsCacheable = false;
        CacheTtlSeconds = 300; // 5 minutes par défaut
    }

    public int QueryId { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public string? UserId { get; set; }
    public virtual bool IsCacheable { get; protected set; }
    public virtual int CacheTtlSeconds { get; protected set; }
    public virtual string? CacheKey { get; protected set; }
    
    /// <summary>
    /// Génère une clé de cache basée sur les propriétés de la requête
    /// </summary>
    /// <returns>Clé de cache</returns>
    public virtual string GenerateCacheKey()
    {
        var type = GetType().Name;
        var properties = GetType().GetProperties()
            .Where(p => p.CanRead && p.PropertyType.IsValueType || p.PropertyType == typeof(string))
            .Select(p => $"{p.Name}:{p.GetValue(this)}")
            .OrderBy(x => x);
        
        return $"{type}:{string.Join("|", properties)}";
    }
}

/// <summary>
/// Interface pour les requêtes paginées
/// </summary>
/// <typeparam name="TResult">Type du résultat</typeparam>
public interface IPagedQuery<TResult> : IQuery<TResult>
{
    /// <summary>
    /// Numéro de page (commence à 1)
    /// </summary>
    int PageNumber { get; }
    
    /// <summary>
    /// Taille de la page
    /// </summary>
    int PageSize { get; }
    
    /// <summary>
    /// Champ de tri
    /// </summary>
    string? SortBy { get; }
    
    /// <summary>
    /// Direction du tri (asc/desc)
    /// </summary>
    string? SortDirection { get; }
}

/// <summary>
/// Classe de base pour les requêtes paginées
/// </summary>
/// <typeparam name="TResult">Type du résultat</typeparam>
public abstract class BasePagedQuery<TResult> : BaseQuery<TResult>, IPagedQuery<TResult>
{
    protected BasePagedQuery()
    {
        PageNumber = 1;
        PageSize = 20;
        SortDirection = "asc";
    }

    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; }
    
    public override string GenerateCacheKey()
    {
        return $"{base.GenerateCacheKey()}:Page{PageNumber}:Size{PageSize}:Sort{SortBy}:{SortDirection}";
    }
}
