using MediatR;
using Microsoft.Extensions.Logging;

namespace NafaPlace.Common.CQRS;

/// <summary>
/// Interface pour les gestionnaires de requêtes
/// </summary>
/// <typeparam name="TQuery">Type de la requête</typeparam>
/// <typeparam name="TResult">Type du résultat</typeparam>
public interface IQueryHandler<TQuery, TResult> : IRequestHandler<TQuery, TResult>
    where TQuery : IQuery<TResult>
{
}

/// <summary>
/// Classe de base pour les gestionnaires de requêtes
/// </summary>
/// <typeparam name="TQuery">Type de la requête</typeparam>
/// <typeparam name="TResult">Type du résultat</typeparam>
public abstract class BaseQueryHandler<TQuery, TResult> : IQueryHandler<TQuery, TResult>
    where TQuery : IQuery<TResult>
{
    protected readonly ILogger<BaseQueryHandler<TQuery, TResult>> Logger;

    protected BaseQueryHandler(ILogger<BaseQueryHandler<TQuery, TResult>> logger)
    {
        Logger = logger;
    }

    public virtual async Task<TResult> Handle(TQuery request, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Handling query {QueryType} with ID {QueryId}", 
            typeof(TQuery).Name, request.QueryId);

        try
        {
            await ValidateQuery(request, cancellationToken);
            var result = await ExecuteQuery(request, cancellationToken);
            
            Logger.LogInformation("Successfully handled query {QueryType} with ID {QueryId}", 
                typeof(TQuery).Name, request.QueryId);
            
            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling query {QueryType} with ID {QueryId}", 
                typeof(TQuery).Name, request.QueryId);
            throw;
        }
    }

    /// <summary>
    /// Valide la requête avant exécution
    /// </summary>
    /// <param name="query">Requête à valider</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    protected virtual Task ValidateQuery(TQuery query, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// Exécute la logique de la requête
    /// </summary>
    /// <param name="query">Requête à exécuter</param>
    /// <param name="cancellationToken">Token d'annulation</param>
    /// <returns>Résultat de la requête</returns>
    protected abstract Task<TResult> ExecuteQuery(TQuery query, CancellationToken cancellationToken);
}

/// <summary>
/// Classe de base pour les gestionnaires de requêtes avec cache
/// </summary>
/// <typeparam name="TQuery">Type de la requête</typeparam>
/// <typeparam name="TResult">Type du résultat</typeparam>
public abstract class BaseCachedQueryHandler<TQuery, TResult> : BaseQueryHandler<TQuery, TResult>
    where TQuery : IQuery<TResult>
{
    protected readonly ICacheService CacheService;

    protected BaseCachedQueryHandler(
        ILogger<BaseCachedQueryHandler<TQuery, TResult>> logger,
        ICacheService cacheService) : base(logger)
    {
        CacheService = cacheService;
    }

    public override async Task<TResult> Handle(TQuery request, CancellationToken cancellationToken)
    {
        // Si la requête n'est pas cacheable, exécuter directement
        if (!request.IsCacheable)
        {
            return await base.Handle(request, cancellationToken);
        }

        var cacheKey = request.CacheKey ?? GenerateCacheKey(request);
        
        // Essayer de récupérer depuis le cache
        var cachedResult = await CacheService.GetAsync<TResult>(cacheKey, cancellationToken);
        if (cachedResult != null)
        {
            Logger.LogInformation("Cache hit for query {QueryType} with key {CacheKey}", 
                typeof(TQuery).Name, cacheKey);
            return cachedResult;
        }

        // Exécuter la requête
        var result = await base.Handle(request, cancellationToken);
        
        // Mettre en cache le résultat
        await CacheService.SetAsync(cacheKey, result, TimeSpan.FromSeconds(request.CacheTtlSeconds), cancellationToken);
        
        Logger.LogInformation("Cached result for query {QueryType} with key {CacheKey}", 
            typeof(TQuery).Name, cacheKey);

        return result;
    }

    /// <summary>
    /// Génère une clé de cache pour la requête
    /// </summary>
    /// <param name="query">Requête</param>
    /// <returns>Clé de cache</returns>
    protected virtual string GenerateCacheKey(TQuery query)
    {
        if (query is BaseQuery<TResult> baseQuery)
        {
            return baseQuery.GenerateCacheKey();
        }
        
        return $"{typeof(TQuery).Name}:{query.QueryId}";
    }
}

/// <summary>
/// Interface pour le service de cache
/// </summary>
public interface ICacheService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default);
    Task SetAsync<T>(string key, T value, TimeSpan expiration, CancellationToken cancellationToken = default);
    Task RemoveAsync(string key, CancellationToken cancellationToken = default);
    Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
}
