using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Threading.RateLimiting;

namespace NafaPlace.Infrastructure.RateLimiting;

/// <summary>
/// Extensions pour configurer le rate limiting
/// </summary>
public static class RateLimitingExtensions
{
    /// <summary>
    /// Ajoute les services de rate limiting
    /// </summary>
    public static IServiceCollection AddNafaPlaceRateLimiting(this IServiceCollection services)
    {
        services.AddRateLimiter(options =>
        {
            // Politique globale par défaut
            options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(context =>
            {
                var userIdentifier = GetUserIdentifier(context);
                
                return RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: userIdentifier,
                    factory: _ => new FixedWindowRateLimiterOptions
                    {
                        PermitLimit = 100,
                        Window = TimeSpan.FromMinutes(1),
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 10
                    });
            });

            // Politique pour les API publiques
            options.AddPolicy("PublicApi", context =>
            {
                var userIdentifier = GetUserIdentifier(context);
                
                return RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: userIdentifier,
                    factory: _ => new FixedWindowRateLimiterOptions
                    {
                        PermitLimit = 60,
                        Window = TimeSpan.FromMinutes(1),
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 5
                    });
            });

            // Politique pour les API d'authentification
            options.AddPolicy("Auth", context =>
            {
                var clientIp = GetClientIpAddress(context);
                
                return RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: clientIp,
                    factory: _ => new FixedWindowRateLimiterOptions
                    {
                        PermitLimit = 5,
                        Window = TimeSpan.FromMinutes(1),
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 0
                    });
            });

            // Politique pour les uploads de fichiers
            options.AddPolicy("FileUpload", context =>
            {
                var userIdentifier = GetUserIdentifier(context);
                
                return RateLimitPartition.GetTokenBucketLimiter(
                    partitionKey: userIdentifier,
                    factory: _ => new TokenBucketRateLimiterOptions
                    {
                        TokenLimit = 10,
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 3,
                        ReplenishmentPeriod = TimeSpan.FromMinutes(1),
                        TokensPerPeriod = 5,
                        AutoReplenishment = true
                    });
            });

            // Politique pour les recherches
            options.AddPolicy("Search", context =>
            {
                var userIdentifier = GetUserIdentifier(context);
                
                return RateLimitPartition.GetSlidingWindowLimiter(
                    partitionKey: userIdentifier,
                    factory: _ => new SlidingWindowRateLimiterOptions
                    {
                        PermitLimit = 30,
                        Window = TimeSpan.FromMinutes(1),
                        SegmentsPerWindow = 6,
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 5
                    });
            });

            // Politique pour les vendeurs (plus permissive)
            options.AddPolicy("Seller", context =>
            {
                var userIdentifier = GetUserIdentifier(context);
                
                return RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: userIdentifier,
                    factory: _ => new FixedWindowRateLimiterOptions
                    {
                        PermitLimit = 200,
                        Window = TimeSpan.FromMinutes(1),
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 20
                    });
            });

            // Politique pour les administrateurs (très permissive)
            options.AddPolicy("Admin", context =>
            {
                var userIdentifier = GetUserIdentifier(context);
                
                return RateLimitPartition.GetFixedWindowLimiter(
                    partitionKey: userIdentifier,
                    factory: _ => new FixedWindowRateLimiterOptions
                    {
                        PermitLimit = 1000,
                        Window = TimeSpan.FromMinutes(1),
                        QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                        QueueLimit = 50
                    });
            });

            // Gestionnaire de rejet personnalisé
            options.OnRejected = async (context, token) =>
            {
                context.HttpContext.Response.StatusCode = 429;
                context.HttpContext.Response.ContentType = "application/json";

                var response = new
                {
                    error = "Rate limit exceeded",
                    message = "Trop de requêtes. Veuillez réessayer plus tard.",
                    retryAfter = GetRetryAfterSeconds(context),
                    timestamp = DateTime.UtcNow
                };

                var logger = context.HttpContext.RequestServices.GetService<ILogger<RateLimitingMiddleware>>();
                logger?.LogWarning("Rate limit exceeded for {UserIdentifier} on {Path}", 
                    GetUserIdentifier(context.HttpContext), 
                    context.HttpContext.Request.Path);

                await context.HttpContext.Response.WriteAsJsonAsync(response, token);
            };
        });

        services.AddSingleton<IRateLimitingService, RateLimitingService>();
        
        return services;
    }

    /// <summary>
    /// Configure le middleware de rate limiting
    /// </summary>
    public static IApplicationBuilder UseNafaPlaceRateLimiting(this IApplicationBuilder app)
    {
        app.UseRateLimiter();
        return app;
    }

    private static string GetUserIdentifier(HttpContext context)
    {
        // Priorité : UserId > Username > IP Address
        var userId = context.User?.FindFirst("sub")?.Value ?? 
                    context.User?.FindFirst("userId")?.Value;
        
        if (!string.IsNullOrEmpty(userId))
            return $"user:{userId}";

        var username = context.User?.Identity?.Name;
        if (!string.IsNullOrEmpty(username))
            return $"username:{username}";

        return $"ip:{GetClientIpAddress(context)}";
    }

    private static string GetClientIpAddress(HttpContext context)
    {
        var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        
        if (string.IsNullOrEmpty(ipAddress))
            ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        
        if (string.IsNullOrEmpty(ipAddress))
            ipAddress = context.Connection.RemoteIpAddress?.ToString();

        return ipAddress ?? "unknown";
    }

    private static int GetRetryAfterSeconds(OnRejectedContext context)
    {
        if (context.Lease.TryGetMetadata(MetadataName.RetryAfter, out var retryAfter))
        {
            return (int)retryAfter.TotalSeconds;
        }

        return 60; // Par défaut, réessayer après 1 minute
    }
}

/// <summary>
/// Service pour gérer le rate limiting programmatiquement
/// </summary>
public interface IRateLimitingService
{
    Task<bool> IsAllowedAsync(string key, int limit, TimeSpan window, CancellationToken cancellationToken = default);
    Task<RateLimitStatus> GetStatusAsync(string key, CancellationToken cancellationToken = default);
    Task ResetAsync(string key, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implémentation du service de rate limiting
/// </summary>
public class RateLimitingService : IRateLimitingService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RateLimitingService> _logger;

    public RateLimitingService(IServiceProvider serviceProvider, ILogger<RateLimitingService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task<bool> IsAllowedAsync(string key, int limit, TimeSpan window, CancellationToken cancellationToken = default)
    {
        try
        {
            // Implémentation basée sur Redis ou en mémoire
            // Pour cet exemple, nous utilisons une logique simplifiée
            return true; // À implémenter selon vos besoins
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification du rate limit pour {Key}", key);
            return true; // En cas d'erreur, on autorise la requête
        }
    }

    public async Task<RateLimitStatus> GetStatusAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            // Récupérer le statut actuel du rate limiting
            return new RateLimitStatus
            {
                Key = key,
                RequestsRemaining = 100,
                ResetTime = DateTime.UtcNow.AddMinutes(1),
                IsBlocked = false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du statut pour {Key}", key);
            return new RateLimitStatus { Key = key, IsBlocked = false };
        }
    }

    public async Task ResetAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            // Réinitialiser le compteur pour une clé donnée
            _logger.LogInformation("Rate limit reset pour {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la réinitialisation pour {Key}", key);
        }
    }
}

/// <summary>
/// Statut du rate limiting
/// </summary>
public class RateLimitStatus
{
    public string Key { get; set; } = string.Empty;
    public int RequestsRemaining { get; set; }
    public DateTime ResetTime { get; set; }
    public bool IsBlocked { get; set; }
    public TimeSpan? RetryAfter { get; set; }
}

/// <summary>
/// Middleware personnalisé pour le rate limiting
/// </summary>
public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RateLimitingMiddleware> _logger;

    public RateLimitingMiddleware(RequestDelegate next, ILogger<RateLimitingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Logique personnalisée de rate limiting si nécessaire
        await _next(context);
    }
}
