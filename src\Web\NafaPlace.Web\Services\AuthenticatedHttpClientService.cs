using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Authorization;
using System.Net.Http.Headers;

namespace NafaPlace.Web.Services;

public interface IAuthenticatedHttpClientService
{
    Task<HttpClient> GetAuthenticatedHttpClientAsync(string baseAddress);
    Task<HttpClient> GetAuthenticatedHttpClientAsync(Uri baseAddress);
}

public class AuthenticatedHttpClientService : IAuthenticatedHttpClientService
{
    private readonly ILocalStorageService _localStorage;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ILogger<AuthenticatedHttpClientService> _logger;

    public AuthenticatedHttpClientService(
        ILocalStorageService localStorage,
        AuthenticationStateProvider authStateProvider,
        ILogger<AuthenticatedHttpClientService> logger)
    {
        _localStorage = localStorage;
        _authStateProvider = authStateProvider;
        _logger = logger;
    }

    public async Task<HttpClient> GetAuthenticatedHttpClientAsync(string baseAddress)
    {
        return await GetAuthenticatedHttpClientAsync(new Uri(baseAddress));
    }

    public async Task<HttpClient> GetAuthenticatedHttpClientAsync(Uri baseAddress)
    {
        var httpClient = new HttpClient();
        httpClient.BaseAddress = baseAddress;

        try
        {
            // Vérifier d'abord l'état d'authentification
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated != true)
            {
                _logger.LogDebug("Utilisateur non authentifié pour {BaseAddress}", baseAddress);
                return httpClient; // Retourner le client sans token
            }

            // Récupérer le token du localStorage directement
            var token = await _localStorage.GetItemAsStringAsync("authToken");
            _logger.LogDebug("Token récupéré: {TokenPresent}", !string.IsNullOrEmpty(token) ? "Présent" : "Absent");

            if (!string.IsNullOrEmpty(token))
            {
                // Supprimer les guillemets éventuels
                token = token.Trim('"');

                // Vérifier que le token n'est pas expiré
                if (!IsTokenExpired(token))
                {
                    httpClient.DefaultRequestHeaders.Authorization =
                        new AuthenticationHeaderValue("Bearer", token);

                    _logger.LogDebug("Token d'authentification configuré pour {BaseAddress}", baseAddress);
                }
                else
                {
                    _logger.LogWarning("Token expiré détecté pour {BaseAddress}", baseAddress);
                    // Nettoyer le token expiré
                    await _localStorage.RemoveItemAsync("authToken");
                }
            }
            else
            {
                _logger.LogWarning("Utilisateur authentifié mais aucun token trouvé pour {BaseAddress}", baseAddress);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la configuration du token d'authentification pour {BaseAddress}", baseAddress);
        }

        return httpClient;
    }

    private bool IsTokenExpired(string token)
    {
        try
        {
            var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);
            
            var expiry = jsonToken.Claims.FirstOrDefault(c => c.Type == "exp")?.Value;
            if (expiry != null && long.TryParse(expiry, out var expiryTimestamp))
            {
                var expiryDate = DateTimeOffset.FromUnixTimeSeconds(expiryTimestamp).DateTime;
                return expiryDate < DateTime.UtcNow;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification de l'expiration du token");
            return true; // Considérer comme expiré en cas d'erreur
        }

        return false;
    }
}
