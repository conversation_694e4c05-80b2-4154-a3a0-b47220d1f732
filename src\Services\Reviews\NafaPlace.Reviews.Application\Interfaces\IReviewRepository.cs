using NafaPlace.Reviews.Domain.Models;

namespace NafaPlace.Reviews.Application.Interfaces;

public interface IReviewRepository
{
    // Review operations
    Task<Review?> GetByIdAsync(int id);
    Task<IEnumerable<Review>> GetByProductIdAsync(int productId, int page = 1, int pageSize = 10);
    Task<IEnumerable<Review>> GetByUserIdAsync(string userId, int page = 1, int pageSize = 10);
    Task<Review> CreateAsync(Review review);
    Task<Review> UpdateAsync(Review review);
    Task DeleteAsync(int id);
    Task<bool> ExistsAsync(int productId, string userId);
    Task<double> GetAverageRatingAsync(int productId);
    Task<int> GetTotalReviewsCountAsync(int productId);
    Task<Dictionary<int, int>> GetRatingDistributionAsync(int productId);
    Task<bool> MarkHelpfulAsync(int reviewId, string userId);
    Task<bool> UnmarkHelpfulAsync(int reviewId, string userId);
    Task<bool> HasUserMarkedHelpfulAsync(int reviewId, string userId);
    
    // Search and Filter operations
    Task<IEnumerable<Review>> SearchReviewsAsync(string searchTerm, int? productId = null, int? minRating = null, int? maxRating = null, bool? isVerifiedOnly = null, int page = 1, int pageSize = 10);
    Task<int> CountSearchResultsAsync(string searchTerm, int? productId = null, int? minRating = null, int? maxRating = null, bool? isVerifiedOnly = null);
    
    // Moderation operations
    Task<IEnumerable<Review>> GetReviewsByStatusAsync(ReviewStatus status, int page = 1, int pageSize = 10);
    Task<int> CountReviewsByStatusAsync(ReviewStatus status);
    Task<Review> UpdateReviewStatusAsync(int id, ReviewStatus status);
    
    // Reply operations
    Task<Reply?> GetReplyByIdAsync(int id);
    Task<IEnumerable<Reply>> GetRepliesByReviewIdAsync(int reviewId);
    Task<Reply> CreateReplyAsync(Reply reply);
    Task<Reply> UpdateReplyAsync(Reply reply);
    Task DeleteReplyAsync(int id);
    
    // Report operations
    Task<ReviewReport?> GetReportByIdAsync(int id);
    Task<IEnumerable<ReviewReport>> GetReportsByReviewIdAsync(int reviewId);
    Task<IEnumerable<ReviewReport>> GetReportsByStatusAsync(ReportStatus status, int page = 1, int pageSize = 10);
    Task<int> CountReportsByStatusAsync(ReportStatus status);
    Task<ReviewReport> CreateReportAsync(ReviewReport report);
    Task<ReviewReport> UpdateReportAsync(ReviewReport report);
    Task<bool> HasUserReportedReviewAsync(int reviewId, string userId);
    Task<IEnumerable<Review>> GetMostReportedReviewsAsync(int count = 10);

    // Seller-specific operations
    Task<IEnumerable<Review>> GetReviewsBySellerIdAsync(int sellerId, int page = 1, int pageSize = 10);
    Task<int> GetTotalReviewsCountBySellerIdAsync(int sellerId);
    Task<double> GetAverageRatingBySellerIdAsync(int sellerId);
    Task<Dictionary<int, int>> GetRatingDistributionBySellerIdAsync(int sellerId);
    Task<int> GetRecentReviewsCountBySellerIdAsync(int sellerId, int days = 30);
    Task<int> GetPendingReviewsCountBySellerIdAsync(int sellerId);

    // Admin-specific operations
    Task<int> GetTotalReviewsCountAsync();
    Task<int> GetPendingReviewsCountAsync();
    Task<int> GetApprovedReviewsCountAsync();
    Task<int> GetRejectedReviewsCountAsync();
    Task<int> GetReportedReviewsCountAsync();
    Task<double> GetOverallAverageRatingAsync();
    Task<Dictionary<int, int>> GetOverallRatingDistributionAsync();
    Task<Dictionary<string, int>> GetReviewsByPeriodAsync();
    Task<IEnumerable<Review>> GetAdminReviewsAsync(string? status, int? rating, bool? isVerifiedPurchase, string? dateFilter, string? searchTerm, int page, int pageSize);
    Task<int> CountAdminReviewsAsync(string? status, int? rating, bool? isVerifiedPurchase, string? dateFilter, string? searchTerm);
}
