@page "/delivery/tracking"
@using NafaPlace.AdminPortal.Models.Delivery
@using NafaPlace.AdminPortal.Services
@inject IDeliveryService DeliveryService
@inject IJSRuntime JSRuntime
@inject ILogger<DeliveryTracking> Logger

<PageTitle>Suivi des Livraisons</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-route text-primary me-2"></i>
                    Suivi des Livraisons
                </h2>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" @onclick="RefreshOrders">
                        <i class="fas fa-sync-alt me-2"></i>Actualiser
                    </button>
                </div>
            </div>

            <!-- Recherche par numéro de suivi -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-end">
                        <div class="col-md-8">
                            <label class="form-label">Rechercher par numéro de suivi</label>
                            <input type="text" class="form-control" @bind="trackingNumber" @onkeypress="OnTrackingSearchKeyPress" placeholder="Entrez le numéro de suivi...">
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-primary w-100" @onclick="SearchByTracking" disabled="@isSearching">
                                @if (isSearching)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                }
                                <i class="fas fa-search me-2"></i>Rechercher
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else
            {
                <!-- Résultats de recherche par tracking -->
                @if (trackingResults.Any())
                {
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-search me-2"></i>
                                Résultats de recherche pour: <code>@searchedTrackingNumber</code>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                @foreach (var tracking in trackingResults.OrderByDescending(t => t.EventDate))
                                {
                                    <div class="timeline-item">
                                        <div class="timeline-marker @GetStatusMarkerClass(tracking.Status)">
                                            <i class="fas @GetStatusIcon(tracking.Status)"></i>
                                        </div>
                                        <div class="timeline-content">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">@tracking.Description</h6>
                                                    <p class="text-muted mb-1">@tracking.EventDate.ToString("dd/MM/yyyy HH:mm")</p>
                                                    @if (!string.IsNullOrEmpty(tracking.Location))
                                                    {
                                                        <p class="text-muted mb-1">
                                                            <i class="fas fa-map-marker-alt me-1"></i>@tracking.Location
                                                        </p>
                                                    }
                                                    @if (!string.IsNullOrEmpty(tracking.Notes))
                                                    {
                                                        <p class="text-muted mb-0">@tracking.Notes</p>
                                                    }
                                                </div>
                                                <span class="badge @GetStatusBadgeClass(tracking.Status)">
                                                    @GetStatusText(tracking.Status)
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }

                <!-- Liste des commandes de livraison -->
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="card-title mb-0">Commandes de Livraison (@deliveryOrders.Count)</h5>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Rechercher une commande..." @bind="searchTerm" @oninput="FilterOrders">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        @if (filteredOrders.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Commande</th>
                                            <th>Client</th>
                                            <th>Transporteur</th>
                                            <th>Zone</th>
                                            <th>Statut</th>
                                            <th>Date Prévue</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var order in filteredOrders.Take(50))
                                        {
                                            <tr>
                                                <td>
                                                    <div>
                                                        <strong>@order.OrderId</strong>
                                                        <br><small class="text-muted">@order.TrackingNumber</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong>@order.CustomerName</strong>
                                                        <br><small class="text-muted">@order.CustomerPhone</small>
                                                    </div>
                                                </td>
                                                <td>@order.CarrierName</td>
                                                <td>@order.ZoneName</td>
                                                <td>
                                                    <span class="badge @GetStatusBadgeClass(order.Status)">
                                                        @GetStatusText(order.Status)
                                                    </span>
                                                </td>
                                                <td>
                                                    @if (order.EstimatedDeliveryDate.HasValue)
                                                    {
                                                        <span>@order.EstimatedDeliveryDate.Value.ToString("dd/MM/yyyy")</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">Non définie</span>
                                                    }
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" @onclick="() => ViewOrderDetails(order)" title="Détails">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success" @onclick="() => UpdateOrderStatus(order)" title="Mettre à jour">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-info" @onclick="() => TrackOrder(order)" title="Suivre">
                                                            <i class="fas fa-route"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            @if (filteredOrders.Count > 50)
                            {
                                <div class="card-footer text-center">
                                    <small class="text-muted">Affichage des 50 premiers résultats sur @filteredOrders.Count</small>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-shipping-fast fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Aucune commande trouvée</h5>
                                <p class="text-muted">
                                    @if (string.IsNullOrEmpty(searchTerm))
                                    {
                                        <span>Aucune commande de livraison disponible.</span>
                                    }
                                    else
                                    {
                                        <span>Aucune commande ne correspond à votre recherche.</span>
                                    }
                                </p>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Modal Mise à jour du statut -->
@if (showStatusModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Mettre à jour le statut</h5>
                    <button type="button" class="btn-close" @onclick="CloseStatusModal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Commande</label>
                        <input type="text" class="form-control" value="@selectedOrder?.OrderId (@selectedOrder?.TrackingNumber)" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Nouveau statut</label>
                        <select class="form-select" @bind="newStatus">
                            <option value="@DeliveryStatus.Pending">En attente</option>
                            <option value="@DeliveryStatus.Confirmed">Confirmée</option>
                            <option value="@DeliveryStatus.PickedUp">Récupérée</option>
                            <option value="@DeliveryStatus.InTransit">En transit</option>
                            <option value="@DeliveryStatus.OutForDelivery">En cours de livraison</option>
                            <option value="@DeliveryStatus.Delivered">Livrée</option>
                            <option value="@DeliveryStatus.Failed">Échec de livraison</option>
                            <option value="@DeliveryStatus.Returned">Retournée</option>
                            <option value="@DeliveryStatus.Cancelled">Annulée</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" @bind="statusDescription" rows="3" placeholder="Description de la mise à jour..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Localisation (optionnel)</label>
                        <input type="text" class="form-control" @bind="statusLocation" placeholder="Ex: Centre de tri Conakry">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CloseStatusModal">Annuler</button>
                    <button type="button" class="btn btn-primary" @onclick="SaveStatusUpdate" disabled="@isUpdatingStatus">
                        @if (isUpdatingStatus)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        Mettre à jour
                    </button>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        z-index: 1;
    }

    .timeline-marker.status-pending { background-color: #6c757d; }
    .timeline-marker.status-confirmed { background-color: #0d6efd; }
    .timeline-marker.status-pickedup { background-color: #fd7e14; }
    .timeline-marker.status-intransit { background-color: #6f42c1; }
    .timeline-marker.status-outfordelivery { background-color: #0dcaf0; }
    .timeline-marker.status-delivered { background-color: #198754; }
    .timeline-marker.status-failed { background-color: #dc3545; }
    .timeline-marker.status-returned { background-color: #ffc107; }
    .timeline-marker.status-cancelled { background-color: #6c757d; }

    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #dee2e6;
    }
</style>

@code {
    private List<DeliveryOrderDto> deliveryOrders = new();
    private List<DeliveryOrderDto> filteredOrders = new();
    private List<DeliveryTrackingDto> trackingResults = new();
    private bool isLoading = true;
    private bool isSearching = false;
    private bool isUpdatingStatus = false;
    private string searchTerm = string.Empty;
    private string trackingNumber = string.Empty;
    private string searchedTrackingNumber = string.Empty;

    // Status update modal
    private bool showStatusModal = false;
    private DeliveryOrderDto? selectedOrder;
    private DeliveryStatus newStatus = DeliveryStatus.Pending;
    private string statusDescription = string.Empty;
    private string statusLocation = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadDeliveryOrders();
    }

    private async Task LoadDeliveryOrders()
    {
        try
        {
            isLoading = true;
            deliveryOrders = await DeliveryService.GetDeliveryOrdersAsync();
            filteredOrders = deliveryOrders;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading delivery orders");
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors du chargement des commandes");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task RefreshOrders()
    {
        await LoadDeliveryOrders();
        await JSRuntime.InvokeVoidAsync("alert", "Liste actualisée");
    }

    private void FilterOrders(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;

        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredOrders = deliveryOrders;
        }
        else
        {
            filteredOrders = deliveryOrders.Where(o =>
                o.OrderId.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.TrackingNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.CustomerPhone.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.CarrierName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.ZoneName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
            ).ToList();
        }
    }

    private async Task OnTrackingSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchByTracking();
        }
    }

    private async Task SearchByTracking()
    {
        if (string.IsNullOrWhiteSpace(trackingNumber))
        {
            await JSRuntime.InvokeVoidAsync("alert", "Veuillez entrer un numéro de suivi");
            return;
        }

        try
        {
            isSearching = true;
            searchedTrackingNumber = trackingNumber;
            trackingResults = await DeliveryService.GetDeliveryTrackingAsync(trackingNumber);

            if (!trackingResults.Any())
            {
                await JSRuntime.InvokeVoidAsync("alert", "Aucun résultat trouvé pour ce numéro de suivi");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error searching tracking {TrackingNumber}", trackingNumber);
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la recherche");
        }
        finally
        {
            isSearching = false;
        }
    }

    private void ViewOrderDetails(DeliveryOrderDto order)
    {
        // TODO: Navigate to order details page
        JSRuntime.InvokeVoidAsync("alert", $"Détails de la commande: {order.OrderId}");
    }

    private void UpdateOrderStatus(DeliveryOrderDto order)
    {
        selectedOrder = order;
        newStatus = order.Status;
        statusDescription = string.Empty;
        statusLocation = string.Empty;
        showStatusModal = true;
    }

    private async Task TrackOrder(DeliveryOrderDto order)
    {
        trackingNumber = order.TrackingNumber;
        await SearchByTracking();
    }

    private async Task SaveStatusUpdate()
    {
        if (selectedOrder == null || string.IsNullOrWhiteSpace(statusDescription))
        {
            await JSRuntime.InvokeVoidAsync("alert", "Veuillez remplir tous les champs requis");
            return;
        }

        try
        {
            isUpdatingStatus = true;
            var success = await DeliveryService.UpdateDeliveryStatusAsync(
                selectedOrder.Id,
                newStatus,
                statusDescription,
                statusLocation
            );

            if (success)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Statut mis à jour avec succès");
                CloseStatusModal();
                await LoadDeliveryOrders();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la mise à jour du statut");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error updating delivery status for order {OrderId}", selectedOrder.Id);
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la mise à jour du statut");
        }
        finally
        {
            isUpdatingStatus = false;
        }
    }

    private void CloseStatusModal()
    {
        showStatusModal = false;
        selectedOrder = null;
        statusDescription = string.Empty;
        statusLocation = string.Empty;
    }

    private string GetStatusText(DeliveryStatus status)
    {
        return status switch
        {
            DeliveryStatus.Pending => "En attente",
            DeliveryStatus.Confirmed => "Confirmée",
            DeliveryStatus.PickedUp => "Récupérée",
            DeliveryStatus.InTransit => "En transit",
            DeliveryStatus.OutForDelivery => "En cours de livraison",
            DeliveryStatus.Delivered => "Livrée",
            DeliveryStatus.Failed => "Échec",
            DeliveryStatus.Returned => "Retournée",
            DeliveryStatus.Cancelled => "Annulée",
            DeliveryStatus.Lost => "Perdue",
            DeliveryStatus.Damaged => "Endommagée",
            _ => "Inconnu"
        };
    }

    private string GetStatusBadgeClass(DeliveryStatus status)
    {
        return status switch
        {
            DeliveryStatus.Pending => "bg-secondary",
            DeliveryStatus.Confirmed => "bg-primary",
            DeliveryStatus.PickedUp => "bg-warning",
            DeliveryStatus.InTransit => "bg-info",
            DeliveryStatus.OutForDelivery => "bg-primary",
            DeliveryStatus.Delivered => "bg-success",
            DeliveryStatus.Failed => "bg-danger",
            DeliveryStatus.Returned => "bg-warning",
            DeliveryStatus.Cancelled => "bg-secondary",
            DeliveryStatus.Lost => "bg-danger",
            DeliveryStatus.Damaged => "bg-danger",
            _ => "bg-light"
        };
    }

    private string GetStatusMarkerClass(DeliveryStatus status)
    {
        return status switch
        {
            DeliveryStatus.Pending => "status-pending",
            DeliveryStatus.Confirmed => "status-confirmed",
            DeliveryStatus.PickedUp => "status-pickedup",
            DeliveryStatus.InTransit => "status-intransit",
            DeliveryStatus.OutForDelivery => "status-outfordelivery",
            DeliveryStatus.Delivered => "status-delivered",
            DeliveryStatus.Failed => "status-failed",
            DeliveryStatus.Returned => "status-returned",
            DeliveryStatus.Cancelled => "status-cancelled",
            _ => "status-pending"
        };
    }

    private string GetStatusIcon(DeliveryStatus status)
    {
        return status switch
        {
            DeliveryStatus.Pending => "fa-clock",
            DeliveryStatus.Confirmed => "fa-check",
            DeliveryStatus.PickedUp => "fa-hand-paper",
            DeliveryStatus.InTransit => "fa-truck",
            DeliveryStatus.OutForDelivery => "fa-shipping-fast",
            DeliveryStatus.Delivered => "fa-check-circle",
            DeliveryStatus.Failed => "fa-times",
            DeliveryStatus.Returned => "fa-undo",
            DeliveryStatus.Cancelled => "fa-ban",
            DeliveryStatus.Lost => "fa-question",
            DeliveryStatus.Damaged => "fa-exclamation-triangle",
            _ => "fa-circle"
        };
    }
}
