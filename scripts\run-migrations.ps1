# Script d'exécution des migrations NafaPlace
# Auteur: Assistant IA
# Date: 2025-01-28

param(
    [switch]$Force,
    [string]$Service = "all"
)

Write-Host "🗄️ Exécution des migrations NafaPlace" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Configuration des services et leurs bases de données
$services = @{
    "Catalog" = @{
        "Path" = "src/Services/Catalog/NafaPlace.Catalog.Infrastructure"
        "Database" = "nafaplace_catalog"
        "Context" = "CatalogDbContext"
    }
    "Order" = @{
        "Path" = "src/Services/Order/NafaPlace.Order.Infrastructure"
        "Database" = "nafaplace_order"
        "Context" = "OrderDbContext"
    }
    "Notification" = @{
        "Path" = "src/Services/Notification/NafaPlace.Notification.Infrastructure"
        "Database" = "nafaplace_notification"
        "Context" = "NotificationDbContext"
    }
    "Analytics" = @{
        "Path" = "src/Services/Analytics/NafaPlace.Analytics.Infrastructure"
        "Database" = "nafaplace_analytics"
        "Context" = "AnalyticsDbContext"
    }
    "Chat" = @{
        "Path" = "src/Services/Chat/NafaPlace.Chat.Infrastructure"
        "Database" = "nafaplace_chat"
        "Context" = "ChatDbContext"
    }
    "Recommendation" = @{
        "Path" = "src/Services/Recommendation/NafaPlace.Recommendation.Infrastructure"
        "Database" = "nafaplace_recommendation"
        "Context" = "RecommendationDbContext"
    }
    "Loyalty" = @{
        "Path" = "src/Services/Loyalty/NafaPlace.Loyalty.Infrastructure"
        "Database" = "nafaplace_loyalty"
        "Context" = "LoyaltyDbContext"
    }
    "Localization" = @{
        "Path" = "src/Services/Localization/NafaPlace.Localization.Infrastructure"
        "Database" = "nafaplace_localization"
        "Context" = "LocalizationDbContext"
    }
}

# Configuration de la base de données
$connectionString = "Host=localhost;Port=5432;Database={0};Username=nafaplace;Password=************"

# Fonction pour vérifier si PostgreSQL est accessible
function Test-PostgreSQLConnection {
    Write-Host "🔍 Vérification de la connexion PostgreSQL..." -ForegroundColor Yellow
    
    try {
        # Test avec docker si le conteneur existe
        $containerExists = docker ps --filter "name=nafaplace-postgres" --format "{{.Names}}" | Select-String "nafaplace-postgres"
        
        if ($containerExists) {
            $result = docker exec nafaplace-postgres pg_isready -U nafaplace
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ PostgreSQL est accessible via Docker" -ForegroundColor Green
                return $true
            }
        }
        
        # Test de connexion directe
        $testConnection = "Host=localhost;Port=5432;Database=postgres;Username=nafaplace;Password=************"
        # Ici on pourrait utiliser une bibliothèque .NET pour tester la connexion
        Write-Host "⚠️ Test de connexion PostgreSQL - Assurez-vous que PostgreSQL est démarré" -ForegroundColor Yellow
        return $true
    }
    catch {
        Write-Host "❌ PostgreSQL n'est pas accessible : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour créer une base de données si elle n'existe pas
function New-DatabaseIfNotExists {
    param($databaseName)
    
    Write-Host "📊 Vérification/Création de la base de données : $databaseName" -ForegroundColor Cyan
    
    try {
        # Utiliser docker exec pour créer la base de données
        $createDbCommand = "CREATE DATABASE $databaseName;"
        $result = docker exec nafaplace-postgres psql -U nafaplace -d postgres -c "$createDbCommand" 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Base de données $databaseName créée" -ForegroundColor Green
        } else {
            Write-Host "ℹ️ Base de données $databaseName existe déjà" -ForegroundColor Blue
        }
        return $true
    }
    catch {
        Write-Host "⚠️ Erreur lors de la création de la base $databaseName : $($_.Exception.Message)" -ForegroundColor Yellow
        return $true # Continue même en cas d'erreur (la base existe peut-être déjà)
    }
}

# Fonction pour exécuter les migrations d'un service
function Invoke-ServiceMigration {
    param($serviceName, $serviceConfig)
    
    Write-Host "`n🔧 Migration du service : $serviceName" -ForegroundColor Magenta
    Write-Host "----------------------------------------" -ForegroundColor Magenta
    
    $projectPath = $serviceConfig.Path
    $databaseName = $serviceConfig.Database
    $contextName = $serviceConfig.Context
    
    # Vérifier si le projet existe
    if (-not (Test-Path $projectPath)) {
        Write-Host "❌ Projet non trouvé : $projectPath" -ForegroundColor Red
        return $false
    }
    
    # Créer la base de données si nécessaire
    $dbCreated = New-DatabaseIfNotExists $databaseName
    if (-not $dbCreated) {
        Write-Host "❌ Impossible de créer la base de données $databaseName" -ForegroundColor Red
        return $false
    }
    
    try {
        # Se déplacer dans le répertoire du projet
        Push-Location $projectPath
        
        # Construire la chaîne de connexion
        $connStr = $connectionString -f $databaseName
        
        Write-Host "📦 Installation des outils EF Core..." -ForegroundColor Yellow
        dotnet tool install --global dotnet-ef --version 8.0.0 2>$null
        
        Write-Host "🔨 Vérification des migrations..." -ForegroundColor Yellow
        
        # Lister les migrations existantes
        $migrations = dotnet ef migrations list --no-build 2>$null
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "⚠️ Aucune migration trouvée, création de la migration initiale..." -ForegroundColor Yellow
            
            # Créer la migration initiale
            $migrationName = "Initial$($serviceName)Create"
            dotnet ef migrations add $migrationName --no-build
            
            if ($LASTEXITCODE -ne 0) {
                Write-Host "❌ Erreur lors de la création de la migration" -ForegroundColor Red
                Pop-Location
                return $false
            }
        }
        
        Write-Host "🚀 Application des migrations..." -ForegroundColor Yellow
        
        # Appliquer les migrations
        $env:ConnectionStrings__DefaultConnection = $connStr
        dotnet ef database update --no-build --connection $connStr
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Migrations appliquées avec succès pour $serviceName" -ForegroundColor Green
            Pop-Location
            return $true
        } else {
            Write-Host "❌ Erreur lors de l'application des migrations pour $serviceName" -ForegroundColor Red
            Pop-Location
            return $false
        }
    }
    catch {
        Write-Host "❌ Exception lors de la migration de $serviceName : $($_.Exception.Message)" -ForegroundColor Red
        Pop-Location
        return $false
    }
}

# Fonction pour exécuter toutes les migrations
function Invoke-AllMigrations {
    Write-Host "🚀 Exécution de toutes les migrations..." -ForegroundColor Green
    
    $successCount = 0
    $totalCount = $services.Count
    
    foreach ($service in $services.GetEnumerator()) {
        $success = Invoke-ServiceMigration $service.Key $service.Value
        if ($success) {
            $successCount++
        }
        
        # Pause entre les migrations
        Start-Sleep -Seconds 2
    }
    
    Write-Host "`n📊 RÉSUMÉ DES MIGRATIONS" -ForegroundColor Magenta
    Write-Host "========================" -ForegroundColor Magenta
    Write-Host "✅ Réussies : $successCount/$totalCount" -ForegroundColor Green
    
    $successRate = [math]::Round(($successCount / $totalCount) * 100, 2)
    if ($successRate -eq 100) {
        Write-Host "🎉 Toutes les migrations ont réussi !" -ForegroundColor Green
    } elseif ($successRate -ge 80) {
        Write-Host "⚠️ La plupart des migrations ont réussi ($successRate%)" -ForegroundColor Yellow
    } else {
        Write-Host "🚨 Plusieurs migrations ont échoué ($successRate%)" -ForegroundColor Red
    }
}

# Fonction pour exécuter la migration d'un service spécifique
function Invoke-SpecificMigration {
    param($serviceName)
    
    if ($services.ContainsKey($serviceName)) {
        $success = Invoke-ServiceMigration $serviceName $services[$serviceName]
        if ($success) {
            Write-Host "🎉 Migration de $serviceName terminée avec succès !" -ForegroundColor Green
        } else {
            Write-Host "❌ Échec de la migration de $serviceName" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Service '$serviceName' non reconnu" -ForegroundColor Red
        Write-Host "Services disponibles : $($services.Keys -join ', ')" -ForegroundColor Yellow
    }
}

# Fonction pour afficher l'aide
function Show-Help {
    Write-Host "📖 Utilisation du script de migrations" -ForegroundColor Cyan
    Write-Host "======================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Syntaxe :" -ForegroundColor Yellow
    Write-Host "  .\run-migrations.ps1 [-Service <nom>] [-Force]" -ForegroundColor White
    Write-Host ""
    Write-Host "Paramètres :" -ForegroundColor Yellow
    Write-Host "  -Service <nom>  : Migrer un service spécifique" -ForegroundColor White
    Write-Host "  -Force          : Forcer la migration même en cas d'erreurs" -ForegroundColor White
    Write-Host ""
    Write-Host "Services disponibles :" -ForegroundColor Yellow
    foreach ($service in $services.Keys) {
        Write-Host "  • $service" -ForegroundColor White
    }
    Write-Host ""
    Write-Host "Exemples :" -ForegroundColor Yellow
    Write-Host "  .\run-migrations.ps1                    # Migrer tous les services" -ForegroundColor White
    Write-Host "  .\run-migrations.ps1 -Service Analytics # Migrer seulement Analytics" -ForegroundColor White
    Write-Host "  .\run-migrations.ps1 -Force             # Forcer toutes les migrations" -ForegroundColor White
}

# Fonction principale
function Start-Migrations {
    # Vérifier la connexion PostgreSQL
    $pgConnected = Test-PostgreSQLConnection
    if (-not $pgConnected -and -not $Force) {
        Write-Host "❌ PostgreSQL n'est pas accessible. Utilisez -Force pour continuer." -ForegroundColor Red
        return
    }
    
    if ($Service -eq "all") {
        Invoke-AllMigrations
    } elseif ($Service -eq "help") {
        Show-Help
    } else {
        Invoke-SpecificMigration $Service
    }
}

# Vérifier les paramètres
if ($args -contains "-help" -or $args -contains "--help" -or $args -contains "/?") {
    Show-Help
    return
}

# Exécuter les migrations
Start-Migrations

Write-Host "`n🏁 Script de migrations terminé !" -ForegroundColor Green
