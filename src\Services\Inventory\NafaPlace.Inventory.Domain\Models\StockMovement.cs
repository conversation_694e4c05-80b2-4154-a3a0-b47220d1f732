using System.ComponentModel.DataAnnotations;
using NafaPlace.Inventory.Domain.Common;
using NafaPlace.Inventory.Domain.Enums;

namespace NafaPlace.Inventory.Domain.Models;

public class StockMovement : BaseEntity
{
    [Required]
    public int ProductId { get; set; }

    [Required]
    [MaxLength(100)]
    public required string ProductName { get; set; }

    [Required]
    public MovementType Type { get; set; }

    [Required]
    public int Quantity { get; set; }

    [Required]
    public int PreviousStock { get; set; }

    [Required]
    public int NewStock { get; set; }

    [Required]
    [MaxLength(200)]
    public required string Reason { get; set; }

    [MaxLength(50)]
    public string? Reference { get; set; } // Order ID, Adjustment ID, etc.

    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [MaxLength(100)]
    public string? UserName { get; set; }

    [Required]
    public int SellerId { get; set; }

    [MaxLength(500)]
    public string? Notes { get; set; }
}
