@page "/delivery/calculator"
@using NafaPlace.AdminPortal.Models.Delivery
@using NafaPlace.AdminPortal.Services
@using System.ComponentModel.DataAnnotations
@inject IDeliveryService DeliveryService
@inject IJSRuntime JSRuntime
@inject ILogger<DeliveryCalculator> Logger

<PageTitle>Calculateur de Frais de Livraison</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-calculator text-primary me-2"></i>
                    Calculateur de Frais de Livraison
                </h2>
            </div>

            <div class="row">
                <!-- Formulaire de calcul -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>
                                Paramètres de Livraison
                            </h5>
                        </div>
                        <div class="card-body">
                            <EditForm Model="calculationRequest" OnValidSubmit="CalculateDeliveryFees">
                                <DataAnnotationsValidator />
                                
                                <div class="mb-3">
                                    <label class="form-label">Adresse de livraison *</label>
                                    <InputTextArea class="form-control" @bind-Value="calculationRequest.Address" rows="3" placeholder="Entrez l'adresse complète de livraison..." />
                                    <ValidationMessage For="@(() => calculationRequest.Address)" />
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Montant de la commande (GNF) *</label>
                                            <InputNumber class="form-control" @bind-Value="calculationRequest.OrderAmount" />
                                            <ValidationMessage For="@(() => calculationRequest.OrderAmount)" />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Poids (kg)</label>
                                            <InputNumber class="form-control" @bind-Value="calculationRequest.Weight" step="0.1" />
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Volume (cm³)</label>
                                            <InputNumber class="form-control" @bind-Value="calculationRequest.Volume" />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Type de livraison</label>
                                            <InputSelect class="form-select" @bind-Value="calculationRequest.DeliveryType">
                                                <option value="@DeliveryType.Standard">Standard</option>
                                                <option value="@DeliveryType.Express">Express</option>
                                                <option value="@DeliveryType.SameDay">Jour même</option>
                                                <option value="@DeliveryType.Scheduled">Programmée</option>
                                            </InputSelect>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Latitude (optionnel)</label>
                                            <InputNumber class="form-control" @bind-Value="calculationRequest.Latitude" step="0.000001" />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Longitude (optionnel)</label>
                                            <InputNumber class="form-control" @bind-Value="calculationRequest.Longitude" step="0.000001" />
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary" disabled="@isCalculating">
                                        @if (isCalculating)
                                        {
                                            <span class="spinner-border spinner-border-sm me-2"></span>
                                        }
                                        <i class="fas fa-calculator me-2"></i>Calculer les Frais
                                    </button>
                                </div>
                            </EditForm>
                        </div>
                    </div>
                </div>

                <!-- Résultats -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Devis de Livraison
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (isCalculating)
                            {
                                <div class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Calcul en cours...</span>
                                    </div>
                                    <p class="mt-2 text-muted">Calcul des frais de livraison...</p>
                                </div>
                            }
                            else if (deliveryQuotes.Any())
                            {
                                <div class="mb-3">
                                    <small class="text-muted">@deliveryQuotes.Count option(s) de livraison disponible(s)</small>
                                </div>
                                
                                @foreach (var quote in deliveryQuotes.Take(5))
                                {
                                    <div class="card mb-3 @(quote == deliveryQuotes.First() ? "border-success" : "")">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="card-title mb-1">
                                                        @quote.CarrierName
                                                        @if (quote == deliveryQuotes.First())
                                                        {
                                                            <span class="badge bg-success ms-2">Recommandé</span>
                                                        }
                                                    </h6>
                                                    <p class="text-muted mb-2">Zone: @quote.ZoneName</p>
                                                    <div class="row">
                                                        <div class="col-6">
                                                            <small class="text-muted">Délai estimé:</small>
                                                            <br><strong>@quote.EstimatedDeliveryDays jour(s)</strong>
                                                        </div>
                                                        <div class="col-6">
                                                            <small class="text-muted">Date prévue:</small>
                                                            <br><strong>@quote.EstimatedDeliveryDate.ToString("dd/MM/yyyy")</strong>
                                                        </div>
                                                    </div>
                                                    
                                                    @if (quote.SameDayAvailable || quote.ExpressAvailable)
                                                    {
                                                        <div class="mt-2">
                                                            <small class="text-muted">Options disponibles:</small>
                                                            <div>
                                                                @if (quote.SameDayAvailable && quote.SameDayFee.HasValue)
                                                                {
                                                                    <span class="badge bg-warning text-dark me-1">
                                                                        Jour même (+@quote.SameDayFee.Value.ToString("N0") GNF)
                                                                    </span>
                                                                }
                                                                @if (quote.ExpressAvailable && quote.ExpressFee.HasValue)
                                                                {
                                                                    <span class="badge bg-info me-1">
                                                                        Express (+@quote.ExpressFee.Value.ToString("N0") GNF)
                                                                    </span>
                                                                }
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                                <div class="text-end">
                                                    <div class="h4 mb-0 text-primary">
                                                        @if (quote.DeliveryFee == 0)
                                                        {
                                                            <span class="text-success">GRATUIT</span>
                                                        }
                                                        else
                                                        {
                                                            <span>@quote.DeliveryFee.ToString("N0") GNF</span>
                                                        }
                                                    </div>
                                                    @if (quote.InsuranceFee.HasValue && quote.InsuranceFee.Value > 0)
                                                    {
                                                        <small class="text-muted">+ @quote.InsuranceFee.Value.ToString("N0") GNF assurance</small>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                                
                                @if (deliveryQuotes.Count > 5)
                                {
                                    <div class="text-center">
                                        <small class="text-muted">Et @(deliveryQuotes.Count - 5) autre(s) option(s)...</small>
                                    </div>
                                }
                            }
                            else if (hasCalculated)
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                    <h5 class="text-muted">Aucune option de livraison</h5>
                                    <p class="text-muted">Aucun transporteur ne dessert cette zone ou les paramètres ne permettent pas la livraison.</p>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-calculator fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Prêt pour le calcul</h5>
                                    <p class="text-muted">Remplissez le formulaire et cliquez sur "Calculer les Frais" pour obtenir les devis de livraison.</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private DeliveryCalculationRequest calculationRequest = new();
    private List<DeliveryQuoteDto> deliveryQuotes = new();
    private bool isCalculating = false;
    private bool hasCalculated = false;

    public class DeliveryCalculationRequest
    {
        [Required(ErrorMessage = "L'adresse est requise")]
        public string Address { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Le montant de la commande est requis")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Le montant doit être positif")]
        public decimal OrderAmount { get; set; }
        
        public decimal? Weight { get; set; }
        public decimal? Volume { get; set; }
        public DeliveryType DeliveryType { get; set; } = DeliveryType.Standard;
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
    }

    private async Task CalculateDeliveryFees()
    {
        try
        {
            isCalculating = true;
            hasCalculated = false;
            deliveryQuotes.Clear();

            // Simulate API call to get delivery quotes
            await Task.Delay(1500); // Simulate network delay
            
            // For now, we'll create mock data since the API integration is not complete
            deliveryQuotes = GenerateMockQuotes();
            
            hasCalculated = true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error calculating delivery fees");
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors du calcul des frais de livraison");
        }
        finally
        {
            isCalculating = false;
        }
    }

    private List<DeliveryQuoteDto> GenerateMockQuotes()
    {
        var quotes = new List<DeliveryQuoteDto>();

        // Base fee calculation
        decimal baseFee = calculationRequest.OrderAmount >= 500000 ? 0 : 15000;

        // Weight surcharge
        if (calculationRequest.Weight.HasValue && calculationRequest.Weight.Value > 5)
        {
            baseFee += (calculationRequest.Weight.Value - 5) * 2000;
        }

        // Volume surcharge
        if (calculationRequest.Volume.HasValue && calculationRequest.Volume.Value > 50000)
        {
            baseFee += ((calculationRequest.Volume.Value - 50000) / 10000) * 1000;
        }

        // Generate quotes for different carriers
        var carriers = new[]
        {
            new { Name = "NafaPlace Express", Zone = "Conakry Centre", Days = 1, Multiplier = 1.0m },
            new { Name = "Guinée Livraison", Zone = "Conakry Centre", Days = 2, Multiplier = 0.9m },
            new { Name = "Rapid Delivery GN", Zone = "Conakry Centre", Days = 1, Multiplier = 1.1m },
            new { Name = "Transport Universel", Zone = "Conakry Périphérie", Days = 3, Multiplier = 1.3m }
        };

        var carrierId = 1;
        foreach (var carrier in carriers)
        {
            var fee = Math.Round(baseFee * carrier.Multiplier, 0);

            quotes.Add(new DeliveryQuoteDto
            {
                CarrierId = carrierId++,
                CarrierName = carrier.Name,
                ZoneId = 1,
                ZoneName = carrier.Zone,
                DeliveryFee = fee,
                TotalFee = fee,
                Currency = "GNF",
                EstimatedDeliveryDays = carrier.Days,
                MaxDeliveryDays = carrier.Days + 2,
                EstimatedDeliveryDate = DateTime.UtcNow.AddDays(carrier.Days),
                IsAvailable = true,
                SameDayAvailable = carrier.Days == 1,
                SameDayFee = carrier.Days == 1 ? fee + 25000 : null,
                ExpressAvailable = true,
                ExpressFee = fee + 15000
            });
        }

        return quotes.OrderBy(q => q.TotalFee).ToList();
    }
}
