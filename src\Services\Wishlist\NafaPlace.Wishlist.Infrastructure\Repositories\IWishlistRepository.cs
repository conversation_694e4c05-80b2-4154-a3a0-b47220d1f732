using NafaPlace.Wishlist.Domain.Models;

namespace NafaPlace.Wishlist.Infrastructure.Repositories;

public interface IWishlistRepository
{
    // Wishlist operations
    Task<Domain.Models.Wishlist?> GetWishlistByIdAsync(int id);
    Task<Domain.Models.Wishlist?> GetUserWishlistAsync(string userId);
    Task<List<Domain.Models.Wishlist>> GetUserWishlistsAsync(string userId);
    Task<Domain.Models.Wishlist> CreateWishlistAsync(Domain.Models.Wishlist wishlist);
    Task<Domain.Models.Wishlist> UpdateWishlistAsync(Domain.Models.Wishlist wishlist);
    Task<bool> DeleteWishlistAsync(int id);

    // Wishlist items operations
    Task<WishlistItem?> GetWishlistItemAsync(string userId, int productId);
    Task<List<WishlistItem>> GetWishlistItemsAsync(string userId, int page = 1, int pageSize = 20);
    Task<WishlistItem> AddItemToWishlistAsync(int wishlistId, WishlistItem item);
    Task<bool> RemoveItemFromWishlistAsync(string userId, int productId);
    Task<bool> ClearWishlistAsync(string userId);
    Task<int> GetWishlistItemCountAsync(string userId);
    Task<List<WishlistItem>> GetRecentlyAddedItemsAsync(string userId, int count = 5);
    Task<bool> UpdateProductAvailabilityAsync(int productId, bool isAvailable);
}
