using System.ComponentModel.DataAnnotations;
using NafaPlace.Inventory.Domain.Common;
using NafaPlace.Inventory.Domain.Enums;

namespace NafaPlace.Inventory.Domain.Models;

public class StockAlertNotification : BaseEntity
{
    [Required]
    public int AlertId { get; set; }

    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [Required]
    public NotificationChannel Channel { get; set; }

    [Required]
    [MaxLength(100)]
    public required string Recipient { get; set; } // Email, phone, etc.

    [Required]
    public NotificationStatus Status { get; set; } = NotificationStatus.Pending;

    public DateTime? SentAt { get; set; }

    [MaxLength(500)]
    public string? ErrorMessage { get; set; }

    public int RetryCount { get; set; } = 0;

    // Navigation properties
    public virtual StockAlert? Alert { get; set; }
}
