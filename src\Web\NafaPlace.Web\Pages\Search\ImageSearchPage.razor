@page "/search/image"
@using NafaPlace.Web.Components.Search
@using NafaPlace.Web.Models.Catalog

<PageTitle>Recherche par image - NafaPlace</PageTitle>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- En-tête -->
            <div class="text-center mb-5">
                <h1 class="display-6 mb-3">
                    <i class="bi bi-camera-fill text-primary me-3"></i>
                    Recherche par image
                </h1>
                <p class="lead text-muted">
                    Trouvez des produits similaires en téléchargeant une photo ou une image
                </p>
            </div>

            <!-- Composant de recherche par image -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <ImageSearch OnSearchCompleted="OnSearchCompleted" />
                </div>
            </div>

            <!-- Conseils d'utilisation -->
            @if (!hasResults)
            {
                <div class="row mt-5">
                    <div class="col-lg-8 mx-auto">
                        <div class="card bg-light border-0">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-lightbulb text-warning me-2"></i>
                                    Conseils pour de meilleurs résultats
                                </h5>
                                <div class="row g-4 mt-2">
                                    <div class="col-md-6">
                                        <div class="d-flex">
                                            <i class="bi bi-check-circle text-success me-3 mt-1"></i>
                                            <div>
                                                <h6>Images claires et nettes</h6>
                                                <small class="text-muted">
                                                    Utilisez des images de bonne qualité avec un bon éclairage
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex">
                                            <i class="bi bi-check-circle text-success me-3 mt-1"></i>
                                            <div>
                                                <h6>Produit bien visible</h6>
                                                <small class="text-muted">
                                                    Le produit doit occuper la majeure partie de l'image
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex">
                                            <i class="bi bi-check-circle text-success me-3 mt-1"></i>
                                            <div>
                                                <h6>Fond neutre</h6>
                                                <small class="text-muted">
                                                    Évitez les arrière-plans trop chargés ou colorés
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex">
                                            <i class="bi bi-check-circle text-success me-3 mt-1"></i>
                                            <div>
                                                <h6>Formats supportés</h6>
                                                <small class="text-muted">
                                                    JPG, PNG, GIF jusqu'à 5MB
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Exemples d'utilisation -->
            @if (!hasResults)
            {
                <div class="row mt-5">
                    <div class="col-12">
                        <h4 class="text-center mb-4">Exemples d'utilisation</h4>
                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-phone display-4 text-primary mb-3"></i>
                                        <h5>Électronique</h5>
                                        <p class="text-muted small">
                                            Trouvez des smartphones, ordinateurs ou accessoires similaires
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-bag display-4 text-success mb-3"></i>
                                        <h5>Mode & Accessoires</h5>
                                        <p class="text-muted small">
                                            Découvrez des vêtements, chaussures ou sacs similaires
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-house display-4 text-warning mb-3"></i>
                                        <h5>Maison & Déco</h5>
                                        <p class="text-muted small">
                                            Recherchez des meubles ou objets de décoration similaires
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Autres options de recherche -->
            @if (!hasResults)
            {
                <div class="row mt-5">
                    <div class="col-12 text-center">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">Vous ne trouvez pas ce que vous cherchez ?</h5>
                                <p class="card-text">
                                    Essayez notre recherche textuelle avancée avec des filtres détaillés
                                </p>
                                <a href="/search" class="btn btn-light">
                                    <i class="bi bi-search me-2"></i>Recherche avancée
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<style>
    .display-6 {
        font-weight: 600;
    }

    .card {
        transition: transform 0.2s ease-in-out;
    }

    .card:hover {
        transform: translateY(-2px);
    }

    .bg-light {
        background-color: #f8f9fa !important;
    }

    .lead {
        font-size: 1.1rem;
        font-weight: 300;
    }
</style>

@code {
    private bool hasResults = false;

    private void OnSearchCompleted(List<ProductDto> results)
    {
        hasResults = results.Any();
        StateHasChanged();
    }
}
