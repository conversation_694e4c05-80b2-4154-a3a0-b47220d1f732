using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using NafaPlace.Chat.Infrastructure.Data;

namespace NafaPlace.Chat.Infrastructure
{
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<ChatDbContext>
    {
        public ChatDbContext CreateDbContext(string[] args)
        {
            var connectionString = "Host=localhost;Port=5432;Database=NafaPlace.Chat;Username=postgres;Password=*****************";
            var optionsBuilder = new DbContextOptionsBuilder<ChatDbContext>();
            optionsBuilder.UseNpgsql(connectionString);

            return new ChatDbContext(optionsBuilder.Options);
        }
    }
}
