# Script de test pour toutes les fonctionnalités NafaPlace
# Auteur: Assistant IA
# Date: 2025-01-28

Write-Host "🚀 Démarrage des tests NafaPlace - Toutes les fonctionnalités" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green

# Configuration des URLs des services
$services = @{
    "Catalog" = "http://localhost:5243"
    "Order" = "http://localhost:5004"
    "Notification" = "http://localhost:5005"
    "Analytics" = "http://localhost:5006"
    "Chat" = "http://localhost:5007"
    "Recommendation" = "http://localhost:5008"
    "Loyalty" = "http://localhost:5009"
    "Localization" = "http://localhost:5010"
    "Search" = "http://localhost:5011"
    "Inventory" = "http://localhost:5012"
}

# Variables globales
$authToken = ""
$testUserId = "test-user-123"
$testProductId = 1

# Fonction pour tester la santé d'un service
function Test-ServiceHealth {
    param($serviceName, $baseUrl)
    
    Write-Host "🔍 Test de santé: $serviceName" -ForegroundColor Yellow
    
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get -TimeoutSec 10
        Write-Host "✅ $serviceName: Service opérationnel" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ $serviceName: Service indisponible - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour obtenir un token d'authentification
function Get-AuthToken {
    Write-Host "🔐 Obtention du token d'authentification..." -ForegroundColor Yellow
    
    $loginData = @{
        email = "<EMAIL>"
        password = "Admin123!"
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$($services.Catalog)/api/auth/login" -Method Post -Body $loginData -ContentType "application/json"
        $script:authToken = $response.token
        Write-Host "✅ Token d'authentification obtenu" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Échec de l'authentification: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction pour créer les headers avec authentification
function Get-AuthHeaders {
    return @{
        "Authorization" = "Bearer $script:authToken"
        "Content-Type" = "application/json"
    }
}

# Test 1: Recherche avancée
function Test-SearchService {
    Write-Host "🔍 Test du service de recherche avancée..." -ForegroundColor Cyan
    
    $searchData = @{
        query = "smartphone"
        filters = @{
            category = "Electronics"
            minPrice = 100
            maxPrice = 1000
        }
        limit = 10
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$($services.Search)/api/search" -Method Post -Body $searchData -Headers (Get-AuthHeaders)
        Write-Host "✅ Recherche: $($response.totalResults) résultats trouvés" -ForegroundColor Green
        
        # Test autocomplétion
        $autocompleteResponse = Invoke-RestMethod -Uri "$($services.Search)/api/search/autocomplete?query=smart" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Autocomplétion: $($autocompleteResponse.suggestions.Count) suggestions" -ForegroundColor Green
        
        return $true
    }
    catch {
        Write-Host "❌ Erreur recherche: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test 2: Analytics et KPIs
function Test-AnalyticsService {
    Write-Host "📊 Test du service Analytics..." -ForegroundColor Cyan
    
    try {
        # Test KPIs
        $kpisResponse = Invoke-RestMethod -Uri "$($services.Analytics)/api/kpis" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Analytics: $($kpisResponse.Count) KPIs récupérés" -ForegroundColor Green
        
        # Test métriques de vente
        $salesResponse = Invoke-RestMethod -Uri "$($services.Analytics)/api/sales/metrics?period=daily" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Métriques de vente récupérées" -ForegroundColor Green
        
        # Test événement analytics
        $eventData = @{
            eventType = "product_view"
            userId = $testUserId
            entityType = "product"
            entityId = $testProductId.ToString()
            properties = @{
                source = "test"
                timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
            }
        } | ConvertTo-Json
        
        Invoke-RestMethod -Uri "$($services.Analytics)/api/events" -Method Post -Body $eventData -Headers (Get-AuthHeaders)
        Write-Host "✅ Événement analytics enregistré" -ForegroundColor Green
        
        return $true
    }
    catch {
        Write-Host "❌ Erreur Analytics: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test 3: Chat et Support Client
function Test-ChatService {
    Write-Host "💬 Test du service Chat Support..." -ForegroundColor Cyan
    
    try {
        # Test création de conversation
        $conversationData = @{
            subject = "Test de support"
            category = 1
            priority = 2
            userId = $testUserId
            userName = "Test User"
            userEmail = "<EMAIL>"
        } | ConvertTo-Json
        
        $conversationResponse = Invoke-RestMethod -Uri "$($services.Chat)/api/conversations" -Method Post -Body $conversationData -Headers (Get-AuthHeaders)
        $conversationId = $conversationResponse.id
        Write-Host "✅ Conversation créée: ID $conversationId" -ForegroundColor Green
        
        # Test envoi de message
        $messageData = @{
            conversationId = $conversationId
            content = "Bonjour, j'ai besoin d'aide"
            messageType = 1
            senderType = 1
            senderId = $testUserId
            senderName = "Test User"
        } | ConvertTo-Json
        
        Invoke-RestMethod -Uri "$($services.Chat)/api/messages" -Method Post -Body $messageData -Headers (Get-AuthHeaders)
        Write-Host "✅ Message envoyé" -ForegroundColor Green
        
        # Test FAQ
        $faqResponse = Invoke-RestMethod -Uri "$($services.Chat)/api/faq?category=1" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ FAQ: $($faqResponse.Count) articles récupérés" -ForegroundColor Green
        
        return $true
    }
    catch {
        Write-Host "❌ Erreur Chat: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test 4: Recommandations IA
function Test-RecommendationService {
    Write-Host "🤖 Test du service Recommandations..." -ForegroundColor Cyan
    
    try {
        # Test recommandations personnalisées
        $recommendationData = @{
            userId = $testUserId
            type = 1
            limit = 5
        } | ConvertTo-Json
        
        $recommendationResponse = Invoke-RestMethod -Uri "$($services.Recommendation)/api/recommendations/personalized" -Method Post -Body $recommendationData -Headers (Get-AuthHeaders)
        Write-Host "✅ Recommandations: $($recommendationResponse.products.Count) produits recommandés" -ForegroundColor Green
        
        # Test produits similaires
        $similarResponse = Invoke-RestMethod -Uri "$($services.Recommendation)/api/recommendations/similar/$testProductId" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Produits similaires: $($similarResponse.products.Count) produits" -ForegroundColor Green
        
        # Test enregistrement d'interaction
        $interactionData = @{
            userId = $testUserId
            productId = $testProductId
            type = 1
            weight = 1.0
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        } | ConvertTo-Json
        
        Invoke-RestMethod -Uri "$($services.Recommendation)/api/interactions" -Method Post -Body $interactionData -Headers (Get-AuthHeaders)
        Write-Host "✅ Interaction utilisateur enregistrée" -ForegroundColor Green
        
        return $true
    }
    catch {
        Write-Host "❌ Erreur Recommandations: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test 5: Programme de Fidélité
function Test-LoyaltyService {
    Write-Host "🎁 Test du service Fidélité..." -ForegroundColor Cyan
    
    try {
        # Test compte de fidélité
        $loyaltyResponse = Invoke-RestMethod -Uri "$($services.Loyalty)/api/loyalty/account/$testUserId" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Compte fidélité: $($loyaltyResponse.totalPoints) points" -ForegroundColor Green
        
        # Test attribution de points
        $pointsData = @{
            userId = $testUserId
            points = 100
            description = "Test d'attribution de points"
            referenceType = "test"
        } | ConvertTo-Json
        
        Invoke-RestMethod -Uri "$($services.Loyalty)/api/loyalty/points/earn" -Method Post -Body $pointsData -Headers (Get-AuthHeaders)
        Write-Host "✅ Points attribués avec succès" -ForegroundColor Green
        
        # Test récompenses disponibles
        $rewardsResponse = Invoke-RestMethod -Uri "$($services.Loyalty)/api/rewards/eligible/$testUserId" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Récompenses: $($rewardsResponse.Count) récompenses disponibles" -ForegroundColor Green
        
        # Test badges
        $badgesResponse = Invoke-RestMethod -Uri "$($services.Loyalty)/api/badges/user/$testUserId" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Badges: $($badgesResponse.Count) badges obtenus" -ForegroundColor Green
        
        return $true
    }
    catch {
        Write-Host "❌ Erreur Fidélité: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test 6: Localisation et Multi-langues
function Test-LocalizationService {
    Write-Host "🌍 Test du service Localisation..." -ForegroundColor Cyan
    
    try {
        # Test langues supportées
        $languagesResponse = Invoke-RestMethod -Uri "$($services.Localization)/api/languages" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Langues: $($languagesResponse.Count) langues supportées" -ForegroundColor Green
        
        # Test traduction
        $translationResponse = Invoke-RestMethod -Uri "$($services.Localization)/api/translations/welcome/fr" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Traduction récupérée: $($translationResponse.value)" -ForegroundColor Green
        
        # Test traduction automatique
        $autoTranslateData = @{
            text = "Bonjour le monde"
            sourceLanguage = "fr"
            targetLanguage = "en"
        } | ConvertTo-Json
        
        $autoTranslateResponse = Invoke-RestMethod -Uri "$($services.Localization)/api/translate/auto" -Method Post -Body $autoTranslateData -Headers (Get-AuthHeaders)
        Write-Host "✅ Traduction automatique: $($autoTranslateResponse.translatedText)" -ForegroundColor Green
        
        # Test formatage de devise
        $currencyResponse = Invoke-RestMethod -Uri "$($services.Localization)/api/format/currency?amount=1000&language=fr" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Formatage devise: $($currencyResponse.formattedAmount)" -ForegroundColor Green
        
        return $true
    }
    catch {
        Write-Host "❌ Erreur Localisation: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test 7: Notifications en temps réel
function Test-NotificationService {
    Write-Host "🔔 Test du service Notifications..." -ForegroundColor Cyan
    
    try {
        # Test envoi de notification
        $notificationData = @{
            userId = $testUserId
            type = "info"
            title = "Test de notification"
            message = "Ceci est un test de notification"
            channels = @("web", "email")
        } | ConvertTo-Json
        
        Invoke-RestMethod -Uri "$($services.Notification)/api/notifications/send" -Method Post -Body $notificationData -Headers (Get-AuthHeaders)
        Write-Host "✅ Notification envoyée" -ForegroundColor Green
        
        # Test récupération des notifications
        $userNotificationsResponse = Invoke-RestMethod -Uri "$($services.Notification)/api/notifications/user/$testUserId" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Notifications utilisateur: $($userNotificationsResponse.Count) notifications" -ForegroundColor Green
        
        return $true
    }
    catch {
        Write-Host "❌ Erreur Notifications: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test 8: Inventory Management
function Test-InventoryService {
    Write-Host "📦 Test du service Inventory..." -ForegroundColor Cyan
    
    try {
        # Test stock d'un produit
        $stockResponse = Invoke-RestMethod -Uri "$($services.Inventory)/api/inventory/stock/$testProductId" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Stock produit: $($stockResponse.quantity) unités" -ForegroundColor Green
        
        # Test prévisions de stock
        $forecastResponse = Invoke-RestMethod -Uri "$($services.Inventory)/api/inventory/forecast/$testProductId" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Prévisions de stock générées" -ForegroundColor Green
        
        # Test alertes de stock
        $alertsResponse = Invoke-RestMethod -Uri "$($services.Inventory)/api/inventory/alerts" -Method Get -Headers (Get-AuthHeaders)
        Write-Host "✅ Alertes: $($alertsResponse.Count) alertes de stock" -ForegroundColor Green
        
        return $true
    }
    catch {
        Write-Host "❌ Erreur Inventory: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Fonction principale de test
function Start-AllTests {
    Write-Host "🚀 Démarrage de tous les tests..." -ForegroundColor Green
    
    $results = @{}
    
    # Test de santé de tous les services
    Write-Host "`n=== TESTS DE SANTÉ DES SERVICES ===" -ForegroundColor Magenta
    foreach ($service in $services.GetEnumerator()) {
        $results[$service.Key + "_Health"] = Test-ServiceHealth $service.Key $service.Value
    }
    
    # Authentification
    Write-Host "`n=== AUTHENTIFICATION ===" -ForegroundColor Magenta
    $authSuccess = Get-AuthToken
    if (-not $authSuccess) {
        Write-Host "❌ Impossible de continuer sans authentification" -ForegroundColor Red
        return
    }
    
    # Tests fonctionnels
    Write-Host "`n=== TESTS FONCTIONNELS ===" -ForegroundColor Magenta
    $results["Search"] = Test-SearchService
    $results["Analytics"] = Test-AnalyticsService
    $results["Chat"] = Test-ChatService
    $results["Recommendation"] = Test-RecommendationService
    $results["Loyalty"] = Test-LoyaltyService
    $results["Localization"] = Test-LocalizationService
    $results["Notification"] = Test-NotificationService
    $results["Inventory"] = Test-InventoryService
    
    # Résumé des résultats
    Write-Host "`n=== RÉSUMÉ DES TESTS ===" -ForegroundColor Magenta
    $successCount = 0
    $totalCount = 0
    
    foreach ($result in $results.GetEnumerator()) {
        $totalCount++
        if ($result.Value) {
            $successCount++
            Write-Host "✅ $($result.Key): SUCCÈS" -ForegroundColor Green
        } else {
            Write-Host "❌ $($result.Key): ÉCHEC" -ForegroundColor Red
        }
    }
    
    $successRate = [math]::Round(($successCount / $totalCount) * 100, 2)
    Write-Host "`n📊 Taux de réussite: $successCount/$totalCount ($successRate%)" -ForegroundColor $(if ($successRate -ge 80) { "Green" } else { "Yellow" })
    
    if ($successRate -eq 100) {
        Write-Host "🎉 TOUS LES TESTS SONT PASSÉS ! NafaPlace est opérationnel !" -ForegroundColor Green
    } elseif ($successRate -ge 80) {
        Write-Host "⚠️  La plupart des tests sont passés. Vérifiez les services en échec." -ForegroundColor Yellow
    } else {
        Write-Host "🚨 Plusieurs tests ont échoué. Vérification nécessaire." -ForegroundColor Red
    }
}

# Exécution des tests
Start-AllTests

Write-Host "`n🏁 Tests terminés !" -ForegroundColor Green
