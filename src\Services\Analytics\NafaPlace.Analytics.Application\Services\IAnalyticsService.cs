using NafaPlace.Analytics.Application.DTOs;

namespace NafaPlace.Analytics.Application.Services;

public interface IAnalyticsService
{
    // Dashboard Analytics
    Task<DashboardAnalyticsDto> GetDashboardAnalyticsAsync(AnalyticsFilterDto? filters = null);
    Task<DashboardAnalyticsDto> GetSellerDashboardAnalyticsAsync(int sellerId, AnalyticsFilterDto? filters = null);
    Task<List<KPIDto>> GetKPIsAsync(int? sellerId = null, List<string>? kpiNames = null);
    
    // Sales Analytics
    Task<SalesAnalyticsDto> GetSalesAnalyticsAsync(AnalyticsFilterDto? filters = null);
    Task<List<TimeSeriesDataDto>> GetRevenueTimeSeriesAsync(AnalyticsFilterDto filters);
    Task<List<TopSellerDto>> GetTopSellersAsync(AnalyticsFilterDto? filters = null, int limit = 10);
    Task<List<CategorySalesDto>> GetSalesByCategoryAsync(AnalyticsFilterDto? filters = null);
    Task<decimal> GetTotalRevenueAsync(DateTime? startDate = null, DateTime? endDate = null, int? sellerId = null);
    
    // Product Analytics
    Task<ProductAnalyticsDto> GetProductAnalyticsAsync(AnalyticsFilterDto? filters = null);
    Task<List<TopProductDto>> GetTopProductsAsync(AnalyticsFilterDto? filters = null, int limit = 10);
    Task<List<ProductPerformanceDto>> GetProductPerformanceAsync(AnalyticsFilterDto? filters = null);
    Task<Dictionary<string, object>> GetProductInsightsAsync(int productId, AnalyticsFilterDto? filters = null);
    
    // Customer Analytics
    Task<CustomerAnalyticsDto> GetCustomerAnalyticsAsync(AnalyticsFilterDto? filters = null);
    Task<List<CustomerSegmentDto>> GetCustomerSegmentsAsync(AnalyticsFilterDto? filters = null);
    Task<List<TimeSeriesDataDto>> GetCustomerAcquisitionAsync(AnalyticsFilterDto filters);
    Task<double> GetCustomerRetentionRateAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<decimal> GetCustomerLifetimeValueAsync(int? customerId = null);
    
    // Inventory Analytics
    Task<InventoryAnalyticsDto> GetInventoryAnalyticsAsync(AnalyticsFilterDto? filters = null);
    Task<List<StockAlertSummaryDto>> GetCriticalStockAlertsAsync(int? sellerId = null);
    Task<List<TopProductDto>> GetSlowMovingProductsAsync(int? sellerId = null, int days = 30);
    Task<double> GetInventoryTurnoverAsync(int? sellerId = null, DateTime? startDate = null, DateTime? endDate = null);
    
    // Traffic Analytics
    Task<TrafficAnalyticsDto> GetTrafficAnalyticsAsync(AnalyticsFilterDto? filters = null);
    Task<List<TimeSeriesDataDto>> GetVisitorTimeSeriesAsync(AnalyticsFilterDto filters);
    Task<List<TopPageDto>> GetTopPagesAsync(AnalyticsFilterDto? filters = null, int limit = 10);
    Task<List<TrafficSourceDto>> GetTrafficSourcesAsync(AnalyticsFilterDto? filters = null);
    
    // Performance Analytics
    Task<PerformanceMetricsDto> GetPerformanceMetricsAsync();
    Task<List<ServiceHealthDto>> GetServiceHealthAsync();
    Task<Dictionary<string, object>> GetSystemMetricsAsync();
    
    // Comparative Analytics
    Task<Dictionary<string, object>> GetComparisonAnalyticsAsync(AnalyticsFilterDto currentPeriod, AnalyticsFilterDto previousPeriod);
    Task<List<TimeSeriesDataDto>> GetTrendAnalysisAsync(string metric, AnalyticsFilterDto filters);
    Task<Dictionary<string, double>> GetGrowthRatesAsync(AnalyticsFilterDto? filters = null);
    
    // Real-time Analytics
    Task<Dictionary<string, object>> GetRealTimeMetricsAsync();
    Task<List<RecentActivityDto>> GetRecentActivitiesAsync(int limit = 20, int? sellerId = null);
    Task<int> GetActiveUsersCountAsync();
    Task<List<TimeSeriesDataDto>> GetLiveTrafficAsync(int minutes = 60);
    
    // Predictive Analytics
    Task<Dictionary<string, object>> GetSalesForecastAsync(int days = 30, int? sellerId = null);
    Task<Dictionary<string, object>> GetDemandForecastAsync(int productId, int days = 30);
    Task<List<string>> GetRecommendationsAsync(int? sellerId = null);
    
    // Custom Analytics
    Task<Dictionary<string, object>> ExecuteCustomQueryAsync(string queryName, Dictionary<string, object> parameters);
    Task<List<TimeSeriesDataDto>> GetCustomMetricAsync(string metricDefinition, AnalyticsFilterDto filters);
    
    // Data Export
    Task<byte[]> ExportAnalyticsDataAsync(string dataType, AnalyticsFilterDto filters, string format = "excel");
    Task<List<Dictionary<string, object>>> GetRawDataAsync(string dataType, AnalyticsFilterDto filters);
    
    // Caching and Performance
    Task RefreshAnalyticsCacheAsync();
    Task<bool> WarmupAnalyticsCacheAsync();
    Task<Dictionary<string, object>> GetCacheStatisticsAsync();
    
    // Alerts and Notifications
    Task<List<AnalyticsAlertDto>> GetAnalyticsAlertsAsync(int? sellerId = null);
    Task<bool> CreateAnalyticsAlertAsync(AnalyticsAlertConfigDto config);
    Task ProcessAnalyticsAlertsAsync();
}

public class AnalyticsAlertDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Metric { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty;
    public decimal Threshold { get; set; }
    public decimal CurrentValue { get; set; }
    public string Severity { get; set; } = string.Empty;
    public DateTime TriggeredAt { get; set; }
    public bool IsActive { get; set; }
}

public class AnalyticsAlertConfigDto
{
    public string Name { get; set; } = string.Empty;
    public string Metric { get; set; } = string.Empty;
    public string Condition { get; set; } = string.Empty; // greater_than, less_than, equals, etc.
    public decimal Threshold { get; set; }
    public int? SellerId { get; set; }
    public List<string> NotificationChannels { get; set; } = new();
    public bool IsEnabled { get; set; } = true;
}
