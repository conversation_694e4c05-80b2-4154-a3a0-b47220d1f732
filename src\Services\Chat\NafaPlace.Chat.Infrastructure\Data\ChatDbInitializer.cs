using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NafaPlace.Chat.Domain.Entities;

namespace NafaPlace.Chat.Infrastructure.Data;

public class ChatDbInitializer
{
    private readonly ChatDbContext _context;
    private readonly ILogger<ChatDbInitializer> _logger;

    public ChatDbInitializer(ChatDbContext context, ILogger<ChatDbInitializer> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task InitializeAsync()
    {
        try
        {
            // Créer la base de données si elle n'existe pas
            await _context.Database.EnsureCreatedAsync();

            // Vérifier si des données existent déjà
            if (await _context.Departments.AnyAsync())
            {
                _logger.LogInformation("La base de données Chat contient déjà des données");
                return;
            }

            _logger.LogInformation("Initialisation de la base de données Chat...");

            // Créer les départements
            await SeedDepartmentsAsync();

            // Créer les agents
            await SeedAgentsAsync();

            // Créer les FAQs
            await SeedFAQsAsync();

            await _context.SaveChangesAsync();

            _logger.LogInformation("Base de données Chat initialisée avec succès");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'initialisation de la base de données Chat");
            throw;
        }
    }

    private async Task SeedDepartmentsAsync()
    {
        var departments = new[]
        {
            new Department
            {
                Id = 1,
                Name = "Support Général",
                Description = "Support général pour toutes les questions",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Department
            {
                Id = 2,
                Name = "Support Technique",
                Description = "Support pour les problèmes techniques",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Department
            {
                Id = 3,
                Name = "Service Commercial",
                Description = "Support pour les questions commerciales et ventes",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Department
            {
                Id = 4,
                Name = "Service Livraison",
                Description = "Support pour les questions de livraison",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await _context.Departments.AddRangeAsync(departments);
    }

    private async Task SeedAgentsAsync()
    {
        var agents = new[]
        {
            new Agent
            {
                Id = "agent-support-1",
                Name = "Marie Dupont",
                Email = "<EMAIL>",
                Status = AgentStatus.Online,
                DepartmentId = 1,
                MaxConcurrentChats = 5,
                CurrentChatCount = 0,
                Skills = "[\"Support général\", \"FAQ\", \"Orientation client\"]",
                Languages = "[\"Français\", \"Anglais\"]",
                WorkingHours = "{\"monday\": {\"start\": \"09:00\", \"end\": \"17:00\"}, \"tuesday\": {\"start\": \"09:00\", \"end\": \"17:00\"}, \"wednesday\": {\"start\": \"09:00\", \"end\": \"17:00\"}, \"thursday\": {\"start\": \"09:00\", \"end\": \"17:00\"}, \"friday\": {\"start\": \"09:00\", \"end\": \"17:00\"}}",
                IsActive = true,
                LastActivity = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Agent
            {
                Id = "agent-tech-1",
                Name = "Ahmed Traoré",
                Email = "<EMAIL>",
                Status = AgentStatus.Online,
                DepartmentId = 2,
                MaxConcurrentChats = 3,
                CurrentChatCount = 0,
                Skills = "[\"Support technique\", \"Résolution de problèmes\", \"API\"]",
                Languages = "[\"Français\", \"Anglais\"]",
                WorkingHours = "{\"monday\": {\"start\": \"08:00\", \"end\": \"16:00\"}, \"tuesday\": {\"start\": \"08:00\", \"end\": \"16:00\"}, \"wednesday\": {\"start\": \"08:00\", \"end\": \"16:00\"}, \"thursday\": {\"start\": \"08:00\", \"end\": \"16:00\"}, \"friday\": {\"start\": \"08:00\", \"end\": \"16:00\"}}",
                IsActive = true,
                LastActivity = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Agent
            {
                Id = "agent-commercial-1",
                Name = "Fatou Camara",
                Email = "<EMAIL>",
                Status = AgentStatus.Online,
                DepartmentId = 3,
                MaxConcurrentChats = 4,
                CurrentChatCount = 0,
                Skills = "[\"Ventes\", \"Négociation\", \"Produits\"]",
                Languages = "[\"Français\", \"Anglais\", \"Arabe\"]",
                WorkingHours = "{\"monday\": {\"start\": \"09:00\", \"end\": \"18:00\"}, \"tuesday\": {\"start\": \"09:00\", \"end\": \"18:00\"}, \"wednesday\": {\"start\": \"09:00\", \"end\": \"18:00\"}, \"thursday\": {\"start\": \"09:00\", \"end\": \"18:00\"}, \"friday\": {\"start\": \"09:00\", \"end\": \"18:00\"}, \"saturday\": {\"start\": \"10:00\", \"end\": \"14:00\"}}",
                IsActive = true,
                LastActivity = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await _context.Agents.AddRangeAsync(agents);
    }

    private async Task SeedFAQsAsync()
    {
        var faqs = new[]
        {
            new FAQ
            {
                Question = "Comment passer une commande ?",
                Answer = "Pour passer une commande, ajoutez les produits à votre panier, puis cliquez sur 'Commander'. Suivez les étapes de paiement et de livraison.",
                Category = "Commandes",
                Tags = "[\"commande\", \"panier\", \"achat\"]",
                IsActive = true,
                ViewCount = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new FAQ
            {
                Question = "Quels sont les modes de paiement acceptés ?",
                Answer = "Nous acceptons les cartes Visa/Mastercard, Orange Money, et le paiement à la livraison.",
                Category = "Paiement",
                Tags = "[\"paiement\", \"carte\", \"orange money\"]",
                IsActive = true,
                ViewCount = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new FAQ
            {
                Question = "Quels sont les délais de livraison ?",
                Answer = "Les délais de livraison sont de 2-5 jours ouvrables. La livraison est gratuite pour les commandes supérieures à 50.000 GNF.",
                Category = "Livraison",
                Tags = "[\"livraison\", \"délai\", \"gratuit\"]",
                IsActive = true,
                ViewCount = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new FAQ
            {
                Question = "Comment retourner un produit ?",
                Answer = "Vous pouvez retourner un produit dans les 14 jours suivant la réception. Allez dans 'Mes Commandes' > 'Retourner'.",
                Category = "Retours",
                Tags = "[\"retour\", \"remboursement\", \"14 jours\"]",
                IsActive = true,
                ViewCount = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new FAQ
            {
                Question = "Comment suivre ma commande ?",
                Answer = "Pour suivre votre commande, connectez-vous à votre compte et allez dans 'Mes Commandes'. Vous y trouverez le statut de livraison.",
                Category = "Suivi",
                Tags = "[\"suivi\", \"commande\", \"statut\"]",
                IsActive = true,
                ViewCount = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new FAQ
            {
                Question = "Comment contacter le service client ?",
                Answer = "Vous pouvez nous contacter via le chat en direct, par email à <EMAIL>, ou par téléphone au +224 123 456 789.",
                Category = "Contact",
                Tags = "[\"contact\", \"support\", \"chat\"]",
                IsActive = true,
                ViewCount = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await _context.FAQs.AddRangeAsync(faqs);
    }
}
