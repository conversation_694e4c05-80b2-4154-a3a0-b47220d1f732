using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Reflection;

namespace NafaPlace.Common.CQRS;

/// <summary>
/// Extensions pour l'enregistrement des services CQRS
/// </summary>
public static class CQRSExtensions
{
    /// <summary>
    /// Ajoute les services CQRS à la collection de services
    /// </summary>
    /// <param name="services">Collection de services</param>
    /// <param name="assemblies">Assemblies contenant les handlers</param>
    /// <returns>Collection de services</returns>
    public static IServiceCollection AddCQRS(this IServiceCollection services, params Assembly[] assemblies)
    {
        // Ajouter MediatR
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(assemblies));
        
        // Ajouter les behaviors
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(PerformanceBehavior<,>));
        
        return services;
    }
}

/// <summary>
/// Behavior pour le logging des requêtes
/// </summary>
/// <typeparam name="TRequest">Type de la requête</typeparam>
/// <typeparam name="TResponse">Type de la réponse</typeparam>
public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;

    public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var requestId = GetRequestId(request);
        var userId = GetUserId(request);

        _logger.LogInformation("Processing request {RequestName} with ID {RequestId} for user {UserId}",
            requestName, requestId, userId);

        var response = await next();

        _logger.LogInformation("Completed request {RequestName} with ID {RequestId}",
            requestName, requestId);

        return response;
    }

    private static string GetRequestId(TRequest request)
    {
        return request switch
        {
            ICommand command => command.CommandId.ToString(),
            IQuery<TResponse> query => query.QueryId.ToString(),
            _ => Random.Shared.Next(1, int.MaxValue).ToString()
        };
    }

    private static string? GetUserId(TRequest request)
    {
        return request switch
        {
            ICommand command => command.UserId,
            IQuery<TResponse> query => query.UserId,
            _ => null
        };
    }
}

/// <summary>
/// Behavior pour la validation des requêtes
/// </summary>
/// <typeparam name="TRequest">Type de la requête</typeparam>
/// <typeparam name="TResponse">Type de la réponse</typeparam>
public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;

    public ValidationBehavior(IEnumerable<IValidator<TRequest>> validators)
    {
        _validators = validators;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        if (_validators.Any())
        {
            var context = new ValidationContext<TRequest>(request);
            var validationResults = await Task.WhenAll(_validators.Select(v => v.ValidateAsync(context, cancellationToken)));
            var failures = validationResults.SelectMany(r => r.Errors).Where(f => f != null).ToList();

            if (failures.Any())
            {
                throw new ValidationException(failures);
            }
        }

        return await next();
    }
}

/// <summary>
/// Behavior pour mesurer les performances
/// </summary>
/// <typeparam name="TRequest">Type de la requête</typeparam>
/// <typeparam name="TResponse">Type de la réponse</typeparam>
public class PerformanceBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger<PerformanceBehavior<TRequest, TResponse>> _logger;
    private readonly System.Diagnostics.Stopwatch _timer;

    public PerformanceBehavior(ILogger<PerformanceBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
        _timer = new System.Diagnostics.Stopwatch();
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        _timer.Start();

        var response = await next();

        _timer.Stop();

        var elapsedMilliseconds = _timer.ElapsedMilliseconds;

        if (elapsedMilliseconds > 500) // Log si plus de 500ms
        {
            var requestName = typeof(TRequest).Name;
            var requestId = GetRequestId(request);

            _logger.LogWarning("Long running request: {RequestName} ({RequestId}) took {ElapsedMilliseconds} milliseconds",
                requestName, requestId, elapsedMilliseconds);
        }

        return response;
    }

    private static string GetRequestId(TRequest request)
    {
        return request switch
        {
            ICommand command => command.CommandId.ToString(),
            IQuery<TResponse> query => query.QueryId.ToString(),
            _ => Random.Shared.Next(1, int.MaxValue).ToString()
        };
    }
}

/// <summary>
/// Interface pour les validateurs
/// </summary>
/// <typeparam name="T">Type à valider</typeparam>
public interface IValidator<T>
{
    Task<ValidationResult> ValidateAsync(ValidationContext<T> context, CancellationToken cancellationToken = default);
}

/// <summary>
/// Contexte de validation
/// </summary>
/// <typeparam name="T">Type à valider</typeparam>
public class ValidationContext<T>
{
    public ValidationContext(T instanceToValidate)
    {
        InstanceToValidate = instanceToValidate;
    }

    public T InstanceToValidate { get; }
}

/// <summary>
/// Résultat de validation
/// </summary>
public class ValidationResult
{
    public ValidationResult()
    {
        Errors = new List<ValidationFailure>();
    }

    public ValidationResult(IEnumerable<ValidationFailure> failures)
    {
        Errors = failures.ToList();
    }

    public List<ValidationFailure> Errors { get; }
    public bool IsValid => !Errors.Any();
}

/// <summary>
/// Échec de validation
/// </summary>
public class ValidationFailure
{
    public ValidationFailure(string propertyName, string errorMessage)
    {
        PropertyName = propertyName;
        ErrorMessage = errorMessage;
    }

    public string PropertyName { get; }
    public string ErrorMessage { get; }
}

/// <summary>
/// Exception de validation
/// </summary>
public class ValidationException : Exception
{
    public ValidationException(IEnumerable<ValidationFailure> failures)
        : base("One or more validation failures have occurred.")
    {
        Errors = failures.GroupBy(e => e.PropertyName, e => e.ErrorMessage)
            .ToDictionary(failureGroup => failureGroup.Key, failureGroup => failureGroup.ToArray());
    }

    public IDictionary<string, string[]> Errors { get; }
}
