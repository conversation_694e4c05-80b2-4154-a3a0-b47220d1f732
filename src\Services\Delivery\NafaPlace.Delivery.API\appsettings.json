{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=NafaPlace.Delivery;Username=postgres;Password=*****************;"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "NafaPlace", "Audience": "NafaPlace-Users", "ExpirationInMinutes": 60}}