using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Text.Json;

namespace NafaPlace.Infrastructure.HealthChecks;

/// <summary>
/// Extensions pour configurer les health checks
/// </summary>
public static class HealthCheckExtensions
{
    /// <summary>
    /// Ajoute les health checks de base
    /// </summary>
    public static IServiceCollection AddNafaPlaceHealthChecks(
        this IServiceCollection services,
        string connectionString,
        string? redisConnectionString = null,
        Dictionary<string, string>? externalServices = null)
    {
        var healthChecksBuilder = services.AddHealthChecks();

        // Health check pour PostgreSQL
        if (!string.IsNullOrEmpty(connectionString))
        {
            healthChecksBuilder.AddNpgSql(
                connectionString,
                name: "database",
                tags: new[] { "db", "postgresql" });
        }

        // Health check pour Redis
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            healthChecksBuilder.AddRedis(
                redisConnectionString,
                name: "redis",
                tags: new[] { "cache", "redis" });
        }

        // Health checks pour les services externes
        if (externalServices != null)
        {
            foreach (var service in externalServices)
            {
                healthChecksBuilder.AddUrlGroup(
                    new Uri(service.Value),
                    name: service.Key,
                    tags: new[] { "external", "api" });
            }
        }

        // Health checks personnalisés
        healthChecksBuilder.AddCheck<DiskSpaceHealthCheck>("disk_space", tags: new[] { "system" });
        healthChecksBuilder.AddCheck<MemoryHealthCheck>("memory", tags: new[] { "system" });
        healthChecksBuilder.AddCheck<ApplicationHealthCheck>("application", tags: new[] { "application" });

        return services;
    }

    /// <summary>
    /// Configure les endpoints de health checks
    /// </summary>
    public static IApplicationBuilder UseNafaPlaceHealthChecks(this IApplicationBuilder app)
    {
        // Health check simple
        app.UseHealthChecks("/health", new HealthCheckOptions
        {
            ResponseWriter = WriteHealthCheckResponse
        });

        // Health check détaillé
        app.UseHealthChecks("/health/detailed", new HealthCheckOptions
        {
            ResponseWriter = WriteDetailedHealthCheckResponse
        });

        // Health check pour la base de données uniquement
        app.UseHealthChecks("/health/db", new HealthCheckOptions
        {
            Predicate = check => check.Tags.Contains("db"),
            ResponseWriter = WriteHealthCheckResponse
        });

        // Health check pour le cache uniquement
        app.UseHealthChecks("/health/cache", new HealthCheckOptions
        {
            Predicate = check => check.Tags.Contains("cache"),
            ResponseWriter = WriteHealthCheckResponse
        });

        // Health check pour les services externes
        app.UseHealthChecks("/health/external", new HealthCheckOptions
        {
            Predicate = check => check.Tags.Contains("external"),
            ResponseWriter = WriteHealthCheckResponse
        });

        return app;
    }

    private static async Task WriteHealthCheckResponse(HttpContext context, HealthReport report)
    {
        context.Response.ContentType = "application/json";

        var response = new
        {
            status = report.Status.ToString(),
            timestamp = DateTime.UtcNow,
            duration = report.TotalDuration.TotalMilliseconds,
            checks = report.Entries.Select(entry => new
            {
                name = entry.Key,
                status = entry.Value.Status.ToString(),
                duration = entry.Value.Duration.TotalMilliseconds,
                description = entry.Value.Description,
                tags = entry.Value.Tags
            })
        };

        var json = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });

        await context.Response.WriteAsync(json);
    }

    private static async Task WriteDetailedHealthCheckResponse(HttpContext context, HealthReport report)
    {
        context.Response.ContentType = "application/json";

        var response = new
        {
            status = report.Status.ToString(),
            timestamp = DateTime.UtcNow,
            duration = report.TotalDuration.TotalMilliseconds,
            environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
            version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "Unknown",
            machineName = Environment.MachineName,
            processId = Environment.ProcessId,
            workingSet = GC.GetTotalMemory(false),
            checks = report.Entries.Select(entry => new
            {
                name = entry.Key,
                status = entry.Value.Status.ToString(),
                duration = entry.Value.Duration.TotalMilliseconds,
                description = entry.Value.Description,
                exception = entry.Value.Exception?.Message,
                data = entry.Value.Data,
                tags = entry.Value.Tags
            })
        };

        var json = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });

        await context.Response.WriteAsync(json);
    }
}

/// <summary>
/// Health check pour l'espace disque
/// </summary>
public class DiskSpaceHealthCheck : IHealthCheck
{
    private readonly long _minimumFreeBytesThreshold;

    public DiskSpaceHealthCheck(long minimumFreeBytesThreshold = 1024 * 1024 * 1024) // 1 GB par défaut
    {
        _minimumFreeBytesThreshold = minimumFreeBytesThreshold;
    }

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var drives = DriveInfo.GetDrives().Where(d => d.IsReady);
            var data = new Dictionary<string, object>();

            foreach (var drive in drives)
            {
                var freeBytes = drive.AvailableFreeSpace;
                var totalBytes = drive.TotalSize;
                var usedBytes = totalBytes - freeBytes;
                var percentUsed = (double)usedBytes / totalBytes * 100;

                data[drive.Name] = new
                {
                    freeBytes,
                    totalBytes,
                    usedBytes,
                    percentUsed = Math.Round(percentUsed, 2)
                };

                if (freeBytes < _minimumFreeBytesThreshold)
                {
                    return Task.FromResult(HealthCheckResult.Unhealthy(
                        $"Espace disque insuffisant sur {drive.Name}: {freeBytes / (1024 * 1024 * 1024)} GB disponibles",
                        data: data));
                }
            }

            return Task.FromResult(HealthCheckResult.Healthy("Espace disque suffisant", data));
        }
        catch (Exception ex)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy("Erreur lors de la vérification de l'espace disque", ex));
        }
    }
}

/// <summary>
/// Health check pour la mémoire
/// </summary>
public class MemoryHealthCheck : IHealthCheck
{
    private readonly long _maximumMemoryThreshold;

    public MemoryHealthCheck(long maximumMemoryThreshold = 1024 * 1024 * 1024) // 1 GB par défaut
    {
        _maximumMemoryThreshold = maximumMemoryThreshold;
    }

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var allocatedMemory = GC.GetTotalMemory(false);
            var workingSet = Environment.WorkingSet;
            
            var data = new Dictionary<string, object>
            {
                ["allocatedMemory"] = allocatedMemory,
                ["workingSet"] = workingSet,
                ["gen0Collections"] = GC.CollectionCount(0),
                ["gen1Collections"] = GC.CollectionCount(1),
                ["gen2Collections"] = GC.CollectionCount(2)
            };

            if (allocatedMemory > _maximumMemoryThreshold)
            {
                return Task.FromResult(HealthCheckResult.Degraded(
                    $"Utilisation mémoire élevée: {allocatedMemory / (1024 * 1024)} MB alloués",
                    data: data));
            }

            return Task.FromResult(HealthCheckResult.Healthy("Utilisation mémoire normale", data));
        }
        catch (Exception ex)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy("Erreur lors de la vérification de la mémoire", ex));
        }
    }
}

/// <summary>
/// Health check pour l'application
/// </summary>
public class ApplicationHealthCheck : IHealthCheck
{
    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var data = new Dictionary<string, object>
            {
                ["uptime"] = DateTime.UtcNow - Process.GetCurrentProcess().StartTime.ToUniversalTime(),
                ["environment"] = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                ["machineName"] = Environment.MachineName,
                ["processId"] = Environment.ProcessId,
                ["threadCount"] = Process.GetCurrentProcess().Threads.Count,
                ["handleCount"] = Process.GetCurrentProcess().HandleCount
            };

            return Task.FromResult(HealthCheckResult.Healthy("Application en cours d'exécution", data));
        }
        catch (Exception ex)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy("Erreur lors de la vérification de l'application", ex));
        }
    }
}
