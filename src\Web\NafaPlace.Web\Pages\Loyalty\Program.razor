@page "/loyalty/program"
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IJSRuntime JSRuntime

<PageTitle>Programme Fidélité - NafaPlace</PageTitle>

<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-5 fw-bold mb-3">
                    🎁 Programme Fidélité NafaPlace
                </h1>
                <p class="lead text-muted">
                    Gagnez des points à chaque achat et débloquez des récompenses exclusives
                </p>
            </div>
        </div>
    </div>

    <AuthorizeView>
        <Authorized>
            <!-- User Loyalty Status -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div class="card-body text-white p-4">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="loyalty-badge me-3">
                                            <i class="bi bi-gem fs-1"></i>
                                        </div>
                                        <div>
                                            <h4 class="mb-1">Statut: @currentTier</h4>
                                            <p class="mb-0 opacity-75">Membre depuis @memberSince</p>
                                        </div>
                                    </div>
                                    <div class="row g-3">
                                        <div class="col-sm-4">
                                            <div class="text-center">
                                                <h3 class="mb-0">@currentPoints.ToString("N0")</h3>
                                                <small class="opacity-75">Points Actuels</small>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="text-center">
                                                <h3 class="mb-0">@totalEarned.ToString("N0")</h3>
                                                <small class="opacity-75">Points Gagnés</small>
                                            </div>
                                        </div>
                                        <div class="col-sm-4">
                                            <div class="text-center">
                                                <h3 class="mb-0">@totalRedeemed.ToString("N0")</h3>
                                                <small class="opacity-75">Points Utilisés</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="progress-circle mb-3">
                                        <div class="progress-value">@progressToNextTier%</div>
                                    </div>
                                    <p class="mb-0">
                                        <strong>@pointsToNextTier points</strong> pour atteindre le niveau @nextTier
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row g-4 mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100 border-0 shadow-sm text-center">
                        <div class="card-body">
                            <i class="bi bi-gift text-primary fs-1 mb-3"></i>
                            <h6 class="card-title">Échanger des Points</h6>
                            <p class="card-text text-muted small">Utilisez vos points pour des récompenses</p>
                            <button class="btn btn-primary btn-sm" @onclick="ShowRewards">Voir Récompenses</button>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card h-100 border-0 shadow-sm text-center">
                        <div class="card-body">
                            <i class="bi bi-calendar-event text-success fs-1 mb-3"></i>
                            <h6 class="card-title">Défis du Mois</h6>
                            <p class="card-text text-muted small">Relevez des défis pour plus de points</p>
                            <button class="btn btn-success btn-sm" @onclick="ShowChallenges">Voir Défis</button>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card h-100 border-0 shadow-sm text-center">
                        <div class="card-body">
                            <i class="bi bi-people text-info fs-1 mb-3"></i>
                            <h6 class="card-title">Parrainage</h6>
                            <p class="card-text text-muted small">Invitez des amis et gagnez des points</p>
                            <button class="btn btn-info btn-sm" @onclick="ShowReferral">Parrainer</button>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card h-100 border-0 shadow-sm text-center">
                        <div class="card-body">
                            <i class="bi bi-clock-history text-warning fs-1 mb-3"></i>
                            <h6 class="card-title">Historique</h6>
                            <p class="card-text text-muted small">Consultez vos transactions</p>
                            <button class="btn btn-warning btn-sm" @onclick="ShowHistory">Voir Historique</button>
                        </div>
                    </div>
                </div>
            </div>
        </Authorized>
        <NotAuthorized>
            <!-- Login Prompt -->
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="card border-0 shadow-sm text-center">
                        <div class="card-body p-5">
                            <i class="bi bi-person-circle text-muted mb-3" style="font-size: 4rem;"></i>
                            <h4 class="mb-3">Connectez-vous pour accéder au programme fidélité</h4>
                            <p class="text-muted mb-4">
                                Rejoignez notre programme fidélité et commencez à gagner des points dès votre premier achat !
                            </p>
                            <div class="d-grid gap-2 d-md-block">
                                <a href="/auth/login" class="btn btn-primary">Se connecter</a>
                                <a href="/auth/register" class="btn btn-outline-primary">S'inscrire</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </NotAuthorized>
    </AuthorizeView>

    <!-- Loyalty Tiers -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="text-center mb-4">🏆 Niveaux de Fidélité</h3>
            <div class="row g-4">
                @foreach (var tier in loyaltyTiers)
                {
                    <div class="col-lg-3 col-md-6">
                        <div class="card h-100 border-0 shadow-sm @(tier.Name == currentTier ? "border-primary" : "")">
                            <div class="card-body text-center">
                                <div class="tier-icon mb-3 @(tier.Name == currentTier ? "text-primary" : "text-muted")">
                                    <i class="@tier.Icon" style="font-size: 2.5rem;"></i>
                                </div>
                                <h5 class="card-title @(tier.Name == currentTier ? "text-primary" : "")">@tier.Name</h5>
                                <p class="text-muted small mb-3">@tier.Description</p>
                                <div class="mb-3">
                                    <strong>@tier.MinPoints.ToString("N0")</strong> points requis
                                </div>
                                <ul class="list-unstyled small text-start">
                                    @foreach (var benefit in tier.Benefits)
                                    {
                                        <li class="mb-1">
                                            <i class="bi bi-check-circle text-success me-2"></i>
                                            @benefit
                                        </li>
                                    }
                                </ul>
                                @if (tier.Name == currentTier)
                                {
                                    <span class="badge bg-primary">Votre niveau actuel</span>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- How to Earn Points -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="text-center mb-4">💰 Comment Gagner des Points</h3>
            <div class="row g-4">
                @foreach (var way in waysToEarn)
                {
                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="@way.Icon text-primary fs-1 mb-3"></i>
                                <h6 class="card-title">@way.Title</h6>
                                <p class="card-text text-muted small">@way.Description</p>
                                <div class="badge bg-primary">+@way.Points points</div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Available Rewards -->
    <div class="row">
        <div class="col-12">
            <h3 class="text-center mb-4">🎁 Récompenses Disponibles</h3>
            <div class="row g-4">
                @foreach (var reward in availableRewards)
                {
                    <div class="col-lg-4 col-md-6">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-start">
                                    <div class="flex-shrink-0">
                                        <i class="@reward.Icon text-primary fs-2"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="card-title">@reward.Title</h6>
                                        <p class="card-text text-muted small">@reward.Description</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-warning">@reward.PointsCost points</span>
                                            <AuthorizeView>
                                                <Authorized>
                                                    <button class="btn btn-sm @(currentPoints >= reward.PointsCost ? "btn-primary" : "btn-outline-secondary")" 
                                                            disabled="@(currentPoints < reward.PointsCost)"
                                                            @onclick="() => RedeemReward(reward)">
                                                        @(currentPoints >= reward.PointsCost ? "Échanger" : "Insuffisant")
                                                    </button>
                                                </Authorized>
                                            </AuthorizeView>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    /* Fix text visibility */
    .container h1, .container h3, .container h4, .container h5, .container h6 {
        color: #212529 !important;
    }

    .text-muted {
        color: #6c757d !important;
    }

    .card-title {
        color: #212529 !important;
    }

    .card-text {
        color: #495057 !important;
    }

    small.text-muted {
        color: #6c757d !important;
    }

    .lead {
        color: #6c757d !important;
    }

    .card {
        background-color: white !important;
        color: #212529 !important;
    }

    .card-body {
        color: #212529 !important;
    }

    .loyalty-badge {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        padding: 1rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .progress-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: conic-gradient(#fff 0deg, #fff @(progressToNextTier * 3.6)deg, rgba(255,255,255,0.3) @(progressToNextTier * 3.6)deg);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        position: relative;
    }

    .progress-circle::before {
        content: '';
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #667eea;
        position: absolute;
    }

    .progress-value {
        position: relative;
        z-index: 1;
        font-weight: bold;
        font-size: 0.9rem;
    }

    .tier-icon {
        transition: all 0.3s ease;
    }

    .card:hover .tier-icon {
        transform: scale(1.1);
    }
</style>

@code {
    // User loyalty data
    private string currentTier = "Argent";
    private string memberSince = "Janvier 2024";
    private int currentPoints = 2450;
    private int totalEarned = 5670;
    private int totalRedeemed = 3220;
    private int progressToNextTier = 65;
    private int pointsToNextTier = 1550;
    private string nextTier = "Or";

    // Loyalty tiers
    private List<LoyaltyTier> loyaltyTiers = new()
    {
        new LoyaltyTier
        {
            Name = "Bronze",
            Icon = "bi bi-award",
            Description = "Niveau de départ",
            MinPoints = 0,
            Benefits = new[] { "1 point par 1000 GNF", "Offres spéciales mensuelles" }
        },
        new LoyaltyTier
        {
            Name = "Argent",
            Icon = "bi bi-gem",
            Description = "Membre fidèle",
            MinPoints = 1000,
            Benefits = new[] { "1.5 points par 1000 GNF", "Livraison gratuite", "Support prioritaire" }
        },
        new LoyaltyTier
        {
            Name = "Or",
            Icon = "bi bi-trophy",
            Description = "Membre privilégié",
            MinPoints = 4000,
            Benefits = new[] { "2 points par 1000 GNF", "Accès anticipé aux ventes", "Retours gratuits" }
        },
        new LoyaltyTier
        {
            Name = "Platine",
            Icon = "bi bi-star-fill",
            Description = "Membre VIP",
            MinPoints = 10000,
            Benefits = new[] { "3 points par 1000 GNF", "Gestionnaire de compte dédié", "Cadeaux exclusifs" }
        }
    };

    // Ways to earn points
    private List<EarnWay> waysToEarn = new()
    {
        new EarnWay { Icon = "bi bi-cart-check", Title = "Achats", Description = "Gagnez des points à chaque achat", Points = "1-3 par 1000 GNF" },
        new EarnWay { Icon = "bi bi-star", Title = "Avis Produits", Description = "Laissez des avis sur vos achats", Points = "50" },
        new EarnWay { Icon = "bi bi-people", Title = "Parrainage", Description = "Invitez vos amis sur NafaPlace", Points = "500" },
        new EarnWay { Icon = "bi bi-calendar-check", Title = "Connexion Quotidienne", Description = "Connectez-vous chaque jour", Points = "10" },
        new EarnWay { Icon = "bi bi-share", Title = "Partage Social", Description = "Partagez des produits sur les réseaux", Points = "25" },
        new EarnWay { Icon = "bi bi-gift", Title = "Anniversaire", Description = "Points bonus le jour de votre anniversaire", Points = "200" }
    };

    // Available rewards
    private List<Reward> availableRewards = new()
    {
        new Reward { Icon = "bi bi-percent", Title = "Réduction 5%", Description = "5% de réduction sur votre prochaine commande", PointsCost = 500 },
        new Reward { Icon = "bi bi-truck", Title = "Livraison Gratuite", Description = "Livraison gratuite pour une commande", PointsCost = 300 },
        new Reward { Icon = "bi bi-gift", Title = "Produit Gratuit", Description = "Produit mystère offert", PointsCost = 1000 },
        new Reward { Icon = "bi bi-percent", Title = "Réduction 10%", Description = "10% de réduction sur votre prochaine commande", PointsCost = 800 },
        new Reward { Icon = "bi bi-star", Title = "Accès VIP", Description = "Accès anticipé aux nouvelles collections", PointsCost = 1500 },
        new Reward { Icon = "bi bi-cash", Title = "Bon d'Achat 50.000 GNF", Description = "Bon d'achat utilisable sur tout le site", PointsCost = 2000 }
    };

    private async Task ShowRewards()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Section récompenses - Fonctionnalité complète bientôt disponible !");
    }

    private async Task ShowChallenges()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Défis du mois - Fonctionnalité complète bientôt disponible !");
    }

    private async Task ShowReferral()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Programme de parrainage - Fonctionnalité complète bientôt disponible !");
    }

    private async Task ShowHistory()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Historique des points - Fonctionnalité complète bientôt disponible !");
    }

    private async Task RedeemReward(Reward reward)
    {
        if (currentPoints >= reward.PointsCost)
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"Échanger {reward.PointsCost} points contre '{reward.Title}' ?");
            if (confirmed)
            {
                currentPoints -= reward.PointsCost;
                totalRedeemed += reward.PointsCost;
                StateHasChanged();
                await JSRuntime.InvokeVoidAsync("alert", $"Récompense '{reward.Title}' échangée avec succès !");
            }
        }
    }

    public class LoyaltyTier
    {
        public string Name { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Description { get; set; } = "";
        public int MinPoints { get; set; }
        public string[] Benefits { get; set; } = Array.Empty<string>();
    }

    public class EarnWay
    {
        public string Icon { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Points { get; set; } = "";
    }

    public class Reward
    {
        public string Icon { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public int PointsCost { get; set; }
    }
}
