/* ===== SYSTÈME DE DESIGN PROFESSIONNEL NAFAPLACE ===== */

/* Variables pour un design system moderne et professionnel */
:root {
    /* Palette de couleurs professionnelle */
    --brand-primary: #E73C30;
    --brand-secondary: #F96302;
    --brand-dark: #003366;
    --brand-light: #f8f9fa;

    /* Dégradés professionnels subtils */
    --gradient-hero: linear-gradient(135deg, #003366 0%, #0056b3 50%, #007bff 100%);
    --gradient-primary: linear-gradient(135deg, #E73C30 0%, #F96302 100%);
    --gradient-neutral: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    --gradient-dark: linear-gradient(135deg, #212529 0%, #495057 100%);

    /* Dégradés avec overlay pour meilleure lisibilité */
    --gradient-overlay-dark: linear-gradient(135deg, rgba(0, 51, 102, 0.95) 0%, rgba(0, 86, 179, 0.95) 100%);
    --gradient-overlay-primary: linear-gradient(135deg, rgba(231, 60, 48, 0.95) 0%, rgba(249, 99, 2, 0.95) 100%);

    /* Couleurs de texte optimisées */
    --text-on-dark: #ffffff;
    --text-on-dark-secondary: rgba(255, 255, 255, 0.85);
    --text-on-dark-muted: rgba(255, 255, 255, 0.65);
    --text-on-light: #212529;
    --text-on-light-secondary: #6c757d;

    /* Ombres professionnelles */
    --shadow-text-strong: 0 2px 8px rgba(0, 0, 0, 0.4);
    --shadow-text-medium: 0 1px 4px rgba(0, 0, 0, 0.25);
    --shadow-text-light: 0 1px 2px rgba(0, 0, 0, 0.15);
    --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.08);
    --shadow-card-hover: 0 8px 30px rgba(0, 0, 0, 0.12);

    /* Espacements cohérents */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;

    /* Rayons de bordure */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 20px;
    --radius-xl: 30px;
}

/* ===== COMPOSANTS PROFESSIONNELS ===== */

/* Header professionnel avec dégradé */
.professional-header {
    background: var(--gradient-overlay-dark);
    color: var(--text-on-dark);
    padding: var(--spacing-xxl) 0;
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    box-shadow: var(--shadow-card);
}

.professional-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="professional-dots" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="white" opacity="0.08"/></pattern></defs><rect width="100" height="100" fill="url(%23professional-dots)"/></svg>');
    pointer-events: none;
}

.professional-header .header-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.professional-header .header-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-on-dark);
    margin-bottom: var(--spacing-md);
    text-shadow: var(--shadow-text-strong);
    letter-spacing: -0.02em;
}

.professional-header .header-subtitle {
    font-size: 1.25rem;
    color: var(--text-on-dark-secondary);
    margin-bottom: 0;
    text-shadow: var(--shadow-text-medium);
    font-weight: 400;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.professional-header .header-icon {
    font-size: 3.5rem;
    color: var(--brand-secondary);
    margin-bottom: var(--spacing-md);
    text-shadow: var(--shadow-text-medium);
}

/* Cards professionnelles */
.professional-card {
    background: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-card);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    overflow: hidden;
}

.professional-card:hover {
    box-shadow: var(--shadow-card-hover);
    transform: translateY(-2px);
}

.professional-card .card-header {
    background: var(--gradient-neutral);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    padding: var(--spacing-md) var(--spacing-lg);
}

.professional-card .card-body {
    padding: var(--spacing-lg);
}

/* Section statistiques professionnelle */
.professional-stats {
    background: white;
    border-radius: var(--radius-md);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-card);
    margin-bottom: var(--spacing-xl);
}

.professional-stats .stat-item {
    text-align: center;
    padding: var(--spacing-md);
}

.professional-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--brand-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.professional-stats .stat-label {
    color: var(--text-on-light-secondary);
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Article en vedette professionnel */
.professional-featured-article {
    background: var(--gradient-overlay-dark);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xxl);
    box-shadow: var(--shadow-card-hover);
    color: var(--text-on-dark);
    margin-bottom: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    max-width: 100%;
}

.professional-featured-article::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="article-pattern" width="80" height="80" patternUnits="userSpaceOnUse"><circle cx="40" cy="40" r="1.5" fill="white" opacity="0.06"/><circle cx="20" cy="20" r="1" fill="white" opacity="0.04"/><circle cx="60" cy="60" r="1" fill="white" opacity="0.04"/></pattern></defs><rect width="100" height="100" fill="url(%23article-pattern)"/></svg>');
    pointer-events: none;
}

.professional-featured-article .article-content {
    position: relative;
    z-index: 2;
}

.professional-featured-article .article-badge {
    display: inline-block;
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-md);
}

.professional-featured-article .article-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-on-dark);
    margin-bottom: var(--spacing-lg);
    text-shadow: var(--shadow-text-strong);
    line-height: 1.2;
    letter-spacing: -0.02em;
}

.professional-featured-article .article-excerpt {
    font-size: 1.2rem;
    color: var(--text-on-dark-secondary);
    margin-bottom: var(--spacing-lg);
    text-shadow: var(--shadow-text-medium);
    line-height: 1.7;
    max-width: 80%;
}

.professional-featured-article .article-meta {
    color: var(--text-on-dark-muted);
    text-shadow: var(--shadow-text-light);
    font-size: 0.95rem;
}

/* Section newsletter professionnelle */
.professional-newsletter {
    background: var(--gradient-overlay-primary);
    color: var(--text-on-dark);
    border-radius: var(--radius-md);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    margin: var(--spacing-xl) 0;
}

.professional-newsletter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="newsletter-pro" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="1.5" fill="white" opacity="0.08"/><circle cx="15" cy="45" r="1" fill="white" opacity="0.05"/><circle cx="45" cy="15" r="1" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23newsletter-pro)"/></svg>');
    pointer-events: none;
}

.professional-newsletter .newsletter-content {
    position: relative;
    z-index: 2;
}

.professional-newsletter .newsletter-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-on-dark);
    margin-bottom: var(--spacing-sm);
    text-shadow: var(--shadow-text-medium);
}

.professional-newsletter .newsletter-description {
    font-size: 1.1rem;
    color: var(--text-on-dark-secondary);
    margin-bottom: var(--spacing-lg);
    text-shadow: var(--shadow-text-light);
    line-height: 1.6;
}

.professional-newsletter .newsletter-form {
    position: relative;
    z-index: 2;
}

.professional-newsletter .form-control {
    border: 2px solid rgba(255, 255, 255, 0.25);
    background-color: rgba(255, 255, 255, 0.15);
    color: var(--text-on-dark);
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.professional-newsletter .form-control::placeholder {
    color: var(--text-on-dark-muted);
}

.professional-newsletter .form-control:focus {
    border-color: rgba(255, 255, 255, 0.6);
    background-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.2);
    outline: none;
}

.professional-newsletter .btn-newsletter {
    background: white;
    color: var(--brand-primary);
    border: 2px solid white;
    font-weight: 600;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
}

.professional-newsletter .btn-newsletter:hover {
    background: transparent;
    color: white;
    border-color: white;
    transform: translateY(-1px);
}

/* Section de contact professionnelle */
.professional-contact-header {
    background: var(--gradient-overlay-dark);
    color: var(--text-on-dark);
    padding: var(--spacing-xxl) 0;
    margin-bottom: var(--spacing-xl);
    border-radius: var(--radius-lg);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.professional-contact-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80"><defs><pattern id="contact-pro" width="80" height="80" patternUnits="userSpaceOnUse"><circle cx="40" cy="40" r="2" fill="white" opacity="0.06"/><circle cx="20" cy="20" r="1" fill="white" opacity="0.04"/><circle cx="60" cy="60" r="1" fill="white" opacity="0.04"/><circle cx="20" cy="60" r="1.5" fill="white" opacity="0.05"/><circle cx="60" cy="20" r="1.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23contact-pro)"/></svg>');
    pointer-events: none;
}

.professional-contact-header .header-content {
    position: relative;
    z-index: 2;
}

.professional-contact-header .contact-icon {
    font-size: 4rem;
    color: var(--brand-secondary);
    margin-bottom: var(--spacing-md);
    text-shadow: var(--shadow-text-medium);
}

.professional-contact-header .contact-title {
    font-size: 2.75rem;
    font-weight: 700;
    color: var(--text-on-dark);
    margin-bottom: var(--spacing-md);
    text-shadow: var(--shadow-text-strong);
    letter-spacing: -0.02em;
}

.professional-contact-header .contact-subtitle {
    font-size: 1.3rem;
    color: var(--text-on-dark-secondary);
    margin-bottom: 0;
    text-shadow: var(--shadow-text-medium);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* ===== RESPONSIVE PROFESSIONNEL ===== */
@media (max-width: 992px) {
    .professional-header .header-title {
        font-size: 2.5rem;
    }

    .professional-featured-article .article-title {
        font-size: 2.2rem;
    }

    .professional-featured-article .article-excerpt {
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .professional-header {
        padding: var(--spacing-xl) 0;
    }

    .professional-header .header-title {
        font-size: 2rem;
    }

    .professional-header .header-subtitle {
        font-size: 1.1rem;
    }

    .professional-featured-article {
        padding: var(--spacing-xl);
    }

    .professional-featured-article .article-title {
        font-size: 1.9rem;
    }

    .professional-featured-article .article-excerpt {
        font-size: 1.1rem;
    }

    .professional-contact-header {
        padding: var(--spacing-xl) 0;
    }

    .professional-contact-header .contact-title {
        font-size: 2.2rem;
    }

    .professional-newsletter {
        padding: var(--spacing-lg);
    }

    .professional-stats .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .professional-header .header-title {
        font-size: 1.75rem;
    }

    .professional-header .header-icon {
        font-size: 2.5rem;
    }

    .professional-featured-article {
        padding: var(--spacing-lg);
    }

    .professional-featured-article .article-title {
        font-size: 1.6rem;
    }

    .professional-contact-header .contact-title {
        font-size: 1.9rem;
    }

    .professional-contact-header .contact-icon {
        font-size: 3rem;
    }

    .professional-newsletter .newsletter-title {
        font-size: 1.5rem;
    }

    .professional-stats {
        padding: var(--spacing-lg);
    }

    .professional-stats .stat-number {
        font-size: 1.8rem;
    }
}

/* ===== UTILITAIRES PROFESSIONNELS ===== */
.text-shadow-strong { text-shadow: var(--shadow-text-strong); }
.text-shadow-medium { text-shadow: var(--shadow-text-medium); }
.text-shadow-light { text-shadow: var(--shadow-text-light); }

.bg-gradient-hero { background: var(--gradient-hero); }
.bg-gradient-primary { background: var(--gradient-primary); }
.bg-gradient-overlay-dark { background: var(--gradient-overlay-dark); }
.bg-gradient-overlay-primary { background: var(--gradient-overlay-primary); }
