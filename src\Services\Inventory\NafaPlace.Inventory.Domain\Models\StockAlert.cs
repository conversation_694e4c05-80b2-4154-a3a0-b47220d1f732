using System.ComponentModel.DataAnnotations;
using NafaPlace.Inventory.Domain.Common;
using NafaPlace.Inventory.Domain.Enums;

namespace NafaPlace.Inventory.Domain.Models;

public class StockAlert : BaseEntity
{
    [Required]
    public int ProductId { get; set; }

    [Required]
    [MaxLength(100)]
    public required string ProductName { get; set; }

    [Required]
    public AlertType Type { get; set; }

    [Required]
    public AlertSeverity Severity { get; set; }

    [Required]
    [MaxLength(500)]
    public required string Message { get; set; }

    [Required]
    public int CurrentStock { get; set; }

    public int? ThresholdValue { get; set; }

    [Required]
    public bool IsActive { get; set; } = true;

    public bool IsAcknowledged { get; set; } = false;

    [MaxLength(50)]
    public string? AcknowledgedBy { get; set; }

    public DateTime? AcknowledgedAt { get; set; }

    [Required]
    public int SellerId { get; set; }

    [MaxLength(100)]
    public string? SellerName { get; set; }

    // Navigation properties
    public virtual List<StockAlertNotification> Notifications { get; set; } = new();
}
