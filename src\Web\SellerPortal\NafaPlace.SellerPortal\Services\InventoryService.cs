using System.Text;
using System.Text.Json;
using NafaPlace.SellerPortal.Models.Inventory;

namespace NafaPlace.SellerPortal.Services;

public class InventoryService
{
    private readonly HttpClient _inventoryHttpClient;
    private readonly HttpClient _catalogHttpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public InventoryService(IHttpClientFactory httpClientFactory)
    {
        _inventoryHttpClient = httpClientFactory.CreateClient("InventoryApi");
        _catalogHttpClient = httpClientFactory.CreateClient("CatalogApi");
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    // Dashboard
    public async Task<InventoryDashboardDto> GetInventoryDashboardAsync(int sellerId)
    {
        try
        {
            var response = await _inventoryHttpClient.GetAsync($"api/v1/inventory/dashboard?sellerId={sellerId}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<InventoryDashboardDto>(json, _jsonOptions) ?? new InventoryDashboardDto();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération du dashboard inventaire: {ex.Message}");
        }

        return new InventoryDashboardDto();
    }

    // Dashboard global (comme dans l'admin portal)
    public async Task<InventoryDashboardDto> GetInventoryDashboardAsync()
    {
        try
        {
            var response = await _inventoryHttpClient.GetAsync("api/v1/inventory/dashboard");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<InventoryDashboardDto>(json, _jsonOptions) ?? new InventoryDashboardDto();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération du dashboard inventaire global: {ex.Message}");
        }

        return new InventoryDashboardDto();
    }

    // Stock Alerts
    public async Task<List<StockAlertDto>> GetSellerAlertsAsync(int sellerId)
    {
        try
        {
            var response = await _inventoryHttpClient.GetAsync($"api/v1/inventory/alerts?sellerId={sellerId}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<StockAlertDto>>(json, _jsonOptions) ?? new List<StockAlertDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des alertes: {ex.Message}");
        }

        return new List<StockAlertDto>();
    }

    public async Task<bool> AcknowledgeAlertAsync(int alertId)
    {
        try
        {
            var response = await _inventoryHttpClient.PutAsync($"api/v1/inventory/alerts/{alertId}/acknowledge", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'acquittement de l'alerte: {ex.Message}");
            return false;
        }
    }

    // Stock Management
    public async Task<bool> UpdateStockAsync(int productId, UpdateStockRequest request)
    {
        try
        {
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _inventoryHttpClient.PutAsync($"api/v1/inventory/products/{productId}/stock", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour du stock: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> AdjustStockAsync(int productId, StockAdjustmentRequest request)
    {
        try
        {
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _inventoryHttpClient.PostAsync($"api/v1/inventory/products/{productId}/adjust", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'ajustement du stock: {ex.Message}");
            return false;
        }
    }

    // Stock Movements
    public async Task<List<StockMovementDto>> GetSellerMovementsAsync(int sellerId, int page = 1, int pageSize = 20)
    {
        try
        {
            var response = await _inventoryHttpClient.GetAsync($"api/v1/inventory/sellers/{sellerId}/movements?page={page}&pageSize={pageSize}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<StockMovementDto>>(json, _jsonOptions) ?? new List<StockMovementDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des mouvements: {ex.Message}");
        }

        return new List<StockMovementDto>();
    }

    public async Task<List<StockMovementDto>> GetProductMovementsAsync(int productId, int page = 1, int pageSize = 20)
    {
        try
        {
            var response = await _inventoryHttpClient.GetAsync($"api/v1/inventory/products/{productId}/movements?page={page}&pageSize={pageSize}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<StockMovementDto>>(json, _jsonOptions) ?? new List<StockMovementDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des mouvements du produit: {ex.Message}");
        }

        return new List<StockMovementDto>();
    }

    // Stock Validation
    public async Task<StockValidationResult> ValidateStockAvailabilityAsync(int productId, int quantity)
    {
        try
        {
            var response = await _inventoryHttpClient.GetAsync($"api/v1/inventory/products/{productId}/validate?quantity={quantity}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<StockValidationResult>(json, _jsonOptions) ?? new StockValidationResult();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la validation du stock: {ex.Message}");
        }

        return new StockValidationResult { IsValid = false, ErrorMessage = "Erreur de validation" };
    }

    public async Task<int> GetAvailableStockAsync(int productId)
    {
        try
        {
            var response = await _inventoryHttpClient.GetAsync($"api/v1/inventory/products/{productId}/available-stock");

            if (response.IsSuccessStatusCode)
            {
                var stockText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(stockText, out var stock))
                {
                    return stock;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération du stock disponible: {ex.Message}");
        }

        return 0;
    }

    // Analytics
    public async Task<List<TopProductDto>> GetTopSellingProductsAsync(int sellerId, int count = 10)
    {
        try
        {
            var response = await _inventoryHttpClient.GetAsync($"api/v1/inventory/analytics/top-selling?sellerId={sellerId}&count={count}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<TopProductDto>>(json, _jsonOptions) ?? new List<TopProductDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des produits les plus vendus: {ex.Message}");
        }

        return new List<TopProductDto>();
    }

    public async Task<List<TopProductDto>> GetLowStockProductsAsync(int sellerId, int threshold = 10)
    {
        try
        {
            var response = await _inventoryHttpClient.GetAsync($"api/v1/inventory/analytics/low-stock?sellerId={sellerId}&threshold={threshold}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<TopProductDto>>(json, _jsonOptions) ?? new List<TopProductDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des produits en stock faible: {ex.Message}");
        }

        return new List<TopProductDto>();
    }

    // Bulk Stock Update
    public async Task<BulkUpdateResult> BulkUpdateStockAsync(BulkStockUpdateRequest request)
    {
        try
        {
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _inventoryHttpClient.PostAsync("api/v1/inventory/bulk-update", content);

            if (response.IsSuccessStatusCode)
            {
                return new BulkUpdateResult
                {
                    Success = true,
                    TotalItems = request.Items.Count,
                    SuccessfulUpdates = request.Items.Count,
                    FailedUpdates = 0,
                    Message = "Mise à jour en lot réussie"
                };
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                return new BulkUpdateResult
                {
                    Success = false,
                    TotalItems = request.Items.Count,
                    SuccessfulUpdates = 0,
                    FailedUpdates = request.Items.Count,
                    Errors = new List<string> { errorContent },
                    Message = "Échec de la mise à jour en lot"
                };
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour en lot: {ex.Message}");
            return new BulkUpdateResult
            {
                Success = false,
                TotalItems = request.Items.Count,
                SuccessfulUpdates = 0,
                FailedUpdates = request.Items.Count,
                Errors = new List<string> { ex.Message },
                Message = "Erreur lors de la mise à jour en lot"
            };
        }
    }

    // Get Seller Products for Bulk Update
    public async Task<List<ProductInfo>> GetSellerProductsAsync(int sellerId, int page = 1, int pageSize = 50)
    {
        try
        {
            // Utiliser l'endpoint du service Catalog pour récupérer les produits du vendeur
            var response = await _catalogHttpClient.GetAsync($"api/v1/catalog/products?sellerId={sellerId}&page={page}&pageSize={pageSize}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var pagedResult = JsonSerializer.Deserialize<PagedResultDto<ProductDto>>(json, _jsonOptions);

                if (pagedResult?.Items != null)
                {
                    var products = pagedResult.Items.Select(p => new ProductInfo
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Price = p.Price,
                        SellerId = p.SellerId,
                        SellerName = p.SellerName ?? "",
                        CurrentStock = p.StockQuantity,
                        Currency = p.Currency,
                        ImageUrl = p.Images?.FirstOrDefault()?.Url,
                        NewStock = p.StockQuantity,
                        IsSelected = false
                    }).ToList();

                    return products;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des produits du vendeur: {ex.Message}");
        }

        return new List<ProductInfo>();
    }

    // DTOs pour la désérialisation
    public class PagedResultDto<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class ProductDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public int SellerId { get; set; }
        public string? SellerName { get; set; }
        public int StockQuantity { get; set; }
        public string Currency { get; set; } = "GNF";
        public List<ProductImageDto>? Images { get; set; }
    }

    public class ProductImageDto
    {
        public string Url { get; set; } = string.Empty;
        public bool IsPrimary { get; set; }
    }

    // Méthodes supplémentaires pour les nouvelles fonctionnalités
    public async Task<List<StockMovementDto>> GetSellerMovementsWithFiltersAsync(int sellerId, string? movementType = null, int? periodDays = null, string? searchTerm = null, int page = 1, int pageSize = 20)
    {
        try
        {
            var queryParams = new List<string>
            {
                $"sellerId={sellerId}",
                $"page={page}",
                $"pageSize={pageSize}"
            };

            if (!string.IsNullOrEmpty(movementType) && movementType != "all")
            {
                queryParams.Add($"type={movementType}");
            }

            if (periodDays.HasValue)
            {
                queryParams.Add($"periodDays={periodDays.Value}");
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                queryParams.Add($"search={Uri.EscapeDataString(searchTerm)}");
            }

            var queryString = string.Join("&", queryParams);
            var response = await _inventoryHttpClient.GetAsync($"api/v1/inventory/sellers/{sellerId}/movements?{queryString}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<StockMovementDto>>(json, _jsonOptions) ?? new List<StockMovementDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des mouvements filtrés: {ex.Message}");
        }

        return new List<StockMovementDto>();
    }

    public async Task<Dictionary<string, object>> GetExportDataAsync(int sellerId, bool includeProducts, bool includeMovements, bool includeAlerts, int periodDays = 30)
    {
        var exportData = new Dictionary<string, object>();

        try
        {
            if (includeProducts)
            {
                var products = await GetSellerProductsAsync(sellerId, 1, 1000);
                exportData["products"] = products;
            }

            if (includeMovements)
            {
                var movements = await GetSellerMovementsWithFiltersAsync(sellerId, null, periodDays, null, 1, 1000);
                exportData["movements"] = movements;
            }

            if (includeAlerts)
            {
                var alerts = await GetSellerAlertsAsync(sellerId);
                exportData["alerts"] = alerts;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des données d'export: {ex.Message}");
        }

        return exportData;
    }

    public async Task<bool> SetStockThresholdAsync(int productId, int threshold)
    {
        try
        {
            var request = new { Threshold = threshold };
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _inventoryHttpClient.PutAsync($"api/v1/inventory/products/{productId}/threshold", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la définition du seuil: {ex.Message}");
            return false;
        }
    }

    public async Task<List<StockAlertDto>> GetActiveAlertsAsync(int sellerId)
    {
        try
        {
            var response = await _inventoryHttpClient.GetAsync($"api/v1/inventory/alerts?sellerId={sellerId}&active=true");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<StockAlertDto>>(json, _jsonOptions) ?? new List<StockAlertDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des alertes actives: {ex.Message}");
        }

        return new List<StockAlertDto>();
    }

    public async Task<bool> BulkAcknowledgeAlertsAsync(List<int> alertIds)
    {
        try
        {
            var request = new { AlertIds = alertIds };
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _inventoryHttpClient.PutAsync("api/v1/inventory/alerts/bulk-acknowledge", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'acquittement en lot des alertes: {ex.Message}");
            return false;
        }
    }
}
