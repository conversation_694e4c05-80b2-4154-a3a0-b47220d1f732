using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Inventory.Application.Services;

namespace NafaPlace.Inventory.API.Controllers;

[ApiController]
[Route("api/v1/inventory/[controller]")]
[Authorize(Roles = "Admin,System")]
public class MaintenanceController : ControllerBase
{
    private readonly IInventoryMaintenanceService _maintenanceService;
    private readonly ILogger<MaintenanceController> _logger;

    public MaintenanceController(
        IInventoryMaintenanceService maintenanceService,
        ILogger<MaintenanceController> logger)
    {
        _maintenanceService = maintenanceService;
        _logger = logger;
    }

    /// <summary>
    /// Nettoie les réservations expirées
    /// </summary>
    [HttpPost("cleanup-expired-reservations")]
    public async Task<IActionResult> CleanupExpiredReservations()
    {
        try
        {
            await _maintenanceService.CleanupExpiredReservationsAsync();
            return Ok(new { message = "Expired reservations cleanup completed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during expired reservations cleanup");
            return StatusCode(500, new { error = "Internal server error during cleanup" });
        }
    }

    /// <summary>
    /// Recalcule les niveaux de stock
    /// </summary>
    [HttpPost("recalculate-stock-levels")]
    public async Task<IActionResult> RecalculateStockLevels()
    {
        try
        {
            await _maintenanceService.RecalculateStockLevelsAsync();
            return Ok(new { message = "Stock levels recalculation completed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during stock levels recalculation");
            return StatusCode(500, new { error = "Internal server error during recalculation" });
        }
    }

    /// <summary>
    /// Traite les alertes en attente
    /// </summary>
    [HttpPost("process-pending-alerts")]
    public async Task<IActionResult> ProcessPendingAlerts()
    {
        try
        {
            await _maintenanceService.ProcessPendingAlertsAsync();
            return Ok(new { message = "Pending alerts processing completed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during pending alerts processing");
            return StatusCode(500, new { error = "Internal server error during alerts processing" });
        }
    }

    /// <summary>
    /// Archive les anciens mouvements de stock
    /// </summary>
    [HttpPost("archive-old-movements")]
    public async Task<IActionResult> ArchiveOldMovements([FromQuery] int daysToKeep = 90)
    {
        try
        {
            await _maintenanceService.ArchiveOldMovementsAsync(daysToKeep);
            return Ok(new { message = $"Old movements archival completed successfully (keeping {daysToKeep} days)" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during old movements archival");
            return StatusCode(500, new { error = "Internal server error during archival" });
        }
    }

    /// <summary>
    /// Exécute toutes les tâches de maintenance
    /// </summary>
    [HttpPost("run-all")]
    public async Task<IActionResult> RunAllMaintenanceTasks()
    {
        try
        {
            _logger.LogInformation("Starting manual execution of all maintenance tasks");

            await _maintenanceService.CleanupExpiredReservationsAsync();
            await _maintenanceService.RecalculateStockLevelsAsync();
            await _maintenanceService.ProcessPendingAlertsAsync();
            await _maintenanceService.ArchiveOldMovementsAsync();

            return Ok(new { message = "All maintenance tasks completed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during maintenance tasks execution");
            return StatusCode(500, new { error = "Internal server error during maintenance tasks" });
        }
    }

    /// <summary>
    /// Obtient le statut des tâches de maintenance
    /// </summary>
    [HttpGet("status")]
    public IActionResult GetMaintenanceStatus()
    {
        try
        {
            var status = new
            {
                lastRun = DateTime.UtcNow, // Pour l'instant, valeur simulée
                nextRun = DateTime.UtcNow.AddHours(1),
                isRunning = false,
                tasksEnabled = new
                {
                    cleanupExpiredReservations = true,
                    recalculateStockLevels = true,
                    processPendingAlerts = true,
                    archiveOldMovements = true
                }
            };

            return Ok(status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting maintenance status");
            return StatusCode(500, new { error = "Internal server error" });
        }
    }
}
