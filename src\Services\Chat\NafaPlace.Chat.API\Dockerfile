# Utiliser l'image de base .NET 9
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Utiliser l'image SDK pour la construction
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copier les fichiers de projet et restaurer les dépendances
COPY ["src/Services/Chat/NafaPlace.Chat.API/NafaPlace.Chat.API.csproj", "src/Services/Chat/NafaPlace.Chat.API/"]
COPY ["src/Services/Chat/NafaPlace.Chat.Application/NafaPlace.Chat.Application.csproj", "src/Services/Chat/NafaPlace.Chat.Application/"]
COPY ["src/Services/Chat/NafaPlace.Chat.Domain/NafaPlace.Chat.Domain.csproj", "src/Services/Chat/NafaPlace.Chat.Domain/"]
COPY ["src/Services/Chat/NafaPlace.Chat.Infrastructure/NafaPlace.Chat.Infrastructure.csproj", "src/Services/Chat/NafaPlace.Chat.Infrastructure/"]

RUN dotnet restore "src/Services/Chat/NafaPlace.Chat.API/NafaPlace.Chat.API.csproj"

# Copier tout le code source
COPY . .

# Construire l'application
WORKDIR "/src/src/Services/Chat/NafaPlace.Chat.API"
RUN dotnet build "NafaPlace.Chat.API.csproj" -c Release -o /app/build

# Publier l'application
FROM build AS publish
RUN dotnet publish "NafaPlace.Chat.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Image finale
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Créer un utilisateur non-root pour la sécurité
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

ENTRYPOINT ["dotnet", "NafaPlace.Chat.API.dll"]
