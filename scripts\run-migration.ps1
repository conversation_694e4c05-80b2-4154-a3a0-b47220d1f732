# Script PowerShell pour exécuter la migration des nouvelles fonctionnalités
# Exécuter après le démarrage de Docker

Write-Host "🔧 Migration des Nouvelles Fonctionnalités NafaPlace" -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Green

# Configuration de la base de données
$DbHost = "localhost"
$DbPort = "5432"
$DbName = "nafaplace"
$DbUser = "postgres"
$DbPassword = "postgres"

# Chemin vers le script SQL
$SqlScript = "scripts/add-new-features-tables.sql"

Write-Host "`n📋 Vérification des prérequis..." -ForegroundColor Cyan

# Vérifier que Docker est en cours d'exécution
try {
    $dockerStatus = docker ps 2>$null
    Write-Host "✅ Docker est en cours d'exécution" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker n'est pas en cours d'exécution. Veuillez démarrer Docker d'abord." -ForegroundColor Red
    exit 1
}

# Vérifier que le fichier SQL existe
if (Test-Path $SqlScript) {
    Write-Host "✅ Script SQL trouvé: $SqlScript" -ForegroundColor Green
} else {
    Write-Host "❌ Script SQL non trouvé: $SqlScript" -ForegroundColor Red
    exit 1
}

Write-Host "`n🔄 Attente du démarrage de PostgreSQL..." -ForegroundColor Cyan

# Attendre que PostgreSQL soit prêt
$maxAttempts = 30
$attempt = 0
$pgReady = $false

while ($attempt -lt $maxAttempts -and -not $pgReady) {
    try {
        # Tester la connexion à PostgreSQL
        $testConnection = docker exec nafaplace-postgres-1 pg_isready -h localhost -p 5432 2>$null
        if ($LASTEXITCODE -eq 0) {
            $pgReady = $true
            Write-Host "✅ PostgreSQL est prêt" -ForegroundColor Green
        } else {
            $attempt++
            Write-Host "⏳ Tentative $attempt/$maxAttempts - PostgreSQL n'est pas encore prêt..." -ForegroundColor Yellow
            Start-Sleep -Seconds 2
        }
    } catch {
        $attempt++
        Write-Host "⏳ Tentative $attempt/$maxAttempts - En attente de PostgreSQL..." -ForegroundColor Yellow
        Start-Sleep -Seconds 2
    }
}

if (-not $pgReady) {
    Write-Host "❌ PostgreSQL n'est pas prêt après $maxAttempts tentatives" -ForegroundColor Red
    exit 1
}

Write-Host "`n📊 Exécution de la migration..." -ForegroundColor Cyan

try {
    # Exécuter le script SQL via Docker
    $migrationResult = docker exec -i nafaplace-postgres-1 psql -U $DbUser -d $DbName -f /dev/stdin < $SqlScript 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Migration exécutée avec succès !" -ForegroundColor Green
        Write-Host $migrationResult -ForegroundColor Gray
    } else {
        Write-Host "❌ Erreur lors de la migration:" -ForegroundColor Red
        Write-Host $migrationResult -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Erreur lors de l'exécution de la migration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n🔍 Vérification des tables créées..." -ForegroundColor Cyan

# Vérifier que les nouvelles tables ont été créées
$verificationQueries = @(
    "SELECT COUNT(*) as coupon_count FROM ""Coupons""",
    "SELECT COUNT(*) as template_count FROM ""NotificationTemplates""",
    "SELECT COUNT(*) as zone_count FROM ""DeliveryZones""",
    "SELECT COUNT(*) as carrier_count FROM ""Carriers"""
)

foreach ($query in $verificationQueries) {
    try {
        $result = docker exec nafaplace-postgres-1 psql -U $DbUser -d $DbName -t -c "$query" 2>$null
        if ($LASTEXITCODE -eq 0) {
            $tableName = $query.Split('_')[0].Split(' ')[-1]
            Write-Host "✅ $tableName : $($result.Trim()) enregistrements" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️ Impossible de vérifier une table" -ForegroundColor Yellow
    }
}

Write-Host "`n🎯 Données de test insérées:" -ForegroundColor Cyan
Write-Host "- 3 coupons de test (WELCOME10, FREESHIP, SAVE50K)"
Write-Host "- 3 templates de notifications"
Write-Host "- 3 zones de livraison (Conakry, Kaloum, Kankan)"
Write-Host "- 3 transporteurs (NafaPlace Express, Guinea Post, Express Guinée)"

Write-Host "`n🚀 Migration terminée ! Vous pouvez maintenant tester les nouvelles fonctionnalités." -ForegroundColor Green

Write-Host "`n📝 Prochaines étapes:" -ForegroundColor Cyan
Write-Host "1. Exécuter: .\scripts\test-new-features.ps1"
Write-Host "2. Ouvrir: http://localhost:8080 (Main Web)"
Write-Host "3. Ouvrir: http://localhost:8081 (Admin Portal)"
Write-Host "4. Ouvrir: http://localhost:8082 (Seller Portal)"
Write-Host "5. Suivre le guide: docs\MANUAL_TESTING_GUIDE.md"

Write-Host "`nMigration terminée avec succès ! 🎉" -ForegroundColor Green
