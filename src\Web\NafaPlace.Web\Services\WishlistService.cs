using System.Net.Http.Json;
using System.Text.Json;
using NafaPlace.Web.Models.Wishlist;
using Microsoft.AspNetCore.Components.Authorization;

namespace NafaPlace.Web.Services;

public class WishlistService : IWishlistService
{
    private readonly IAuthenticatedHttpClientService _httpClientService;
    private readonly ILogger<WishlistService> _logger;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly string _baseAddress = "http://localhost:5008"; // URL par défaut

    public WishlistService(
        IAuthenticatedHttpClientService httpClientService,
        ILogger<WishlistService> logger,
        AuthenticationStateProvider authStateProvider)
    {
        _httpClientService = httpClientService;
        _logger = logger;
        _authStateProvider = authStateProvider;
    }

    private async Task<bool> IsUserAuthenticatedAsync()
    {
        try
        {
            var authState = await _authStateProvider.GetAuthenticationStateAsync();
            return authState.User.Identity?.IsAuthenticated == true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<WishlistDto> GetUserWishlistAsync(string userId)
    {
        try
        {
            // Vérifier l'authentification avant de faire l'appel API
            if (!await IsUserAuthenticatedAsync())
            {
                _logger.LogDebug("Utilisateur non authentifié, retour d'une wishlist vide");
                return new WishlistDto { UserId = userId };
            }

            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.GetAsync("api/wishlist");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var wishlist = JsonSerializer.Deserialize<WishlistDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return wishlist ?? new WishlistDto { UserId = userId };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user wishlist");
            return new WishlistDto { UserId = userId };
        }
    }

    public async Task<WishlistSummaryDto> GetWishlistSummaryAsync(string userId)
    {
        try
        {
            // Vérifier l'authentification avant de faire l'appel API
            if (!await IsUserAuthenticatedAsync())
            {
                _logger.LogDebug("Utilisateur non authentifié, retour d'un résumé de wishlist vide");
                return new WishlistSummaryDto { UserId = userId };
            }

            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.GetAsync("api/wishlist/summary");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var summary = JsonSerializer.Deserialize<WishlistSummaryDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return summary ?? new WishlistSummaryDto { UserId = userId };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist summary");
            return new WishlistSummaryDto { UserId = userId };
        }
    }

    public async Task<int> GetWishlistItemCountAsync(string userId)
    {
        try
        {
            // Vérifier l'authentification avant de faire l'appel API
            if (!await IsUserAuthenticatedAsync())
            {
                _logger.LogDebug("Utilisateur non authentifié, retour de 0 pour le compteur de wishlist");
                return 0;
            }

            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.GetAsync("api/wishlist/count");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<int>(content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist item count");
            return 0;
        }
    }

    public async Task<List<WishlistItemDto>> GetWishlistItemsAsync(string userId, int page = 1, int pageSize = 20)
    {
        try
        {
            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.GetAsync($"api/wishlist/items?page={page}&pageSize={pageSize}");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var items = JsonSerializer.Deserialize<List<WishlistItemDto>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return items ?? new List<WishlistItemDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist items");
            return new List<WishlistItemDto>();
        }
    }

    public async Task<WishlistItemDto> AddToWishlistAsync(string userId, AddToWishlistRequest request)
    {
        try
        {
            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.PostAsJsonAsync("api/wishlist/items", request);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var item = JsonSerializer.Deserialize<WishlistItemDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return item ?? throw new InvalidOperationException("Failed to add item to wishlist");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding item to wishlist");
            throw;
        }
    }

    public async Task<bool> RemoveFromWishlistAsync(string userId, int productId)
    {
        try
        {
            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.DeleteAsync($"api/wishlist/items/{productId}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing item from wishlist");
            return false;
        }
    }

    public async Task<bool> IsProductInWishlistAsync(string userId, int productId)
    {
        try
        {
            // Vérifier l'authentification avant de faire l'appel API
            if (!await IsUserAuthenticatedAsync())
            {
                _logger.LogDebug("Utilisateur non authentifié, produit {ProductId} pas dans la wishlist", productId);
                return false;
            }

            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.GetAsync($"api/wishlist/items/{productId}/exists");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<bool>(content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if product is in wishlist");
            return false;
        }
    }

    public async Task<WishlistItemDto?> GetWishlistItemAsync(string userId, int productId)
    {
        try
        {
            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.GetAsync($"api/wishlist/items/{productId}");
            if (!response.IsSuccessStatusCode)
                return null;

            var content = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<WishlistItemDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting wishlist item");
            return null;
        }
    }

    public async Task<bool> ClearWishlistAsync(string userId)
    {
        try
        {
            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.DeleteAsync("api/wishlist/clear");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing wishlist");
            return false;
        }
    }

    public async Task<bool> MoveToCartAsync(string userId, int productId)
    {
        try
        {
            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.PostAsync($"api/wishlist/items/{productId}/move-to-cart", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving item to cart");
            return false;
        }
    }

    public async Task<List<WishlistItemDto>> GetRecentlyAddedItemsAsync(string userId, int count = 5)
    {
        try
        {
            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.GetAsync($"api/wishlist/recent?count={count}");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var items = JsonSerializer.Deserialize<List<WishlistItemDto>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return items ?? new List<WishlistItemDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recently added items");
            return new List<WishlistItemDto>();
        }
    }

    public async Task<WishlistDto> GetGuestWishlistAsync(string guestId)
    {
        try
        {
            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.GetAsync($"api/wishlist/guest/{guestId}");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var wishlist = JsonSerializer.Deserialize<WishlistDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return wishlist ?? new WishlistDto { UserId = $"guest_{guestId}" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting guest wishlist");
            return new WishlistDto { UserId = $"guest_{guestId}" };
        }
    }

    public async Task<WishlistItemDto> AddToGuestWishlistAsync(string guestId, AddToWishlistRequest request)
    {
        try
        {
            var httpClient = await _httpClientService.GetAuthenticatedHttpClientAsync(_baseAddress);
            var response = await httpClient.PostAsJsonAsync($"api/wishlist/guest/{guestId}/items", request);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var item = JsonSerializer.Deserialize<WishlistItemDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return item ?? throw new InvalidOperationException("Failed to add item to guest wishlist");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding item to guest wishlist");
            throw;
        }
    }
}
