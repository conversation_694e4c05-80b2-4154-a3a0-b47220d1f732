namespace NafaPlace.Chat.Domain.Entities;

public enum ConversationStatus
{
    Open = 1,
    InProgress = 2,
    Waiting = 3,
    Resolved = 4,
    Closed = 5
}

public enum ConversationPriority
{
    Low = 1,
    Normal = 2,
    High = 3,
    Urgent = 4
}

public enum ConversationCategory
{
    General = 1,
    Technical = 2,
    Billing = 3,
    Sales = 4,
    Complaint = 5
}

public enum MessageType
{
    Text = 1,
    Image = 2,
    File = 3,
    System = 4
}

public enum SenderType
{
    User = 1,
    Agent = 2,
    System = 3,
    Bot = 4
}

public enum AgentStatus
{
    Offline = 1,
    Available = 2,
    Busy = 3,
    Away = 4
}

public class Conversation
{
    public int Id { get; set; }
    public string Subject { get; set; } = string.Empty;
    public ConversationStatus Status { get; set; }
    public ConversationPriority Priority { get; set; }
    public ConversationCategory Category { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string? AssignedAgentId { get; set; }
    public string? AssignedAgentName { get; set; }
    public int? DepartmentId { get; set; }
    public string Tags { get; set; } = "[]";
    public bool IsArchived { get; set; }
    public int? SatisfactionRating { get; set; }
    public string? SatisfactionComment { get; set; }
    public TimeSpan? FirstResponseTime { get; set; }
    public TimeSpan? ResolutionTime { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public DateTime? ClosedAt { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class Message
{
    public int Id { get; set; }
    public int ConversationId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType MessageType { get; set; }
    public SenderType SenderType { get; set; }
    public string SenderId { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public bool IsRead { get; set; }
    public DateTime? ReadAt { get; set; }
    public bool IsEdited { get; set; }
    public DateTime? EditedAt { get; set; }
    public int? ParentMessageId { get; set; }
    public string Attachments { get; set; } = "[]";
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class Agent
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? Avatar { get; set; }
    public AgentStatus Status { get; set; }
    public int? DepartmentId { get; set; }
    public int MaxConcurrentChats { get; set; } = 5;
    public int CurrentChatCount { get; set; }
    public string Skills { get; set; } = "[]";
    public string Languages { get; set; } = "[]";
    public string WorkingHours { get; set; } = "{}";
    public bool IsActive { get; set; } = true;
    public DateTime? LastActivity { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class Department
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? Email { get; set; }
    public bool IsActive { get; set; } = true;
    public string WorkingHours { get; set; } = "{}";
    public bool AutoAssignment { get; set; } = true;
    public int MaxQueueSize { get; set; } = 100;
    public int Priority { get; set; } = 1;
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public enum TicketStatus
{
    Open = 1,
    InProgress = 2,
    Waiting = 3,
    Resolved = 4,
    Closed = 5,
    Cancelled = 6
}

public enum TicketSource
{
    Web = 1,
    Email = 2,
    Phone = 3,
    Chat = 4,
    Mobile = 5
}

public class Ticket
{
    public int Id { get; set; }
    public string TicketNumber { get; set; } = string.Empty;
    public string Subject { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TicketStatus Status { get; set; }
    public ConversationPriority Priority { get; set; }
    public ConversationCategory Category { get; set; }
    public TicketSource Source { get; set; }
    public string UserId { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string? AssignedAgentId { get; set; }
    public string? AssignedAgentName { get; set; }
    public int? DepartmentId { get; set; }
    public int? ConversationId { get; set; }
    public string Tags { get; set; } = "[]";
    public DateTime? SlaDeadline { get; set; }
    public TimeSpan? FirstResponseTime { get; set; }
    public TimeSpan? ResolutionTime { get; set; }
    public int? SatisfactionRating { get; set; }
    public string? SatisfactionComment { get; set; }
    public DateTime? ClosedAt { get; set; }
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class FAQ
{
    public int Id { get; set; }
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public ConversationCategory Category { get; set; }
    public string Tags { get; set; } = "[]";
    public string Keywords { get; set; } = "[]";
    public int ViewCount { get; set; }
    public int HelpfulCount { get; set; }
    public int NotHelpfulCount { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsFeatured { get; set; }
    public int Priority { get; set; } = 1;
    public string Language { get; set; } = "fr";
    public string CreatedBy { get; set; } = string.Empty;
    public string Metadata { get; set; } = "{}";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class ChatbotInteraction
{
    public int Id { get; set; }
    public string SessionId { get; set; } = string.Empty;
    public string? UserId { get; set; }
    public string UserMessage { get; set; } = string.Empty;
    public string BotResponse { get; set; } = string.Empty;
    public string Intent { get; set; } = string.Empty;
    public double Confidence { get; set; }
    public string Entities { get; set; } = "[]";
    public bool WasHelpful { get; set; }
    public bool RequiredHandoff { get; set; }
    public string Context { get; set; } = "{}";
    public DateTime Timestamp { get; set; }
}
